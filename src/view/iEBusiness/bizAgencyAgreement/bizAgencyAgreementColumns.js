import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }
  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};
const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()
// 初始化时获取数据
await getMerchantOptions()
function getColumns() {
  const commColumns = reactive([
    'businessType'
    , 'id'
    , 'businessType'
    , 'contractNo'
    , 'agreementNo'
    , 'customer'
    , 'signingDate'
    , 'signingPlace'
    , 'currency'
    , 'contractAmount'
    , 'agencyRate'
    , 'agencyFee'
    , 'suggestedSignatory'
    , 'agreementTerms'
    , 'maker'
    , 'makeDate'
    , 'billStatus'
    , 'confirmTime'
    , 'approvalStatus'
    , 'extend1'
    , 'extend2'
    , 'extend3'
    , 'extend4'
    , 'extend5'
    , 'extend6'
    , 'extend7'
    , 'extend8'
    , 'extend9'
    , 'extend10'
    , 'parentId'
    , 'tradeCode'
    , 'sysOrgCode'
    , 'createBy'
    , 'createTime'
    , 'updateBy'
    , 'updateTime'
  ])
  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])
  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])
  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      resizable: true,
      fixed: 'left',
    },

    {
      title: '合同号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo',
    },
    {
      title: '协议编号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'agreementNo',
      resizable: true,
      key: 'agreementNo',
    },
    {
      title: '客户',
      width: 300,
      minWidth: 300,
      align: 'center',
      dataIndex: 'customer',
      resizable: true,
      key: 'customer',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '签约日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'signingDate',
      resizable: true,
      key: 'signingDate',
      customRender: ({ text }) => {
        return h('span', text ? text.slice(0, 10) : text)
      }
    },
    {
      title: '制单人',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'maker',
      resizable: true,
      key: 'maker',
    },
    {
      title: '制单日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'makeDate',
      resizable: true,
      key: 'makeDate',
    },
    {
      title: '单据状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'billStatus',
      resizable: true,
      key: 'billStatus',
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },
    {
      title: '确认时间',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'confirmTime',
      resizable: true,
      key: 'confirmTime',
    },
    // {
    //   title: '审批状态',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'approvalStatus',
    //   resizable: true,
    //   key: 'approvalStatus',
    // },

  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
export { getColumns }
