<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 审批状态 -->
    <a-form-item name="approvalStatus" v-if="isAudit"   :label="'审批状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.approvalStatus" id="approvalStatus">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.approval_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>


    <a-form-item name="billStatus"   :label="'单据状态'" class="grid-item"  :colon="false">
<!--      <a-input size="small" v-model:value="searchParam.billStatus" xid="s_billStatus"></a-input>-->
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.billStatus" id="billStatus">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.data_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.contractNo" xid="s_contractNo"></a-input>
    </a-form-item>
    <a-form-item name="agreementNo"   :label="'协议编号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.agreementNo" xid="s_agreementNo"></a-input>
    </a-form-item>
    <a-form-item name="customer"   :label="'客户'" class="grid-item"  :colon="false">
<!--      <a-input size="small" v-model:value="searchParam.customer" xid="s_customer"></a-input>-->
      <cs-select  optionFilterProp="label" option-label-prop="key" allow-clear show-search
                  v-model:value="searchParam.customer" id="customer">
        <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                         :value="item.value" :label="item.value + item.label">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <a-form-item name="signingDate"   :label="'签约日期'" class="grid-item"  :colon="false">
      <a-form-item-rest xid="s_signingDate">
          <a-row>
          <a-col :span="11">
              <a-date-picker v-model:value="searchParam.signingDateFrom" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" :locale="locale" size="small" style="width: 100%" placeholder=""/>
           </a-col>
          <a-col :span="2" style="text-align: center">-</a-col>
          <a-col :span="11">
              <a-date-picker v-model:value="searchParam.signingDateTo" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" :locale="locale" size="small" style="width: 100%" placeholder=""/>
          </a-col>
        </a-row>
        </a-form-item-rest>
    </a-form-item>
    <a-form-item name="maker"   :label="'制单人'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.maker" xid="s_maker"></a-input>
    </a-form-item>
    <a-form-item name="makeDate"   :label="'制单日期'" class="grid-item"  :colon="false">
      <a-form-item-rest xid="s_makeDate">
          <a-row>
          <a-col :span="11">
              <a-date-picker v-model:value="searchParam.makeDateFrom" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" :locale="locale" size="small" style="width: 100%" placeholder=""/>
           </a-col>
          <a-col :span="2" style="text-align: center">-</a-col>
          <a-col :span="11">
              <a-date-picker v-model:value="searchParam.makeDateTo" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" :locale="locale" size="small" style="width: 100%" placeholder=""/>
          </a-col>
        </a-row>
        </a-form-item-rest>
    </a-form-item>

  </a-form>
</template>
<script setup>
import {inject, onMounted, reactive, watch} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import ycCsApi from "@/api/ycCsApi";
import {message} from "ant-design-vue";
defineOptions({
  name: 'BizAgencyAgreementListSearch'
})
/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  isAudit: {
    type: Boolean,
    default: false
  }
});
const searchParam = reactive({
    businessType: '7',
    contractNo: '',
    agreementNo: '',
    customer: '',
    signingDateFrom:'',
    signingDateTo:'',
    maker: '',
    makeDateFrom:'',
    makeDateTo:'',
    billStatus: '',
    confirmTimeFrom:'',
    confirmTimeTo:'',
    approvalStatus:''
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}

watch(() => props.isAudit, (newValue) => {
  if (newValue) {
    searchParam.approvalStatus = '2';
  } else {
    searchParam.approvalStatus = '';
  }
}, { immediate: true });

//基础资料-客商信息
const buyerOptions = reactive([])

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

onMounted(() => {
  getBuyerOptions()
})
  defineExpose({
    searchParam,
    resetSearch
  })
</script>
<style scoped>
</style>
