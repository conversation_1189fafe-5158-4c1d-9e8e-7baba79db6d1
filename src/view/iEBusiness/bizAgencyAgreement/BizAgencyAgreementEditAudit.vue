<template>
  <section>
    <a-card size="small" title="代理协议" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">

          <a-form-item name="businessType" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('businessType')"
                  @click="handleLabelClick('businessType')"
              >
                业务类型
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('contractNo')"
                  @click="handleLabelClick('contractNo')"
              >
                合同号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="agreementNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('agreementNo')"
                  @click="handleLabelClick('agreementNo')"
              >
                协议编号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.agreementNo"></a-input>
          </a-form-item>
          <a-form-item name="customer" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('customer')"
                  @click="handleLabelClick('customer')"
              >
                客户
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.customer" id="customer">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="signingDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('signingDate')"
                  @click="handleLabelClick('signingDate')"
              >
                签约日期
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.signingDate" valueFormat="YYYY-MM-DD"
                           format="YYYY-MM-DD" :locale="locale" placeholder="" size="small" style="width: 100%;"/>
          </a-form-item>
          <a-form-item name="signingPlace" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('signingPlace')"
                  @click="handleLabelClick('signingPlace')"
              >
                签约地点
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.signPlace" id="signPlace">
              <a-select-option v-for="item in cityOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="currency" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('currency')"
                  @click="handleLabelClick('currency')"
              >
                币种
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.currency" id="currency">
              <a-select-option v-for="item in currencyOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractAmount" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('contractAmount')"
                  @click="handleLabelClick('contractAmount')"
              >
                合同金额
              </span>
            </template>
            <a-input-number disabled size="small" v-model:value="formData.contractAmount"
                            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="calculateAgencyFee"
                            style="width: 100%"/>
          </a-form-item>
          <a-form-item name="agencyRate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('agencyRate')"
                  @click="handleLabelClick('agencyRate')"
              >
                代理费率%
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.agencyRate"
                            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="calculateAgencyFee"
                            style="width: 100%"/>
          </a-form-item>
          <a-form-item name="agencyFee" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('agencyFee')"
                  @click="handleLabelClick('agencyFee')"
              >
                代理费用
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.agencyFee"
                            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            style="width: 100%"/>
          </a-form-item>
          <a-form-item name="suggestedSignatory" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('suggestedSignatory')"
                  @click="handleLabelClick('suggestedSignatory')"
              >
                建议授权签约人
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.suggestedSignatory"></a-input>
          </a-form-item>
          <a-form-item name="agreementTerms" class="grid-item  merge-3" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('agreementTerms')"
                  @click="handleLabelClick('agreementTerms')"
              >
                协议条款
              </span>
            </template>
            <a-textarea :disabled="showDisable" size="small" v-model:value="formData.agreementTerms"
                        :autosize="{ minRows: 3, maxRows: 10 }"/>
          </a-form-item>
          <a-form-item name="maker" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('maker')"
                  @click="handleLabelClick('maker')"
              >
                制单人
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.maker"></a-input>
          </a-form-item>
          <a-form-item name="makeDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('makeDate')"
                  @click="handleLabelClick('makeDate')"
              >
                制单日期
              </span>
            </template>
            <a-date-picker disabled v-model:value="formData.makeDate" valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="billStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('billStatus')"
                  @click="handleLabelClick('billStatus')"
              >
                单据状态
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.billStatus" id="billStatus">
              <a-select-option v-for="item in productClassify.data_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="confirmTime" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('confirmTime')"
                  @click="handleLabelClick('confirmTime')"
              >
                确认时间
              </span>
            </template>
            <a-date-picker disabled v-model:value="formData.confirmTime" valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>

          <a-form-item name="approvalStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('approvalStatus')"
                @click="handleLabelClick('approvalStatus')"
              >
                审批状态
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.approvalStatus" id="approvalStatus">
              <a-select-option v-for="item in productClassify.approval_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>

    <a-card size="small" title="代理协议表体" class="cs-card-form">
      <a-tabs v-model:activeKey="headInnerTabKey" v-if="formData.id" >
        <a-tab-pane key="1" tab="进口薄片">
          <a-card size="small" title="" class="cs-card-form">
            <biz-agency-agreement-list-table :head-id="formData.id" :i-e-mark="'I'" :edit-config="props.editConfig"></biz-agency-agreement-list-table>
          </a-card>
        </a-tab-pane>
        <a-tab-pane key="2" tab="出料加工">
          <a-card size="small" title="" class="cs-card-form">
            <biz-agency-agreement-list-table :head-id="formData.id" :i-e-mark="'E'" :edit-config="props.editConfig"></biz-agency-agreement-list-table>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import BizAgencyAgreementListTable from "@/view/iEBusiness/bizAgencyAgreementList/BizAgencyAgreementListTable.vue";
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'id':'主键'
, 'businessType':'业务类型'
, 'contractNo':'合同号'
, 'agreementNo':'协议编号'
, 'customer':'客户'
, 'signingDate':'签约日期'
, 'signingPlace':'签约地点'
, 'currency':'币种'
, 'contractAmount':'合同金额'
, 'agencyRate':'代理费率%'
, 'agencyFee':'代理费用'
, 'suggestedSignatory':'建议授权签约人'
, 'agreementTerms':'协议条款'
, 'maker':'制单人'
, 'makeDate':'制单日期'
, 'billStatus':'单据状态'
, 'confirmTime':'确认时间'
, 'approvalStatus':'审批状态'
, 'extend1':'扩展字段1'
, 'extend2':'扩展字段2'
, 'extend3':'扩展字段3'
, 'extend4':'扩展字段4'
, 'extend5':'扩展字段5'
, 'extend6':'扩展字段6'
, 'extend7':'扩展字段7'
, 'extend8':'扩展字段8'
, 'extend9':'扩展字段9'
, 'extend10':'扩展字段10'
, 'parentId':'父节点'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'updateBy':'最后修改人'
, 'updateTime':'最后修改时间'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 表单校验规则
const rules = {

  businessType: [
    { required: true, message: '不能为空！', trigger: 'blur' },
    { max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
  ],
  contractNo: [
    { required: true, message: '不能为空！', trigger: 'blur' },
    { max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
  ],
  agreementNo: [
    { required: true, message: '不能为空！', trigger: 'blur' },
    { max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
  ],
  customer: [
    { required: true, message: '不能为空！', trigger: 'blur' },
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  signingDate: [
  ],
  signingPlace: [
    { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
  ],
  currency: [
    { required: true, message: '不能为空！', trigger: 'blur' },
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ],
  contractAmount: [
    { required: true, message: '不能为空！', trigger: 'blur' },
  ],
  agencyRate: [
  ],
  agencyFee: [
  ],
  suggestedSignatory: [
    { max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
  ],
  agreementTerms: [
    { max: 1000, message: '长度不能超过1000位字节(汉字占2位)！', trigger: 'blur' }
  ],
  maker: [
    { required: true, message: '不能为空！', trigger: 'blur' },
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ],
  makeDate: [
    { required: true, message: '不能为空！', trigger: 'blur' },
  ],
  billStatus: [
    { required: true, message: '不能为空！', trigger: 'blur' },
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ],
  confirmTime: [
  ],
  approvalStatus: [
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ],

}
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid:''
,id: ''
,businessType: ''
,contractNo: ''
,agreementNo: ''
,customer: ''
,signingDate: ''
,signingPlace: ''
,currency: ''
,contractAmount: ''
,agencyRate: ''
,agencyFee: ''
,suggestedSignatory: ''
,agreementTerms: ''
,maker: ''
,makeDate: ''
,billStatus: ''
,confirmTime: ''
,approvalStatus: ''
,extend1: ''
,extend2: ''
,extend3: ''
,extend4: ''
,extend5: ''
,extend6: ''
,extend7: ''
,extend8: ''
,extend9: ''
,extend10: ''
,parentId: ''
,tradeCode: ''
,sysOrgCode: ''
,createBy: ''
,createTime: ''
,updateBy: ''
})
// 表单引用
const formRef = ref()

const headInnerTabKey = ref('1')
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.id).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    saveCurrentMarkings(formData.id, 'default', fieldMarkings.value)
  }
}

//基础资料-客商信息
const buyerOptions = reactive([])

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

const cityOptions = reactive([])

const getCityOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.city.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        cityOptions.push({
          value: item.paramCode,
          label: item.cityCnName
        });
      });
    } else {
      message.error(res.message || '获取城市数据失败');
    }
  } catch (error) {
    message.error('获取城市数据失败');
  }
}

//海关参数币制
const currencyOptions = reactive([]);

const getCurrency = async () => {
  const params = {
    paramsType: 'CURR',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/7`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        currencyOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取币制数据失败');
    }
  } catch (error) {
    message.error('获取币制数据失败');
  }
}

const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  getBuyerOptions()
  getCurrency()
  getCityOptions()
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.id, 'default')
})
/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }
  // 审核意见输入框
  const auditOpinion = ref('同意审批')
  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.id],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '7',
        billType: 'proxy',
      }
      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.audit, params)
          .then(res => {
            if (res.code === 200) {
              message.success("审核通过成功！")
              // 返回列表页面
              onBack(true)
            } else {
              message.error(res.message || '审核失败')
            }
          })
          .catch(error => {
            console.error('审核失败:', error)
            message.error('审核失败，请重试')
          })
          .finally(() => {
            auditLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }
  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.id],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '7',
        billType: 'proxy',
      }
      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.reject, params)
          .then(res => {
            if (res.code === 200) {
              // 审核退回成功后，调用标记保存接口
              return saveCurrentMarkings(formData.id, 'default', fieldMarkings.value)
            } else {
              throw new Error(res.message || '审核退回失败')
            }
          })
          .then(res => {
            message.success("审核退回成功！")
            // 返回列表页面
            onBack(true)
          })
          .catch(error => {
            console.error('审核退回失败:', error)
            message.error(error.message || '审核退回失败，请重试')
          })
          .finally(() => {
            invalidLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
</script>
<style lang="less" scoped>
</style>
