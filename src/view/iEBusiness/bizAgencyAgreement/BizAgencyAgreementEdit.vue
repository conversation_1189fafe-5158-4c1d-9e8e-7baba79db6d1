<template>
  <section>
    <a-card size="small" title="代理协议" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
<!--          <a-form-item name="id" :label="'主键'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.id"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input disabled size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="agreementNo" :label="'协议编号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.agreementNo"></a-input>
          </a-form-item>
          <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.customer"></a-input>-->
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.customer" id="customer">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="signingDate" :label="'签约日期'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.signingDate" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="signingPlace" :label="'签约地点'" class="grid-item" :colon="false">
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.signingPlace"></a-input>-->
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.signPlace" id="signPlace">
              <a-select-option v-for="item in cityOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="currency" :label="'币种'" class="grid-item" :colon="false">
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.currency"></a-input>-->
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.currency" id="currency">
              <a-select-option v-for="item in currencyOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractAmount" :label="'合同金额'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.contractAmount"
                            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="calculateAgencyFee"
                            style="width: 100%"/>
          </a-form-item>
          <a-form-item name="agencyRate" :label="'代理费率%'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.agencyRate"
                            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="calculateAgencyFee"
                            style="width: 100%"/>
          </a-form-item>
          <a-form-item name="agencyFee" :label="'代理费用'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.agencyFee"
                            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            style="width: 100%"/>
          </a-form-item>
          <a-form-item name="suggestedSignatory" :label="'建议授权签约人'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.suggestedSignatory"></a-input>
          </a-form-item>
          <a-form-item name="agreementTerms" :label="'协议条款'" class="grid-item merge-3" :colon="false">
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.agreementTerms"></a-input>-->
            <a-textarea :disabled="showDisable" size="small" v-model:value="formData.agreementTerms"  :autosize="{ minRows: 3, maxRows: 10 }"/>
          </a-form-item>
          <a-form-item name="maker" :label="'制单人'" class="grid-item" :colon="false">
            <a-input disabled size="small" v-model:value="formData.maker"></a-input>
          </a-form-item>
          <a-form-item name="makeDate" :label="'制单日期'" class="grid-item" :colon="false">
            <a-date-picker disabled v-model:value="formData.makeDate" valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="billStatus" :label="'单据状态'" class="grid-item" :colon="false">
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.billStatus"></a-input>-->
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.billStatus" id="billStatus">
              <a-select-option v-for="item in productClassify.data_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker disabled v-model:value="formData.confirmTime" valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="approvalStatus" :label="'审批状态'" class="grid-item" :colon="false">
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.approvalStatus"></a-input>-->
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.approvalStatus" id="approvalStatus">
              <a-select-option v-for="item in productClassify.approval_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


    <a-card size="small" title="代理协议表体" class="cs-card-form">
      <a-tabs v-model:activeKey="headInnerTabKey" v-if="formData.id" >
        <a-tab-pane key="1" tab="进口薄片">
          <a-card size="small" title="" class="cs-card-form">
            <biz-agency-agreement-list-table :head-id="formData.id" :i-e-mark="'I'" :edit-config="props.editConfig"></biz-agency-agreement-list-table>
          </a-card>
        </a-tab-pane>
        <a-tab-pane key="2" tab="出料加工">
          <a-card size="small" title="" class="cs-card-form">
            <biz-agency-agreement-list-table :head-id="formData.id" :i-e-mark="'E'" :edit-config="props.editConfig"></biz-agency-agreement-list-table>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode, watch} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import BizAgencyAgreementListTable from '../bizAgencyAgreementList/BizAgencyAgreementListTable.vue'
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'id':'主键'
, 'businessType':'业务类型'
, 'contractNo':'合同号'
, 'agreementNo':'协议编号'
, 'customer':'客户'
, 'signingDate':'签约日期'
, 'signingPlace':'签约地点'
, 'currency':'币种'
, 'contractAmount':'合同金额'
, 'agencyRate':'代理费率%'
, 'agencyFee':'代理费用'
, 'suggestedSignatory':'建议授权签约人'
, 'agreementTerms':'协议条款'
, 'maker':'制单人'
, 'makeDate':'制单日期'
, 'billStatus':'单据状态'
, 'confirmTime':'确认时间'
, 'approvalStatus':'审批状态'
, 'extend1':'扩展字段1'
, 'extend2':'扩展字段2'
, 'extend3':'扩展字段3'
, 'extend4':'扩展字段4'
, 'extend5':'扩展字段5'
, 'extend6':'扩展字段6'
, 'extend7':'扩展字段7'
, 'extend8':'扩展字段8'
, 'extend9':'扩展字段9'
, 'extend10':'扩展字段10'
, 'parentId':'父节点'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'updateBy':'最后修改人'
, 'updateTime':'最后修改时间'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

//首次新增未保存 删除数据
let firstAddSave = ref(false);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid: ''
,id: ''
,businessType: ''
,contractNo: ''
,agreementNo: ''
,customer: ''
,signingDate: ''
,signingPlace: ''
,currency: ''
,contractAmount: ''
,agencyRate: ''
,agencyFee: ''
,suggestedSignatory: ''
,agreementTerms: ''
,maker: ''
,makeDate: ''
,billStatus: ''
,confirmTime: ''
,approvalStatus: ''
,extend1: ''
,extend2: ''
,extend3: ''
,extend4: ''
,extend5: ''
,extend6: ''
,extend7: ''
,extend8: ''
,extend9: ''
,extend10: ''
,parentId: ''
,tradeCode: ''
,sysOrgCode: ''
,createBy: ''
,createTime: ''
,updateBy: ''
})
// 表单校验规则
const rules = {

businessType: [
  { required: true, message: '不能为空！', trigger: 'blur' },
{ max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
],
contractNo: [
  { required: true, message: '不能为空！', trigger: 'blur' },
{ max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
],
agreementNo: [
  { required: true, message: '不能为空！', trigger: 'blur' },
  { max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
],
customer: [
  { required: true, message: '不能为空！', trigger: 'blur' },
  { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
signingDate: [
],
signingPlace: [
{ max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
],
currency: [
  { required: true, message: '不能为空！', trigger: 'blur' },
  { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
],
contractAmount: [
  { required: true, message: '不能为空！', trigger: 'blur' },
],
agencyRate: [
],
agencyFee: [
],
suggestedSignatory: [
{ max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
],
agreementTerms: [
{ max: 1000, message: '长度不能超过1000位字节(汉字占2位)！', trigger: 'blur' }
],
maker: [
  { required: true, message: '不能为空！', trigger: 'blur' },
  { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
],
makeDate: [
  { required: true, message: '不能为空！', trigger: 'blur' },
],
billStatus: [
  { required: true, message: '不能为空！', trigger: 'blur' },
  { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
],
confirmTime: [
],
approvalStatus: [
{ max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
],

}
// 表单引用
const formRef = ref()

const headInnerTabKey = ref('1')
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    window.majesty.httpUtil.deleteAction(`${ycCsApi.iEBusiness.bizAgencyAgreement.delete}/${formData.id}`).then(res => {
      emit('onEditBack', val);
    })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      window.majesty.httpUtil.putAction(`${ycCsApi.iEBusiness.bizAgencyAgreement.update}/${formData.id}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
          firstAddSave = true
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      window.majesty.httpUtil.putAction(`${ycCsApi.iEBusiness.bizAgencyAgreement.update}/${formData.id}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}


//基础资料-客商信息
const buyerOptions = reactive([])

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

const cityOptions = reactive([])

const getCityOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.city.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        cityOptions.push({
          value: item.paramCode,
          label: item.cityCnName
        });
      });
    } else {
      message.error(res.message || '获取城市数据失败');
    }
  } catch (error) {
    message.error('获取城市数据失败');
  }
}

//海关参数币制
const currencyOptions = reactive([]);

const getCurrency = async () => {
  const params = {
    paramsType: 'CURR',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/7`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        currencyOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取币制数据失败');
    }
  } catch (error) {
    message.error('获取币制数据失败');
  }
}

const pCode = ref('')
// 计算代理费用的函数
const calculateAgencyFee = () => {
  if (formData.contractAmount && formData.agencyRate) {
    // 计算代理费用 = 合同金额 * 代理费率% / 100
    const calculatedFee = (parseFloat(formData.contractAmount) * parseFloat(formData.agencyRate)) / 100;
    formData.agencyFee = calculatedFee;
  }
};

// 监听合同金额变化，自动计算代理费用
watch(() => formData.contractAmount, () => {
  calculateAgencyFee();
});

// 监听代理费率变化，自动计算代理费用
watch(() => formData.agencyRate, () => {
  calculateAgencyFee();
});

// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  getBuyerOptions()
  getCurrency()
  getCityOptions()
  firstAddSave = false
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.id, 'default')
})
</script>
<style lang="less" scoped>
</style>
