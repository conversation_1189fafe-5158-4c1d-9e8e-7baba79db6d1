import { defineAsyncComponent } from "vue"
export default [
  {
    path: '/tobacco/iEBusiness/bizAgencyAgreementList',
    name: 'BizAgencyAgreementList',
    meta: {
      icon: 'ios-document',
      title: '代理协议'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./bizAgencyAgreement/BizAgencyAgreementList.vue"))
  },

  {
    path: '/audit/iEBusiness/bizAgencyAgreementList',
    name: 'BizAgencyAgreementAuditList',
    meta: {
      title: '代理协议审核'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./bizAgencyAgreement/BizAgencyAgreementListAudit.vue"))
  },
]
