const debounceMap = new Map()

export function debounced(id) {
  if (debounceMap.has(id)) {
    return debounceMap.get(id)
  }
  let timer = null
  const debounced = (func, delay = 1000) => {
    return (...args) => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        func(...args)
      }, delay)
    }
  }
  debounceMap.set(id, debounced)
  return debounced
}

export function removeDebounced(id) {
  if (debounceMap.has(id)) {
    debounceMap.delete(id)
  }
}
