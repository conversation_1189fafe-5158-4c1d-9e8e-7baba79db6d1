<template>
  <a-form ref="formRef" layout="inline" label-align="right" :label-col="{ style: { width: '100px' } }"
          :model="searchParam"
          class="cs-form  grid-container">
    <!-- 单据状态 -->
    <a-form-item name="dataStatus" :label="'单据状态'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search multiple
                 v-model:value="searchParam.dataStatus">
        <a-select-option class="cs-select-dropdown" v-for="item in productClassify.data_status"
                         :key="`${item.value} ${item.label}`" :value="item.value" :label="`${item.value}${item.label}`">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 合同号 -->
    <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.contractNo" allow-clear />
    </a-form-item>

    <!-- 买方 -->
    <a-form-item name="buyer" :label="'买方'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.buyer">
        <a-select-option class="cs-select-dropdown" v-for="item in optionsConfig.merchantOptions"
                         :key="`${item.value} ${item.label}`" :value="item.value" :label="`${item.value}${item.label}`">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 卖方 -->
    <a-form-item name="seller" :label="'卖方'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.seller">
        <a-select-option class="cs-select-dropdown" v-for="item in optionsConfig.merchantOptions"
                         :key="`${item.value} ${item.label}`" :value="item.value" :label="`${item.value}${item.label}`">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 使用厂家 -->
    <a-form-item name="usingManufacturer" :label="'使用厂家'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.usingManufacturer">
        <a-select-option class="cs-select-dropdown" v-for="item in optionsConfig.merchantOptions"
                         :key="`${item.value} ${item.label}`" :value="item.value" :label="`${item.value}${item.label}`">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 国内委托方 -->
    <a-form-item name="domesticClient" :label="'国内委托方'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.domesticClient">
        <a-select-option class="cs-select-dropdown" v-for="item in optionsConfig.merchantOptions"
                         :key="`${item.value} ${item.label}`" :value="item.value" :label="`${item.value}${item.label}`">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 制单日期 -->
    <a-form-item name="documentMakeDate" label="制单日期" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.documentMakeDateFrom"
              :valueFormat="DATE_FORMAT.DATE"
              :format="DATE_FORMAT.DATE"
              size="small"
              style="width: 100%"
              placeholder="制单日期起"
              :locale="locale"
              allow-clear
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.documentMakeDateTo"
              :valueFormat="DATE_FORMAT.DATE"
              :format="DATE_FORMAT.DATE"
              size="small"
              style="width: 100%"
              placeholder="制单日期止"
              :locale="locale"
              allow-clear
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>

    <!-- 制单人 -->
    <a-form-item name="documentMaker" :label="'制单人'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.documentMaker">
        <a-select-option class="cs-select-dropdown" v-for="item in optionsConfig.makerOptions"
                         :key="`${item.value} ${item.label}`" :value="item.value" :label="`${item.value}${item.label}`">
          {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
  </a-form>
</template>

<script setup>
import CsSelect from '@/components/select/CsSelect.vue'
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN'
import { inject, ref } from 'vue'
import { productClassify, DATE_FORMAT } from '@/view/common/constant'
import { OPTIONS_CONFIG } from '@/view/equipment/foreignContract/js/handle'

const optionsConfig = inject(OPTIONS_CONFIG)

const searchParam = ref({
  dataStatus: '',
  contractNo: '',
  buyer: '',
  seller: '',
  usingManufacturer: '',
  domesticClient: '',
  documentMaker: '',
  documentMakeDateFrom: '',
  documentMakeDateTo: ''
})

const formRef = ref(null)

const resetSearch = () => {
  formRef.value.resetFields()
}

defineExpose({ searchParam, resetSearch })

defineOptions({
  name: 'EquipmentForeignContractSearch'
})
</script>
