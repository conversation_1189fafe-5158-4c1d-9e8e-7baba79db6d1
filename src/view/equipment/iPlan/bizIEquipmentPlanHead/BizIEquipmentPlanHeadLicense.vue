<template>
  <section>
    <a-card size="small" title="证件信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">

          <!-- 许可证号 -->
          <a-form-item name="licenseNo" :label="'许可证号'" class="grid-item" :colon="false">
            <a-input
              :disabled="buttonDisabled"
              size="small"
              v-model:value="formData.licenseNo"
              maxlength="60"
              placeholder="请输入许可证号"
            />
          </a-form-item>

          <!-- 许可证申请日期 -->
          <a-form-item name="licenseApplyDate" :label="'许可证申请日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="buttonDisabled"
              v-model:value="formData.licenseApplyDate"
              id="licenseApplyDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder="请选择许可证申请日期"
            />
          </a-form-item>

          <!-- 许可证有效期 -->
          <a-form-item name="licenseValidityDate" :label="'许可证有效期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="buttonDisabled"
              v-model:value="formData.licenseValidityDate"
              id="licenseValidityDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder="请选择许可证有效期"
            />
          </a-form-item>

          <!-- 许可证备注 -->
          <a-form-item name="licenseRemark" :label="'许可证备注'" class="grid-item merge-3" :colon="false">
            <a-textarea
              :disabled="buttonDisabled"
              size="small"
              v-model:value="formData.licenseRemark"
              :autosize="{ minRows: 3, maxRows: 6 }"
              maxlength="200"
              placeholder="请输入许可证备注"
              show-count
            />
          </a-form-item>

          <!-- 操作按钮 -->
          <div class="cs-submit-btn merge-3">
            <a-button
              size="small"
              type="primary"
              @click="handlerSave"
              class="cs-margin-right"
              v-show="props.editConfig.editStatus !== editStatus.SHOW"
              :disabled="buttonDisabled"
            >
              保存
            </a-button>
            <a-button
              size="small"
              class="cs-margin-right cs-warning"
              @click="onBack"
            >
              返回
            </a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>

<script setup>
import { computed, onMounted, reactive, ref, watch } from "vue";
import { editStatus } from "@/view/common/constant";
import { message } from "ant-design-vue";
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { updateEquipmentPlan } from "@/api/equipment/equipmentPlanApi";

defineOptions({
  name: 'BizIEquipmentPlanHeadLicense'
})

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => ({})
  },
  headId: {
    type: String,
    default: ''
  },
  operationStatus: {
    type: String,
    default: ''
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = () => {
  emit('onEditBack', true);
};

// 是否禁用
const showDisable = ref(false)

// 计算最终禁用状态
const fieldDisabled = computed(() => {
  return showDisable.value || props.editConfig.editStatus === editStatus.SHOW
})

// 添加表头按钮控制字段
const hasHeadButtonControl = computed(() => {
  return (props.editConfig.editData.hasHeadNotice === '1')
})

// 计算按钮最终禁用状态（结合显示模式和表头控制）
const buttonDisabled = computed(() => {
  return showDisable.value || hasHeadButtonControl.value
})

// 日期本地化
const locale = zhCN;

// 表单引用
const formRef = ref();

// 表单数据 - 使用reactive创建响应式数据
const formData = reactive({
  licenseNo: '',
  licenseApplyDate: '',
  licenseValidityDate: '',
  licenseRemark: ''
})

// 表单校验规则
const rules = {
  licenseNo: [
    { max: 60, message: '许可证号不能超过60个字符', trigger: 'blur' }
  ],
  licenseRemark: [
    { max: 200, message: '许可证备注不能超过200个字符', trigger: 'blur' }
  ]
}

// 保存操作
const handlerSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    // 检查是否有ID
    if (!props.editConfig.editData.id) {
      message.error('缺少表头数据，无法保存');
      return;
    }

    // 准备保存数据 - 需要包含完整的设备计划数据，不只是证件字段
    const saveData = {
      // 从父组件获取完整的设备计划数据
      ...props.editConfig.editData,
      // 更新证件相关字段
      licenseNo: formData.licenseNo,
      licenseApplyDate: formData.licenseApplyDate,
      licenseValidityDate: formData.licenseValidityDate,
      licenseRemark: formData.licenseRemark
    };

    // 调用updateEquipmentPlan接口
    const res = await updateEquipmentPlan(props.editConfig.editData.id, saveData);

    if (res.code === 200) {
      // 将后端返回的完整数据同步回父组件的editData
      if (props.editConfig && props.editConfig.editData && res.data) {
        Object.assign(props.editConfig.editData, res.data);
      }

      // 同时更新本地表单数据
      if (res.data) {
        formData.licenseNo = res.data.licenseNo || '';
        formData.licenseApplyDate = res.data.licenseApplyDate || '';
        formData.licenseValidityDate = res.data.licenseValidityDate || '';
        formData.licenseRemark = res.data.licenseRemark || '';
      }

      message.success('证件信息保存成功');
    } else {
      message.error(res.message || '保存失败');
    }

  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请检查输入信息');
  }
}

// 初始化数据
const initData = () => {
  if (props.editConfig && props.editConfig.editData) {
    const editData = props.editConfig.editData;
    formData.licenseNo = editData.licenseNo || '';
    formData.licenseApplyDate = editData.licenseApplyDate || '';
    formData.licenseValidityDate = editData.licenseValidityDate || '';
    formData.licenseRemark = editData.licenseRemark || '';
  }
}

// 监听editConfig变化
watch(() => props.editConfig, () => {
  initData();
}, { deep: true, immediate: true });

// 组件挂载时初始化数据
onMounted(() => {
  initData();

  // 根据编辑状态设置禁用状态
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  } else {
    showDisable.value = false
  }
})
</script>

<style scoped>
:deep(.cs-submit-btn) {
  padding-bottom: 0;
  margin: 10px 0
}

.head :deep(.ant-card-body) {
  padding-bottom: 0;
}
</style>
