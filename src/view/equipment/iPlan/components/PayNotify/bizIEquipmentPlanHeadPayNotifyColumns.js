import { ref } from 'vue'

export function getColumns() {
  const totalColumns = ref([
    {
      title: '操作',
      key: 'operation',
      width: 100,
      minWidth: 80,
      align: 'center',
      fixed: 'left',
      resizable: true
    },
    {
      title: '序号',
      dataIndex: 'serialNo',
      key: 'serialNo',
      width: 80,
      minWidth: 60,
      align: 'center',
      resizable: true
    },
    {
      title: '款项类型',
      dataIndex: 'paymentType',
      key: 'paymentType',
      width: 200,
      minWidth: 150,
      align: 'center',
      resizable: true
    },
    {
      title: '合同金额',
      dataIndex: 'contractAmount',
      key: 'contractAmount',
      width: 120,
      minWidth: 100,
      align: 'right',
      resizable: true
    },
    {
      title: '汇率',
      dataIndex: 'exchangeRate',
      key: 'exchangeRate',
      width: 100,
      minWidth: 80,
      align: 'right',
      resizable: true
    },
    {
      title: '进出口公司代理费率%',
      dataIndex: 'importExportAgentRate',
      key: 'importExportAgentRate',
      width: 150,
      minWidth: 120,
      align: 'right',
      resizable: true
    },
    {
      title: '进出口公司代理费',
      dataIndex: 'importExportAgentFee',
      key: 'importExportAgentFee',
      width: 140,
      minWidth: 120,
      align: 'right',
      resizable: true
    },
    {
      title: '总公司代理费率%',
      dataIndex: 'headOfficeAgentRate',
      key: 'headOfficeAgentRate',
      width: 130,
      minWidth: 110,
      align: 'right',
      resizable: true
    },
    {
      title: '总公司代理费',
      dataIndex: 'headOfficeAgentFee',
      key: 'headOfficeAgentFee',
      width: 120,
      minWidth: 100,
      align: 'right',
      resizable: true
    },
    {
      title: '计费箱数',
      dataIndex: 'chargeContainerCount',
      key: 'chargeContainerCount',
      width: 100,
      minWidth: 80,
      align: 'right',
      resizable: true
    },
    {
      title: '通关费',
      dataIndex: 'customsClearanceFee',
      key: 'customsClearanceFee',
      width: 100,
      minWidth: 80,
      align: 'right',
      resizable: true
    },
    {
      title: '验柜服务费',
      dataIndex: 'containerInspectionFee',
      key: 'containerInspectionFee',
      width: 110,
      minWidth: 90,
      align: 'right',
      resizable: true
    },
    {
      title: '货代费用',
      dataIndex: 'freightForwardingFee',
      key: 'freightForwardingFee',
      width: 100,
      minWidth: 80,
      align: 'right',
      resizable: true
    },
    {
      title: '保险费率',
      dataIndex: 'insuranceRate',
      key: 'insuranceRate',
      width: 100,
      minWidth: 80,
      align: 'right',
      resizable: true
    },
    {
      title: '保险费',
      dataIndex: 'insuranceFee',
      key: 'insuranceFee',
      width: 100,
      minWidth: 80,
      align: 'right',
      resizable: true
    },
    {
      title: '划款金额（RMB）',
      dataIndex: 'remittanceAmountRmb',
      key: 'remittanceAmountRmb',
      width: 140,
      minWidth: 120,
      align: 'right',
      resizable: true
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      minWidth: 100,
      resizable: true
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      key: 'createBy',
      width: 100,
      minWidth: 80,
      align: 'center',
      resizable: true
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      minWidth: 120,
      align: 'center',
      resizable: true
    },

  ])

  return {
    totalColumns
  }
}
