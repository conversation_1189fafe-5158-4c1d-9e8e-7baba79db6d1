const columnsConfig = ['selection','operation'
  , 'id'
  , 'headId'
  , 'containerType'
  , 'unitPriceTotal'
  , 'containerCount'
  , 'amount'
  , 'createBy'
  , 'createTime'
  , 'updateBy'
  , 'updateTime'
  , 'sysOrgCode'
  , 'tradeCode'
  , 'createUserName'
  , 'updateUserName'
  , 'extend1'
  , 'extend2'
  , 'extend3'
  , 'extend4'
  , 'extend5'
  , 'extend6'
  , 'extend7'
  , 'extend8'
  , 'extend9'
  , 'extend10'
        ]
    const columns = {
      data() {
        return {
          totalColumns: [
            {
              type: 'selection',
              width: 60,
              align: 'center',
              key: 'selection',
            },
            {
              title: '操作',
              width: 120,
              align: 'center',
              render: (h,params) => {
                return h('div', [
                  h('a', {
                    props: {
                      type: 'primary',
                      size: 'small'
                    },
                    style: {
                      marginRight: '15px'
                    },
                    on: {
                      click: () => {
                        this.gridConfig.selectData = params.row
                        this.handleEdit()
                      }
                    }
                  }, '编辑'),
                  //添加查看按钮
                  h('a', {
                    props: {
                      type: 'primary',
                    },
                    style: {},
                    on: {
                      click: () => {
                        this.gridConfig.selectData = params.row
                        this.handleView()
                      },
                    },
                  }, '查看')
                        ]);
              },
              key: 'operation'
            },
            {
              title: '箱型（如20GP,40HQ等）',
              minWidth: 120,
              align: 'center',
              key: 'containerType',
            },
            {
              title: '单价合计',
              minWidth: 120,
              align: 'center',
              key: 'unitPriceTotal',
            },
            {
              title: '箱数',
              minWidth: 120,
              align: 'center',
              key: 'containerCount',
            },
            {
              title: '金额',
              minWidth: 120,
              align: 'center',
              key: 'amount',
            },
          ]
        }
      }
    }
export {
  columns,
  columnsConfig
}
