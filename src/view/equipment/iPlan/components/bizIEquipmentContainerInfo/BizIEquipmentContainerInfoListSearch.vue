<template>
    <section>
        <XdoForm ref="formInline" class="dc-form" :model="searchParams" label-position="right" :label-width="100" inline>
        </XdoForm>
    </section>
</template>
<script>
    import { tobaccoUri } from '@/api'
    export default {
        name: 'BizIEquipmentContainerInfoListSearch',
        data() {
            return {
                searchParams: {
                },
            }
        },
        mounted(){
        }
    }
</script>
<style scoped>
</style>
