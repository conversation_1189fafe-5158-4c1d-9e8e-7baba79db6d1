<template>
  <section>
    <div>
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search" >
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <BizIEquipmentContainerInfoListSearch ref="listSearch"></BizIEquipmentContainerInfoListSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <XdoButton v-if="item.needed === true" xid="t_head_list_func_btn" :type="item.type" :disabled="item.disabled" :loading="item.loading" style="font-size: 12px" :class="item.key"
                    @click="item.click" :key="item.label">
              <Icon :type="item.icon" size="22" class="xdo-icon"/>{{ item.label }}</XdoButton>&nbsp;
          </template>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                 :data="gridConfig.data" stripe :border="gridConfig.data.length > 0"  @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" show-total show-sizer
                   @on-change="pageChange" @on-page-size-change="pageSizeChange" :page-size-opts="[15,20,30,40]"/>
        </div>
      </XdoCard>
    </div>
    <BizIEquipmentContainerInfoEdit :show.sync="showEditDialog" :edit-config="editConfig"></BizIEquipmentContainerInfoEdit>
  </section>
</template>
<script>
  import BizIEquipmentContainerInfoEdit from './BizIEquipmentContainerInfoEdit'
  import BizIEquipmentContainerInfoListSearch from './BizIEquipmentContainerInfoListSearch'
  import { tobaccoUri } from '@/api'
  import { getColumnsByConfig  } from '@/common'
  import { columnsConfig,columns  } from './bizIEquipmentContainerInfoColumns'
  import { commonProcess } from '@/view/tobacco-common'
  import { editStatus } from '@/view/tobacco-common/constant'
  export default {
    name: 'BizIEquipmentContainerInfoList',
    mixins: [columns, commonProcess],
    components: {
      BizIEquipmentContainerInfoEdit,
      BizIEquipmentContainerInfoListSearch,
    },
    data() {
      return {
        showEditDialog: false,
        actions: [
        { type: 'text', disabled: false, click: this.handleAdd, icon: 'ios-add', label: '新增', key:'xdo-btn-add', loading: false, needed: true },
        { type: 'text', disabled: false, click: this.handleDelete, icon: 'ios-trash-outline', label: '删除', key:'xdo-btn-delete', loading: false, needed: true },
        ],
      }
    },
    mounted() {
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
      this.refreshDynamicHeight()
      this.getList()
    },
    methods: {
      getList() {
        this.tableLoading = true
        this.$nextTick(() => {
          let params = Object.assign({}, this.getSearchParams())
          // 其他参数赋值
          //params.iEMark = "E"
          //params.emsNo = "E2325B0A0008"
          this.$http.post(tobaccoUri.equipmentUrl.bizIEquipmentContainerInfo.list, params, {params: this.pageParam}).then(res => {
            if (res.data.success) {
              this.tableLoading = false
              this.gridConfig.data = res.data.data
              this.pageParam.dataTotal = res.data.total
            }
          }, () => {})
        })
      },
      handleEdit() {
          this.editConfig.editStatus = editStatus.EDIT
          this.editConfig.editData = this.gridConfig.selectData
          this.editConfig.headId = this.gridConfig.selectData.sid
          this.showEditDialog = true
      },
      handleView() {
          this.editConfig.editStatus = editStatus.SHOW
          this.editConfig.editData = this.gridConfig.selectData
          this.editConfig.headId = this.gridConfig.selectData.sid
          this.showEditDialog = true
      },
      handleAdd() {
        this.editConfig.editStatus = editStatus.ADD
        this.showEditDialog = true
      },
      handleDelete() {
        this.doDelete(tobaccoUri.equipmentUrl.bizIEquipmentContainerInfo.delete)
      },
    },
    watch: {
      showEditDialog: {
        handler: function(val) {
          if (!val) {
          this.gridConfig.selectRows = []
          this.getList()
          }
        }
      }
    }
  }
</script>
<style lang="less" scoped>
</style>
