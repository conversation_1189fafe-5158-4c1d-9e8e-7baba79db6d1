<template>
    <XdoModal v-model="showEditDialog" :title="title" width="1000" :footer-hide="true" :mask-closable="false" @on-cancel="handleBack">
        <div v-focus>
            <XdoForm v-if="show" ref="formItem" class="dc-form" :model="formItem" label-position="right" :label-width="120" :rules="ruleValidate" inline>
                <XdoFormItem prop="containerType" label="箱型（如20GP,40HQ等）" xid="c_head_edit_form_item">
                <a-input :disabled="showDisable" size="small" v-model:value="formItem.containerType"></a-input>
                </XdoFormItem>
                <XdoFormItem prop="unitPriceTotal" label="单价合计" xid="c_head_edit_form_item">
                <a-input-number :disabled="showDisable" size="small" v-model:value="formItem.unitPriceTotal" style="width: 100%"/>
                </XdoFormItem>
                <XdoFormItem prop="containerCount" label="箱数" xid="c_head_edit_form_item">
                <a-input-number :disabled="showDisable" size="small" v-model:value="formItem.containerCount" style="width: 100%"/>
                </XdoFormItem>
                <XdoFormItem prop="amount" label="金额" xid="c_head_edit_form_item">
                <a-input-number :disabled="showDisable" size="small" v-model:value="formItem.amount" style="width: 100%"/>
                </XdoFormItem>
            </XdoForm>
        </div>
        <div style="text-align: center;margin-top: 10px;">
            <template v-for="item in buttons">
                <XdoButton v-if="item.needed === true" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                           @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
                </XdoButton>
            </template>
        </div>
    </XdoModal>
</template>
<script>
    import { tobaccoUri } from '@/api'
    import { editStatus } from '@/view/tobacco-common/constant'
    import { decimalKeypress,decimalKeyup,numberKeypress,numberKeyup} from '@/libs/num'
    export default {
        props: {
            show: { type: Boolean, required: true },
            editConfig: { type: Object, default: () => {} }
        },
        data: function() {
            return {
                formItem: {
                    sid: '',
                    containerType: '',
                    unitPriceTotal: '',
                    containerCount: '',
                    amount: '',
                },
                ruleValidate: {
                            containerType: [
                                { required: true, message: '箱型（如20GP,40HQ等）不能为空！', trigger: 'blur' },
                                    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
                            ],
                            unitPriceTotal: [
                                { required: true, message: '单价合计不能为空！'},
                            ],
                            containerCount: [
                                { required: true, message: '箱数不能为空！'},
                            ],
                            amount: [
                            ],
                },
                buttons: [
                    {
                        code: 'Save',
                        type: 'primary',
                        disabled: false,
                        click: this.handleSave,
                        label: '保存',
                        loading: false,
                        needed: true
                    },
                    {
                        code: 'Cancel',
                        type: 'default',
                        disabled: false,
                        click: this.handleBack,
                        label: '关闭',
                        loading: false,
                        needed: true
                    }
                ],
                sid: '',
                title: '',
                showEditDialog: false,
                showDisable: false,
            }
        },
        mounted: function (){
        },
        methods: {
            getDefaultData() {
                return {
                    appId: '',
                    dbGroup: '',
                    description: '',
                    corpName: '',
                    corpCode: ''
                }
            },
            handleSave() {
                this.$refs['formItem'].validate().then(isValid => {
                    if (isValid) {
                        let http = ''
                        if (this.sid) {
                            http = this.$http.put(`${tobaccoUri.equipmentUrl.bizIEquipmentContainerInfo.update}/${this.sid}`, this.formItem, {noIntercept: true})
                        } else {
                            http = this.$http.post(tobaccoUri.equipmentUrl.bizIEquipmentContainerInfo.insert, this.formItem, {noIntercept: true})
                        }
                        http.then(res => {
                            if (res.data.success) {
                                this.$Message.success(res.data.message)
                                this.handleBack()
                            } else {
                                this.$Message.error(res.data.message)
                            }
                        }, () => {}).finally(() => {
                        })
                    }
                })
            },
            handleBack() {
                this.getDefaultData()
                this.$emit('update:show',false)
            },
            decimalKeypress,decimalKeyup,numberKeypress,numberKeyup
        },
        watch: {
            show: {
                immediate: true,
                handler: function(val) {
                    this.showEditDialog = val
                    if (val) {
                        if (this.editConfig.editStatus === editStatus.ADD) {
                            this.formItem = this.getDefaultData()
                            this.sid = ''
                            this.title = '新增'
                            this.showDisable = false
                            this.buttons.filter(item => item.code === "Save")[0].needed = true
                        }
                        if (this.editConfig.editStatus === editStatus.EDIT) {
                            this.formItem  = this.editConfig.editData
                            this.sid = this.editConfig.headId
                            this.title = '修改'
                            this.showDisable = false
                            this.buttons.filter(item => item.code === "Save")[0].needed = true
                        }
                        if (this.editConfig.editStatus === editStatus.SHOW) {
                            this.formItem  = this.editConfig.editData
                            this.sid = this.editConfig.headId
                            this.title = '查看'
                            this.showDisable = true
                            this.buttons.filter(item => item.code === "Save")[0].needed = false
                        }
                    }
                }
            }
        }
    }
</script>
<style lang="less" scoped>
</style>
