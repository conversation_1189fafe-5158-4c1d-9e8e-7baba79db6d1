<template>
  <section  class="dc-section">
    <div class="cs-action-container"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search" ref="searchArea">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  查询
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <!-- 查询按钮组件 -->
          <div ref="area_search">
            <div v-show="showSearch">
              <biz-incoming-goods-head-search ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming-list:add']">
            <a-button size="small" @click="handlerAdd" >
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              新增
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming-list:edit']">
            <a-button  size="small"  @click="handlerEdit">
              <template #icon>
                <GlobalIcon type="form" style="color:orange"/>
              </template>
              编辑
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming-list:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              删除
            </a-button>
          </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming:confirm']">
          <a-button size="small" :loading="confirmLoading" @click="handlerConfirm">
            <template #icon>
              <GlobalIcon type="check" style="color:green"/>
            </template>
            确认
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming:send-entry']">
          <a-button size="small" :loading="sendEntryLoading" @click="handlerSendEntry">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue"/>
            </template>
            发送报关
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming:audit']">
          <a-button size="small" :loading="auditLoading" @click="handlerAudit">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue"/>
            </template>
            发送审核
          </a-button>
        </div>
          <!-- 作废数据 -->
          <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming-list:cancel']">
            <a-button size="small"  @click="handlerCancel" :loading="cancelLoading">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              作废
            </a-button>
          </div>


        <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming-list:export']">
          <a-button  size="small" :loading="exportLoading" @click="handlerExport">
            <template #icon>
              <GlobalIcon type="folder-open" style="color:orange"/>
            </template>
            {{localeContent('m.common.button.export')}}
          </a-button>
        </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-suppler_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>


      </div>

      <!-- 表格区域 -->
      <div  v-if="showColumns && showColumns.length > 0"  ref="containerRef">
        <s-table
          ref="tableRef"
          class="cs-action-item remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight, x: 400 }"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          :custom-row="customRow"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
          <!-- 操作 -->
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleEditByRow(record)"
                    :style="operationEdit('edit')"
                  >
                    <template #icon>
                      <GlobalIcon type="form" style="color:#e93f41"/>
                    </template>
                  </a-button>
                </div>

                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleViewByRow(record)"
                    :style="operationEdit('view')"
                  >
                    <template #icon>
                      <GlobalIcon type="search" style="color:#1677ff"/>
                    </template>
                  </a-button>
                </div>


              </div>
            </template>
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination  v-if="showColumns && showColumns.length > 0" ref="paginationRef">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <biz-incoming-goods-head-tab :editConfig="editConfig" @onPageChange="onPageChange"  @on-edit-back="handlerOnBack"/>
    </div>


    <!-- 新增弹框 -->
    <!-- 新增合同号 -->
    <cs-modal :visible="isShowExtract"   title="新增进货单"   :width="1000" :footer="false" @cancel="handlerBackExtract">
      <template #customContent>
        <add-incoming-dialog ref="contractModalRef" @cancel="handlerBackExtract" @save="handlerExtract"></add-incoming-dialog>
      </template>
    </cs-modal>

  </section>


</template>

<script setup>

  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {getColumns} from "@/view/nonAuxiliaryMaterials/incoming/head/BizIncomingGoodsHeadColumns";
  import {message, Modal} from "ant-design-vue";
  import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  const  { totalColumns } = getColumns()
  import {localeContent} from "@/view/utils/commonUtil";
  import { useImport } from "@/view/common/useImport";
  import ycCsApi from "@/api/ycCsApi";
  import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
  import {useRoute} from "vue-router";
  import {editStatus} from "@/view/common/constant";
  import BizIncomingGoodsHeadSearch from "@/view/nonAuxiliaryMaterials/incoming/head/BizIncomingGoodsHeadSearch.vue";
  const  { importConfig } = useImport()


  import {createVNode, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
  import {deepClone} from "@/view/utils/common";

  /* 引入通用方法 */
  const {
    editConfig,
    show,
    page,
    showSearch,
    headSearch,
    handleViewByRow,
    operationEdit,
    onPageChange,
    handleShowSearch,
    handlerSearch,
    dataSourceList,
    tableLoading,
    getTableScroll,
    exportLoading,
    getList,
    ajaxUrl,
    doExport,
    handlerRefresh,
    getSearchParams

  } = useCommon()



  defineOptions({
    name: 'BizNonIncomingGoodsHeadList',
  });



  const importShow = ref(false)



  onMounted(fn => {


    ajaxUrl.selectAllPage = ycCsApi.bizNonInComingHead.list
    ajaxUrl.exportUrl = ycCsApi.bizNonInComingHead.export

    getList()


    // 自定义显示列信息
    initCustomColumn()


  })

  const tableHeight = ref('')

  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData:[],
    loading: false,
  });


  /* 表格列编辑 */
  const handleEditByRow = (row)=> {
    // 在这里添加处理编辑行的逻辑
    show.value = !show.value
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData = row
  }



  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };


  /* 按钮loading */
  const deleteLoading = ref(false)
  const confirmLoading = ref(false)
  const sendEntryLoading = ref(false)
  const auditLoading = ref(false)


  /* 返回事件 */
  const handlerOnBack = (flag) => {
    show.value = !show.value;
    // 返回清空选择数据
    gridData.selectedData = [];
    gridData.selectedRowKeys = [];
    editConfig.editData = {}
    if (flag){
      getList()
    }
  }

  /* 新增数据 */
  const addLoading = ref(false)
  const handlerAdd = ()=>{
    // editConfig.value.editStatus = editStatus.ADD
    // show.value = !show.value;
    // 大
    handlerSelectContract()
  }

  /* 弹框 */
  // 打开提取合同弹框
  // 是否显示新增合同号
  const isShowExtract = ref(false)
  const handlerSelectContract =  () => {

    isShowExtract.value = true

  }

  // 关闭提取合同弹框
  const handlerBackExtract = () => {
    isShowExtract.value = false
  }


  // 保存提取合同
  const handlerExtract = (data) => {
    // console.log('data',data)
    addLoading.value = true
    // 刷新表格数据
    // handlerSearch()
    window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.extractContract,data).then((res)=>{
      // console.log('res',res)

      if (res.code !== 200){
        message.error(res.message)
        return
      }else {
        getList()
        // // 提取完成后，重新刷新数据，
        // Object.assign(formData, res.data.head)
        editConfig.value.editStatus = editStatus.EDIT
        editConfig.value.editData =  res.data.head

        show.value =!show.value;

        // // 通过Bus发送事件，刷新表体数据
        // emitEvent('refreshIncomingGoodsList',res.data.head.id)
        // handlerOnBack({
        //   editData: null,
        //   showBody: true,
        //   editStatus: editStatus.EDIT,
        //   showBodyReceiptSell:false
        // })
      }


    }).finally(()=>{
      addLoading.value = false
      // 关闭提取合同弹框
      isShowExtract.value = false
    })
  }











  /* 编辑数据 */
  const handlerEdit = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1){
      message.warning('只允许操作一票数据')
      return
    }
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData =  gridData.selectedData[0]
    console.log('editConfig.value.editData',gridData)

    show.value =!show.value;
  }


  /* 作废数据  */
  const cancelLoading = ref(false)
  const handlerCancel = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1){
      message.warning('只允许操作一票数据')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '作废',
      cancelText: '取消',
      content: '确认作废所选项吗？',
      onOk() {
        cancelLoading.value = true
        let params = {
          id: gridData.selectedData[0].id,
          dataState: 1
        }
        window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.cancel,params).then(res => {
          if (res.code === 200) {
            message.success("作废成功！")
            getList()
          }else{
            message.error(res.message)
          }
        }).finally(() => {
          cancelLoading.value = false
        })
      },
      onCancel() {

      },
    })
  }

  /* 确认功能 */
  const handlerConfirm = () => {
    // 1）只可选择一行数据操作，如选择多行，则提示用户只可操作一行数据
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1) {
      message.warning('只可操作一行数据')
      return
    }

    // 2）单据状态为"编制"数据可以操作确认，如不符合，则提示用户"数据条件不符，请重新选择"
    if (gridData.selectedData[0].dataState !== '0') {
      message.warning('数据条件不符，请重新选择')
      return
    }

    // 弹出确认框
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: '确认执行此操作吗？',
      onOk() {
        confirmLoading.value = true
        let params = {
          id: gridData.selectedData[0].id
        }
        window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.confirmIncomingGoods, params).then(res => {
          if (res.code === 200) {
            message.success("确认成功！")
            // 确认成功后刷新列表数据
            getList()
          } else {
            message.error(res.message)
          }
        }).finally(() => {
          confirmLoading.value = false
        })
      },
      onCancel() {
        // 点击取消则关闭系统弹框，不执行确认操作
      },
    })
  }

  /* 发送报关  */
  const handlerSendEntry = () => {
    // 1）只可选择一行数据操作，如选择多行，则提示用户只可操作一行数据
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1) {
      message.warning('只可操作一行数据')
      return
    }

    // 2）单据状态为"确认"数据可以操作发送，如不符合，则提示用户"数据条件不符，请重新选择"
    if (gridData.selectedData[0].dataState !== '1') {
      message.warning('数据条件不符，请重新选择')
      return
    }

    // 获取选中数据的发送报关字段值
    const selectedRecord = gridData.selectedData[0]
    const sendEntryValue = selectedRecord.sendEntry
    let messageContent = ''
    if (sendEntryValue === '1') {
      messageContent = '是否将数据发送关务系统？'
    } else {
      messageContent = '是否将数据发送关务系统？'
    }
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: messageContent,
      onOk() {
        sendEntryLoading.value = true
        let params = {
          id: selectedRecord.id
        }
        window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.sendEntry, params).then(res => {
          debugger
          if (res.success === true) {
            message.success("发送成功！")
            // 发送成功后刷新列表数据
            getList()
          } else {
            message.error(res.data)
          }
        }).finally(() => {
          sendEntryLoading.value = false
        })
      },
      onCancel() {
        // 点击取消则关闭系统弹框，不执行发送及不修改相关栏位值
      },
    })
  }

  /* 删除数据 */
  const handlerDelete = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    // 只允许操作一票数据
    if (gridData.selectedRowKeys.length > 1){
      message.warning('只允许操作一票数据')
      return
    }
    // 只能选择状态为0的数据
    // if (gridData.selectedData[0].dataState !== '0'){
    //   message.warning('仅编制状态数据允许删除')
    //   return
    // }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk() {
        deleteLoading.value = true
        window.majesty.httpUtil.deleteAction(`${ycCsApi.bizNonInComingHead.delete}/${gridData.selectedRowKeys[0]}`).then(res => {
          if (res.code === 200) {
            message.success("删除成功！")
            getList()
          } else {
            message.error(res.message)
          }
        }).finally(() => {
          deleteLoading.value = false
        })
      },
      onCancel() {

      },
    });

  }


  // 退单
  const returnOrderLoading  = ref(false)
  const handlerReturnOrder = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1){
      message.warning('只允许操作一票数据')
      return
    }
    // 只能选择状态为0的数据
    if (gridData.selectedData[0].sellStatus === '-1'){
      message.warning('未生成销售信息，无法退单')
      return
    }
    // 只能选择状态为0的数据
    if (gridData.selectedData[0].sellStatus === '0'){
      message.warning('编制状态数据，无需退单')
      return
    }
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '退单',
      cancelText: '取消',
      content: '确认退单所选项吗？',
      onOk() {
        returnOrderLoading.value = true
        let params = {
          id: gridData.selectedData[0].id,
          dataState: 2
        }
        window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.returnOrder,params).then(res => {
          if (res.code === 200) {
            message.success("退单成功！")
            getList()
          }else{
            message.error(res.message)
          }
        }).finally(() => {
          returnOrderLoading.value = false
        })
      }
    })
  }





  const redFlushLoading = ref(false)
  const handlerRedFlush = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1){
      message.warning('只允许操作一票数据')
      return
    }
    // 只能选择状态为0的数据
    if (gridData.selectedData[0].sellStatus === '-1'){
      message.warning('未生成销售信息，无法红冲')
      return
    }

    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '红冲',
      cancelText: '取消',
      content: '是否将该票数据置为红冲？',
      onOk() {
        redFlushLoading.value = true
        let params = {
          id: gridData.selectedData[0].id,
          dataState: 2
        }
        window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.redFlush,params).then(res => {
          if (res.code === 200) {
            message.success("红冲成功！")
            getList()
          }else{
            message.error(res.message)
          }
        }).finally(() => {
          redFlushLoading.value = false
        })
      }
    })

  }


  /* 导出事件 */
  const handlerExport = () =>{
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
    doExport(`进货信息表头导出${timestamp}.xlsx`, totalColumns)
  }





  /* ====================================== 自定义显示列信息 ====================================== */
  /* 自定义设置 */
  const showColumns =  ref([])

  /* 唯一键 */
  const tableKey = ref('')
  tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
  const originalColumns = ref()


  /* 自定义显示列初始化操作 */
  const initCustomColumn = () => {
    // 这里是拷贝是属于
    let tempColumns = deepClone(totalColumns.value)
    let dealColumns = []
    // 使用map遍历会丢失customRender方法，所以使用forEach
    tempColumns.map((item) => {
      let newObj = Object.assign({}, item);
      newObj["visible"] = true;
      // 需要将customRender 方法追加到新对象中
      if (item.customRender) {
        newObj["customRender"] = item.customRender;
      }
      dealColumns.push(newObj);
    });
    //原始列信息
    originalColumns.value = dealColumns;
  }


  /* 选中visible为true的数据进行显示 */
  const customColumnChange = (settingColumns)  => {
    totalColumns.value = settingColumns.filter((item) => item.visible === true);
    showColumns.value = [...totalColumns.value]
  }

  /* 监控 dataSourceList */
  watch(totalColumns.value, (newValue, oldValue) => {
    if(!window.$vueApp){
      showColumns.value = [...totalColumns.value];
    }else {
      // console.log('newValue',newValue)
      // console.log('[...totalColumns]', [...totalColumns.value])
      // console.log('[totalColumns]', totalColumns.value)
      if (newValue.length === 0) {
        showColumns.value = [...totalColumns.value];
      }else {
        showColumns.value = newValue.map((item) => {
          item.visible = true;
          return item;
        })
        totalColumns.value = newValue.map((item) => {
          item.visible = true;
          return item;
        })
        // console.log('totalColumns.value', totalColumns.value)
      }
    }
  },{immediate:true,deep:true})



  /* ================================== 自定义表格高度 ================================== */
  import { useTableScrollY } from '@/view/utils/useTableScrollY';
  import BizIncomingGoodsHeadTab from "@/view/nonAuxiliaryMaterials/incoming/BizIncomingGoodsHeadTab.vue";
  import {GlobalIcon} from "@/components/icon";
  import CsModal from "@/components/modal/cs-modal.vue";
  import AddIncomingDialog from "@/view/nonAuxiliaryMaterials/incoming/componment/AddIncomingDialog.vue";
  const { containerRef, paginationRef, tableScrollY } = useTableScrollY([showSearch, dataSourceList]);




  /* =================================== 双击进入表格 ================================== */
  // 自定义行属性
  const customRow = (record) => {
    return {
      onDblclick: () => {
        handleRowDblclick(record);
      },
      style: {
        cursor: 'pointer'
      }
    };
  };
  const handleRowDblclick = (record) => {
    // 仅数据状态为0编制的允许操作删除。对状态不符的，提示：仅编制状态数据允许删除。
    // if(record.dataStatus!== '0' &&
    //    record.purchaseDataStatus !== '0' &&
    //    record.salesDataStatus !== '0' &&
    //    record.inboundReceiptStatus !== '0' &&
    //    record.outboundReceiptStatus !== '0'){
    //   message.warning('仅状态为0编制的数据可以操作编辑!')
    //   return
    // }
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData =  record

    show.value =!show.value;
  };

</script>

<style lang="less" scoped>


</style>
