import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }

  // 转换为数字并检查有效性
  const number = Number(value);
  if (isNaN(number)) {
    return '';
  }

  // 配置 NumberFormat 选项
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,  // 至少两位小数
    maximumFractionDigits: 10
  }).format(number);
};
export function getColumns(editData) {


  const commColumns = reactive([
    'contractNo',
    'orderNumber',
    // 'orderNo',
    'goodsName',
    'invoiceNumber',
    'qty',
    'unit',
    'payAmt',
    'payAmtRmb',
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    // {
    //   width: 80,
    //   minWidth:80,
    //   title: '操作',
    //   dataIndex: 'operation',
    //   resizable: true,
    //   key: 'operation',
    //   align: 'center',
    //   fixed: 'left',
    // },
    {
      title: '合同号',
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo',
    },
    {
      title: '进货单号',
      align: 'center',
      dataIndex: 'orderNumber',
      resizable: true,
      key: 'orderNumber',
    },
    // {
    //   title: '订单号',
    //   width: '14.28%',
    //   align: 'center',
    //   dataIndex: 'orderNo',
    //   resizable: true,
    //   key: 'orderNo',
    // },
    {
      title: '商品名称',
      align: 'center',
      dataIndex: 'goodsName',
      resizable: true,
      key: 'goodsName',
    },
    {
      title: '发票号',
      align: 'center',
      dataIndex: 'invoiceNumber',
      resizable: true,
      key: 'invoiceNumber',
      editable:()=> editData.bizType === '3'?'cellEditorSlot':false
    },
    {
      title: '数量',
      align: 'center',
      dataIndex: 'qty',
      resizable: true,
      editable: 'cellEditorSlot',
      key: 'qty',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '单位',
      align: 'center',
      dataIndex: 'unit',
      resizable: true,
      key: 'unit',
    },
    // {
    //   title: '金额',
    //   width: '14.28%',
    //   align: 'center',
    //   dataIndex: 'payAmt',
    //   resizable: true,
    //   key: 'payAmt',
    //   customRender: ({ text }) => {
    //     return formatNumber(text);
    //   }
    // },
    {
      title: 'RMB金额',
      align: 'center',
      dataIndex: 'payAmtRmb',
      resizable: true,
      key: 'payAmtRmb',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },

  ])


  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


