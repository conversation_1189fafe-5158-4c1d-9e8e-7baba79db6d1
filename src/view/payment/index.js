import { defineAsyncComponent } from "vue"

export default  [
  {
    path: '/tobacco/payment/notify',
    name: 'notify',
    meta: {
      title: '付款通知'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./notify/NotifyHeadList.vue"))
  },
  {
    path: '/tobacco/payment/settlement',
    name: 'settlement',
    meta: {
      title: '货款结算'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./settlement/SettlementHeadList.vue"))
  },
  {
    path: '/tobacco/payment/registration',
    name: 'registration',
    meta: {
      title: '收款登记'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./registration/RegistrationList.vue"))
  },
  {
    path: '/tobacco/payment/customerAccount',
    name: 'customerAccount',
    meta: {
      title: '客户结算'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./customerAccount/CustomerAccountList.vue"))
  },
  {
    path: '/tobacco/payment/customerAccountTobacoo',
    name: 'customerAccountTobacoo',
    meta: {
      title: '结算单'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./customerAccountTobacoo/BizCustomerAccountTobacooList.vue"))
  },
  {
    path: '/tobacco/payment/customerAccountSummary',
    name: 'customerAccountSummary',
    meta: {
      title: '总结算单'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./customerAccountSummary/BizCustomerAccountSummaryList.vue"))
  },
  {
    path: '/tobacco/payment/customerAccountSlice',
    name: 'customerAccountSlice',
    meta: {
      title: '客户结算单'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./customerAccountSlice/BizCustomerAccountSliceList.vue"))
  },
]
