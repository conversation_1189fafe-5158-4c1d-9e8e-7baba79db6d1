<template>
  <section>
    <a-card size="small" title="xxx" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="businessType" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('businessType')"
                  @click="handleLabelClick('businessType')"
              >
                业务类型
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.businessType"></a-input>
          </a-form-item>
          <a-form-item name="accountNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('accountNo')"
                  @click="handleLabelClick('accountNo')"
              >
                结算单号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.accountNo"></a-input>
          </a-form-item>
          <a-form-item name="contractNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('contractNo')"
                  @click="handleLabelClick('contractNo')"
              >
                合同号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="currE" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('currE')"
                  @click="handleLabelClick('currE')"
              >
                出口币种
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.currE"></a-input>
          </a-form-item>
          <a-form-item name="currI" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('currI')"
                  @click="handleLabelClick('currI')"
              >
                进口币种
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.currI"></a-input>
          </a-form-item>
          <a-form-item name="exchangeRateE" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('exchangeRateE')"
                  @click="handleLabelClick('exchangeRateE')"
              >
                出口汇率
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.exchangeRateE" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="exchangeRateI" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('exchangeRateI')"
                  @click="handleLabelClick('exchangeRateI')"
              >
                进口汇率
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.exchangeRateI" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="goodsPriceE" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('goodsPriceE')"
                  @click="handleLabelClick('goodsPriceE')"
              >
                出口货款
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.goodsPriceE" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="goodsPriceI" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('goodsPriceI')"
                  @click="handleLabelClick('goodsPriceI')"
              >
                进口货款
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.goodsPriceI" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="businessDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('businessDate')"
                  @click="handleLabelClick('businessDate')"
              >
                结算日期
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.businessDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="gName" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('gName')"
                  @click="handleLabelClick('gName')"
              >
                商品名称
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.gName"></a-input>
          </a-form-item>
          <a-form-item name="sendFinance" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('sendFinance')"
                  @click="handleLabelClick('sendFinance')"
              >
                发送财务系统
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.sendFinance"></a-input>
          </a-form-item>
          <a-form-item name="status" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('status')"
                  @click="handleLabelClick('status')"
              >
                单据状态
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.status"></a-input>
          </a-form-item>
          <a-form-item name="confirmTime" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('confirmTime')"
                  @click="handleLabelClick('confirmTime')"
              >
                确认时间
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.confirmTime" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="goodsPriceERmb" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('goodsPriceERmb')"
                  @click="handleLabelClick('goodsPriceERmb')"
              >
                出口货款(人民币)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.goodsPriceERmb" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="goodsPriceIRmb" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('goodsPriceIRmb')"
                  @click="handleLabelClick('goodsPriceIRmb')"
              >
                进口货款(人民币)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.goodsPriceIRmb" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="totalAmount" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('totalAmount')"
                  @click="handleLabelClick('totalAmount')"
              >
                共计金额(人民币)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.totalAmount" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="customer" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('customer')"
                  @click="handleLabelClick('customer')"
              >
                客户
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.customer"></a-input>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'sid':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'accountNo':'结算单号'
, 'contractNo':'合同号'
, 'currE':'出口币种'
, 'currI':'进口币种'
, 'exchangeRateE':'出口汇率'
, 'exchangeRateI':'进口汇率'
, 'goodsPriceE':'出口货款'
, 'goodsPriceI':'进口货款'
, 'agentFeeRate':'中烟代理费率%'
, 'agentFee':'中烟代理费（不含税）'
, 'agentTaxFee':'中烟代理费税额'
, 'agentFeeTotal':'中烟代理费（价税合计）'
, 'businessDate':'结算日期'
, 'gName':'商品名称'
, 'sendFinance':'发送财务系统'
, 'producrSome':'商品与数量'
, 'note':'备注'
, 'freightRatio':'货款比例'
, 'redFlush':'是否红冲'
, 'status':'单据状态'
, 'apprStatus':'审核状态'
, 'confirmTime':'确认时间'
, 'isConfirm':'是否确认'
, 'purchaseMark':'外商合同、进货明细数据标记'
, 'purchaseNoMark':'外商合同、进货明细数据标记'
, 'goodsPriceERmb':'出口货款(人民币)'
, 'goodsPriceIRmb':'进口货款(人民币)'
, 'vatRate':'增值税率%'
, 'freightForwardingFee':'货代费'
, 'insuranceFee':'保险费'
, 'costFee':'已结算款项合计'
, 'depositReceived':'实际预收款'
, 'refundFee':'应退款项'
, 'totalAmount':'共计金额(人民币)'
, 'customer':'客户'
, 'businessLocation':'业务地点'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid:''
,businessType: ''
,accountNo: ''
,contractNo: ''
,currE: ''
,currI: ''
,exchangeRateE: ''
,exchangeRateI: ''
,goodsPriceE: ''
,goodsPriceI: ''
,businessDate: ''
,gName: ''
,sendFinance: ''
,status: ''
,confirmTime: ''
,goodsPriceERmb: ''
,goodsPriceIRmb: ''
,totalAmount: ''
,customer: ''
})
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})
/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }
  // 审核意见输入框
  const auditOpinion = ref('同意审批')
  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'xxx',
      }
      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.customerAccount.bizCustomerAccountSlice.audit, params)
          .then(res => {
            if (res.code === 200) {
              message.success("审核通过成功！")
              // 返回列表页面
              onBack(true)
            } else {
              message.error(res.message || '审核失败')
            }
          })
          .catch(error => {
            console.error('审核失败:', error)
            message.error('审核失败，请重试')
          })
          .finally(() => {
            auditLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }
  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'contract',
      }
      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.customerAccount.bizCustomerAccountSlice.reject, params)
          .then(res => {
            if (res.code === 200) {
              // 审核退回成功后，调用标记保存接口
              return saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
            } else {
              throw new Error(res.message || '审核退回失败')
            }
          })
          .then(res => {
            message.success("审核退回成功！")
            // 返回列表页面
            onBack(true)
          })
          .catch(error => {
            console.error('审核退回失败:', error)
            message.error(error.message || '审核退回失败，请重试')
          })
          .finally(() => {
            invalidLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
</script>
<style lang="less" scoped>
</style>
