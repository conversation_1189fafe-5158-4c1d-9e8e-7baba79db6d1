<template>
  <section>
    <a-card size="small" title="客户结算单表头" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType2"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="purchaseOrderNo" :label="'出货单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.purchaseOrderNo"></a-input>
          </a-form-item>
          <a-form-item name="accountNo" :label="'结算单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.accountNo"></a-input>
          </a-form-item>
          <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.customer" id="customer">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="goodsPriceE" :label="'出口货款'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                                          if (!value) return '';
                                          const parts = value.toString().split('.');
                                          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                          return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.goodsPriceE" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="currE" :label="'出口币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.currE"
              :disabled="true"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
            ></a-select>
          </a-form-item>
          <a-form-item name="exchangeRateE" :label="'出口汇率'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable  || formData.status  !== '0' " size="small"
                            :formatter="value => {
                                          if (!value) return '';
                                          const parts = value.toString().split('.');
                                          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                          return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="EChange"
                            v-model:value="formData.exchangeRateE" notConvertNumber decimal int-length="13" precision="6"/>
          </a-form-item>
          <a-form-item name="goodsPriceERmb" :label="'出口货款(人民币)'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable  || formData.status  !== '0' " size="small"
                            :formatter="value => {
                                          if (!value) return '';
                                          const parts = value.toString().split('.');
                                          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                          return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.goodsPriceERmb" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="goodsPriceI" :label="'进口货款'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                                          if (!value) return '';
                                          const parts = value.toString().split('.');
                                          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                          return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.goodsPriceI" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="currI" :label="'进口币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.currI"
              :disabled="true"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
            ></a-select>
          </a-form-item>
          <a-form-item name="exchangeRateI" :label="'进口汇率'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable  || formData.status  !== '0' " size="small"
                            :formatter="value => {
                                          if (!value) return '';
                                          const parts = value.toString().split('.');
                                          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                          return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="IChange"
                            v-model:value="formData.exchangeRateI" notConvertNumber decimal int-length="13" precision="6"/>
          </a-form-item>
          <a-form-item name="goodsPriceIRmb" :label="'进口货款(人民币)'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable  || formData.status  !== '0' " size="small"
                            :formatter="value => {
                                          if (!value) return '';
                                          const parts = value.toString().split('.');
                                          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                          return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.goodsPriceIRmb" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="agentFeeRate" :label="'代理费率%'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable  || formData.status  !== '0' " size="small" v-model:value="formData.agentFeeRate"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
          <a-form-item name="totalAmount" :label="'共计金额(人民币)'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable  || formData.status  !== '0' " size="small"
                            :formatter="value => {
                                          if (!value) return '';
                                          const parts = value.toString().split('.');
                                          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                          return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.totalAmount" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>

          <a-form-item name="businessDate" :label="'结算日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable  || formData.status  !== '0' "
              v-model:value="formData.businessDate"
              id="businessDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="sendFinance" :label="'发送财务系统'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' "  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendFinance" id="destinationPort">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="createrUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createrUserName"/>
          </a-form-item>
          <!--          制单时间-->
          <a-form-item name="createrTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createrTime"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="status" :label="'单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import {getOrderSupplierList} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
import useEventBus from "@/view/common/eventBus";
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'sid':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'accountNo':'结算单号'
, 'contractNo':'合同号'
, 'currE':'出口币种'
, 'currI':'进口币种'
, 'exchangeRateE':'出口汇率'
, 'exchangeRateI':'进口汇率'
, 'goodsPriceE':'出口货款'
, 'goodsPriceI':'进口货款'
, 'agentFeeRate':'中烟代理费率%'
, 'agentFee':'中烟代理费（不含税）'
, 'agentTaxFee':'中烟代理费税额'
, 'agentFeeTotal':'中烟代理费（价税合计）'
, 'businessDate':'结算日期'
, 'gName':'商品名称'
, 'sendFinance':'发送财务系统'
, 'producrSome':'商品与数量'
, 'note':'备注'
, 'freightRatio':'货款比例'
, 'redFlush':'是否红冲'
, 'status':'单据状态'
, 'apprStatus':'审核状态'
, 'confirmTime':'确认时间'
, 'isConfirm':'是否确认'
, 'purchaseMark':'外商合同、进货明细数据标记'
, 'purchaseNoMark':'外商合同、进货明细数据标记'
, 'goodsPriceERmb':'出口货款(人民币)'
, 'goodsPriceIRmb':'进口货款(人民币)'
, 'vatRate':'增值税率%'
, 'freightForwardingFee':'货代费'
, 'insuranceFee':'保险费'
, 'costFee':'已结算款项合计'
, 'depositReceived':'实际预收款'
, 'refundFee':'应退款项'
, 'totalAmount':'共计金额(人民币)'
, 'customer':'客户'
, 'businessLocation':'业务地点'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid: ''
,businessType: ''
,accountNo: ''
,contractNo: ''
,currE: ''
,currI: ''
,exchangeRateE: ''
,exchangeRateI: ''
,goodsPriceE: ''
,goodsPriceI: ''
,businessDate: ''
,agentFeeRate: '3'
,sendFinance: ''
,status: ''
,confirmTime: ''
,goodsPriceERmb: ''
,goodsPriceIRmb: ''
,totalAmount: ''
,customer: ''
})
// 表单校验规则
const rules = {
businessType: [
  { required: true, message: '业务类型不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
accountNo: [
  { required: true, message: '结算单号不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
contractNo: [
  { required: true, message: '合同号不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
  purchaseOrderNo: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
currE: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
currI: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
exchangeRateE: [
  { required: true, message: '出口汇率不能为空！', trigger: 'blur' },
],
exchangeRateI: [
  { required: true, message: '进口汇率不能为空！', trigger: 'blur' },
],
goodsPriceE: [
  { required: true, message: '出口货款不能为空！', trigger: 'blur' },
],
goodsPriceI: [
  { required: true, message: '进口货款不能为空！', trigger: 'blur' },
],
businessDate: [
],
  agentFeeRate: [
],
sendFinance: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
status: [
  { required: true, message: '单据状态不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
confirmTime: [
],
goodsPriceERmb: [
  { required: true, message: '出口货款(人民币)不能为空！', trigger: 'blur' },
],
goodsPriceIRmb: [
  { required: true, message: '进口货款(人民币)不能为空！', trigger: 'blur' },
],
totalAmount: [
],
customer: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
}
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
const {emitEvent} = useEventBus()
// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      window.majesty.httpUtil.putAction(`${ycCsApi.payment.bizCustomerAccountSlice.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      window.majesty.httpUtil.putAction(`${ycCsApi.payment.bizCustomerAccountSlice.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
          Object.assign(formData, res.data)
          formData.createrUserName = res.data.updateUserName
          formData.createrTime = res.data.updateTime
          onBack({
            editData: res.data,
            showBody: true,
            editStatus: editStatus.EDIT
          })
          emitEvent('refreshOrderList')
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
const EChange = ()　=>{
  if(formData.goodsPriceERmb !== null){
    delete formData.goodsPriceERmb;
  }
  if(formData.goodsPriceE !== null && formData.exchangeRateE !== null){
    formData.goodsPriceERmb = eCount(formData)
  }
}
const eCount = (row) => {
  const goodsPriceE = parseFloat(row.goodsPriceE);
  const exchangeRateE = parseFloat(row.exchangeRateE);
  const goodsPriceERmb = roundToDecimal(exchangeRateE*goodsPriceE,2)
  return goodsPriceERmb !== null ? goodsPriceERmb : null
};
const IChange = ()　=>{
  if(formData.goodsPriceIRmb !== null){
    delete formData.goodsPriceIRmb;
  }
  if(formData.goodsPriceI !== null && formData.exchangeRateI !== null){
    formData.goodsPriceIRmb = iCount(formData)
  }
}
const iCount = (row) => {
  const goodsPriceI = parseFloat(row.goodsPriceI);
  const exchangeRateI = parseFloat(row.exchangeRateI);
  const goodsPriceIRmb = roundToDecimal(goodsPriceI*exchangeRateI,2)
  return goodsPriceIRmb !== null ? goodsPriceIRmb : null
};
function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}
const supplierList = ref([])
const currMap = ref([])
const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getSupplierList();
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      value
    }));
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})
</script>
<style lang="less" scoped>
</style>
