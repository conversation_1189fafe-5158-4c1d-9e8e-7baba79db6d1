<template>
  <section>
    <a-card size="small" title="投保信息" class="cs-card-form">
      <div v-if="formLoading" class="cs-form" style="height:38vh;display: flex;align-items: center;justify-content: center;">
        <a-spin tip="数据加载中..."/>
      </div>
      <div class="cs-form" v-else>
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"  :model="formData"   class=" grid-container">

            <!-- 编号 （取进货单号） -->
            <a-form-item name="codeNo"   :label="'编号'" class="grid-item"  :colon="false">
                <a-input :disabled="true"  size="small" v-model:value="formData.codeNo"  allow-clear/>
            </a-form-item>
            <!-- 保险公司 （客户档案，下拉选择）-->
            <a-form-item name="insuranceCompany"   :label="'保险公司'" class="grid-item"  :colon="false">
              <cs-select :disabled="showDisable || props.isAllConfirmed"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.insuranceCompany" id="insuranceCompany">
                <a-select-option class="cs-select-dropdown" v-for="item in customerList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                  {{item.value}} {{item.label }}
                </a-select-option>
              </cs-select>
            </a-form-item>
            <!-- 被保险人 （进货单带入，不可修改） -->
            <a-form-item name="insuredPerson"   :label="'被保险人'" class="grid-item"  :colon="false">
              <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.insuredPerson" id="insuredPerson">
                <a-select-option class="cs-select-dropdown" v-for="item in customerList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                  {{item.value}} {{item.label }}
                </a-select-option>
              </cs-select>
            </a-form-item>
            <!-- 发票抬头 （企业档案，下拉选择） -->
            <a-form-item name="invoiceTitle"   :label="'发票抬头'" class="grid-item"  :colon="false">
              <cs-select :disabled="showDisable || props.isAllConfirmed"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.invoiceTitle" id="invoiceTitle">
                <a-select-option class="cs-select-dropdown" v-for="item in customerList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                  {{item.value}} {{item.label }}
                </a-select-option>
              </cs-select>
            </a-form-item>
            <!-- 运输工具名称 -->
            <a-form-item name="transportName"   :label="'运输工具名称'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.transportName" allow-clear/>
            </a-form-item>
            <!-- 开航日期 -->
            <a-form-item name="departureDate"   :label="'开航日期'" class="grid-item"  :colon="false">
              <a-date-picker
                allow-clear
                :disabled="showDisable  || props.isAllConfirmed"
                v-model:value="formData.departureDate"
                id="departureDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>
            <!-- 运输路线自 -->
            <a-form-item name="routeFrom"   :label="'运输路线自'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.routeFrom" allow-clear/>
            </a-form-item>
            <!-- 经 -->
            <a-form-item name="routeVia"   :label="'经'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.routeVia" allow-clear/>
            </a-form-item>
            <!-- 至 -->
            <a-form-item name="routeTo"   :label="'至'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.routeTo"  allow-clear />
            </a-form-item>
            <!-- 投保险别 -->
            <a-form-item name="insuranceType"   :label="'投保险别'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.insuranceType"   allow-clear />
            </a-form-item>
            <!-- 币种 （外商合同带入）-->
            <a-form-item name="currency"   :label="'币种'" class="grid-item"  :colon="false">
              <cs-select :disabled="showDisable || props.isAllConfirmed"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.currency" id="currency">
                <a-select-option class="cs-select-dropdown" v-for="item in currList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                  {{item.value}} {{item.label }}
                </a-select-option>
              </cs-select>
            </a-form-item>
            <!-- 投保加成%  录入 -->
            <a-form-item name="insurancePremiumRate"   :label="'投保加成%'" class="grid-item"  :colon="false">
              <a-input-number :disabled="showDisable || props.isAllConfirmed"
                              size="small"
                              style="width: 100%"
                              addon-after="%"
                              v-model:value="formData.insurancePremiumRate"
                              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                              :parser="value => inputParser(value)"
                              @keydown.enter="() => {
                                handlerCalcPremium();
                              }"
              />
            </a-form-item>
            <!-- 保险金额 （取进货单表体金额合计*（1+投保加成%）） -->
            <a-form-item name="insuranceAmount"   :label="'保险金额'" class="grid-item"  :colon="false">
              <a-input-number :disabled="showDisable || props.isAllConfirmed"
                              size="small"
                              style="width: 100%"
                              v-model:value="formData.insuranceAmount"
                              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                              :parser="value => inputParser(value)"
                              @keydown.enter="() => {
                                handlerCalcPremium();
                              }"
              />
            </a-form-item>
            <!-- 保险费率  （根据外商合同关联划款参数带出，允许修改） -->
            <a-form-item name="insuranceRate"   :label="'保险费率'" class="grid-item"  :colon="false">
              <a-input-number :disabled="showDisable || props.isAllConfirmed"
                              size="small"
                              style="width: 100%"
                              v-model:value="formData.insuranceRate"
                              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                              :parser="value => inputParser(value)"
                              @keydown.enter="() => {
                                handlerCalcPremium();
                              }"
                              addon-after="%"

              />
            </a-form-item>
            <!-- 如果保险费率修改，可以计算出保费。保费修改，不要反算保险费率 -->
            <!-- 保费  （保费=保险金额*汇率*保险费率，允许修改）       保费费率 重新计算  -->
            <a-form-item name="premium"   :label="'保费'" class="grid-item"  :colon="false">
                <a-input-number :disabled="showDisable || props.isAllConfirmed"
                                 size="small"
                                style="width: 100%"
                                v-model:value="formData.premium"
                                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                                :parser="value => inputParser(value)"
                />
            </a-form-item>
            <!-- 投保日期 -->
            <a-form-item name="insuranceDate"   :label="'投保日期'" class="grid-item"  :colon="false">
              <a-date-picker
                allow-clear
                :disabled="showDisable  || props.isAllConfirmed"
                v-model:value="formData.insuranceDate"
                id="releaseDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>
            <!-- 备注 -->
            <a-form-item name="remark"   :label="'备注'" class="grid-item merge-3"  :colon="false">
              <a-textarea :disabled="showDisable || props.isAllConfirmed"
                          size="small"
                          allow-clear
                          v-model:value="formData.remark"
                          :autosize="{ minRows: 3, maxRows: 10 }" />
            </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      :loading="buttonLoadingMap.save"
                      v-if="!showDisable"
                      :disabled="props.isAllConfirmed"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="handlerBack(true)">返回</a-button>
            <div class="cs-action-btn-item" >
              <a-dropdown >
                <template #overlay>
                  <a-menu @click="handlerGenerateTB"  :disabled="props.isAllConfirmed">
                    <a-menu-item key="pdf">生成投保单(PDF)</a-menu-item>
                    <a-menu-item key="xlsx">生成投保单(XLSX)</a-menu-item>
                  </a-menu>
                </template>
                <a-button class="button"  size="small" type="ghost"
                          v-if="!showDisable"
                          :disabled="props.isAllConfirmed"
                          v-show="props.editConfig.editStatus !== 'SHOW' ">
                  打印
                  <template #icon>
                    <GlobalIcon type="cloud-download" style="color:blue"/>
                  </template>
                </a-button>
              </a-dropdown>
            </div>

          </div>

        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import ycCsApi from "@/api/ycCsApi";
const { getPCode } = usePCode()
import {useButtonLoading} from "@/view/utils/useBtnLoading";
const { setLoading,buttonLoadingMap } = useButtonLoading()
// 导入数值类型格式化方法
import { useColumnsRender} from "@/view/common/useColumnsRender";
import {isNullOrEmpty} from "@/view/utils/common";
import useEventBus from "@/view/common/eventBus";
import {GlobalIcon} from "@/components/icon";
const { inputFormatter, inputParser }= useColumnsRender()
const { onEvent }  = useEventBus()



const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId:{
    type:String,
    default:()=>''
  },
  operationStatus:{
    type:String,
    default:()=>''
  },
  /* 是否全部确认 */
  isAllConfirmed: {
    type: Boolean,
    default: () => false
  }
});

defineOptions({
  name: 'BizSmokeMachineIncomingGoodsTbEdit',
})

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const handlerBack = (val) => {
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
    // 主键ID
    id:'',
    // 业务类型
    businessType:'',
    // 数据状态
    dataState:'',
    // 版本号
    versionNo:'',
    // 企业10位编码
    tradeCode:'',
    // 组织机构代码
    sysOrgCode:'',
    // 父级ID
    parentId:'',
    // 创建人
    createBy:'',
    // 创建时间
    createTime:'',
    // 更新人
    updateBy:'',
    // 更新时间
    updateTime:'',
    // 插入用户名
    insertUserName:'',
    // 更新用户名
    updateUserName:'',
    // 扩展字段1
    extend1:'',
    // 扩展字段2
    extend2:'',
    // 扩展字段3
    extend3:'',
    // 扩展字段4
    extend4:'',
    // 扩展字段5
    extend5:'',
    // 扩展字段6
    extend6:'',
    // 扩展字段7
    extend7:'',
    // 扩展字段8
    extend8:'',
    // 扩展字段9
    extend9:'',
    // 扩展字段10
    extend10:'',
    // 表头ID
    headId:'',
    // 编号
    codeNo:'',
    // 保险公司
    insuranceCompany:'',
    // 被保险人
    insuredPerson:'',
    // 发票抬头
    invoiceTitle:'',
    // 运输工具名称
    transportName:'',
    // 开航日期
    departureDate:'',
    // 运输路线自
    routeFrom:'',
    // 经
    routeVia:'',
    // 至
    routeTo:'',
    // 投保险别
    insuranceType:'',
    // 币种
    currency:'',
    // 投保加成%
    insurancePremiumRate:'',
    // 保险金额
    insuranceAmount:'',
    // 保险费率
    insuranceRate:'',
    // 保费
    premium:'',
    // 投保日期
    insuranceDate:'',
    // 备注
    remark:''
})
// 校验规则
const rules = {
    id:[
        {max: 40, message: '主键ID长度不能超过 40位字节', trigger: 'blur'}
    ],
    businessType:[
        {max: 60, message: '业务类型长度不能超过 60位字节', trigger: 'blur'}
    ],
    dataState:[
        {max: 10, message: '数据状态长度不能超过 10位字节', trigger: 'blur'}
    ],
    versionNo:[
        {max: 10, message: '版本号长度不能超过 10位字节', trigger: 'blur'}
    ],
    tradeCode:[
        {max: 10, message: '企业10位编码长度不能超过 10位字节', trigger: 'blur'}
    ],
    sysOrgCode:[
        {max: 10, message: '组织机构代码长度不能超过 10位字节', trigger: 'blur'}
    ],
    parentId:[
        {max: 40, message: '父级ID长度不能超过 40位字节', trigger: 'blur'}
    ],
    createBy:[
        {max: 50, message: '创建人长度不能超过 50位字节', trigger: 'blur'}
    ],
    createTime:[
    ],
    updateBy:[
        {max: 50, message: '更新人长度不能超过 50位字节', trigger: 'blur'}
    ],
    updateTime:[
    ],
    insertUserName:[
        {max: 50, message: '插入用户名长度不能超过 50位字节', trigger: 'blur'}
    ],
    updateUserName:[
        {max: 50, message: '更新用户名长度不能超过 50位字节', trigger: 'blur'}
    ],
    extend1:[
        {max: 200, message: '扩展字段1长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend2:[
        {max: 200, message: '扩展字段2长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend3:[
        {max: 200, message: '扩展字段3长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend4:[
        {max: 200, message: '扩展字段4长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend5:[
        {max: 200, message: '扩展字段5长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend6:[
        {max: 200, message: '扩展字段6长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend7:[
        {max: 200, message: '扩展字段7长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend8:[
        {max: 200, message: '扩展字段8长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend9:[
        {max: 200, message: '扩展字段9长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend10:[
        {max: 200, message: '扩展字段10长度不能超过 200位字节', trigger: 'blur'}
    ],
    headId:[
        {max: 50, message: '表头ID长度不能超过 50位字节', trigger: 'blur'}
    ],
    codeNo:[
        {required: true, message: '编号不能为空', trigger: 'blur'},
        {max: 60, message: '编号长度不能超过 60位字节', trigger: 'blur'}
    ],
    insuranceCompany:[
        {required: true, message: '保险公司不能为空', trigger: 'blur'},
        {max: 200, message: '保险公司长度不能超过 200位字节', trigger: 'blur'}
    ],
    insuredPerson:[
        {max: 200, message: '被保险人长度不能超过 200位字节', trigger: 'blur'}
    ],
    invoiceTitle:[
        {max: 200, message: '发票抬头长度不能超过 200位字节', trigger: 'blur'}
    ],
    transportName:[
        {max: 200, message: '运输工具名称长度不能超过 200位字节', trigger: 'blur'}
    ],
    departureDate:[
    ],
    routeFrom:[
        {max: 200, message: '运输路线自长度不能超过 200位字节', trigger: 'blur'}
    ],
    routeVia:[
        {max: 200, message: '经长度不能超过 200位字节', trigger: 'blur'}
    ],
    routeTo:[
        {max: 200, message: '至长度不能超过 200位字节', trigger: 'blur'}
    ],
    insuranceType:[
        {max: 200, message: '投保险别长度不能超过 200位字节', trigger: 'blur'}
    ],
    currency:[
        {required: true, message: '币种不能为空', trigger: 'blur'},
        {max: 10, message: '币种长度不能超过 10位字节', trigger: 'blur'}
    ],
    insurancePremiumRate:[
        { type: 'number', message: '投保加成%不是有效的数字!'},
    ],
    insuranceAmount:[
        {required: true, message: '保险金额不能为空', trigger: 'blur'},
        { type: 'number', message: '保险金额不是有效的数字!'},
    ],
    insuranceRate:[
        {required: true, message: '保险费率不能为空', trigger: 'blur'},
        { type: 'number', message: '保险费率不是有效的数字!'},
    ],
    premium:[
         // {required: true, message: '保费不能为空', trigger: 'blur'},
        { type: 'number', message: '保费不是有效的数字!'},
    ],
    insuranceDate:[
    ],
    remark:[
        {max: 500, message: '备注长度不能超过 500位字节', trigger: 'blur'}
    ]
}









const formLoading = ref(false)
const getTB = async () => {
  try {
    console.log('props.headId', props.headId)
    formLoading.value = true // 开始加载状态

    const url = `${ycCsApi.bizSmokeMachineIncomingGoodsTb.getTBByHeadId}/${props.headId}`
    const res = await window.majesty.httpUtil.postAction(url) // 使用 await 等待结果

    if (res.code === 200) {
      Object.assign(formData, res.data) // 成功时赋值
    } else {
      message.error(res.message) // 业务错误提示
    }
  } catch (error) {
    message.error('请求失败，请稍后再试') // 网络错误或异常处理
    console.error('请求异常:', error)
  } finally {
    formLoading.value = false // 结束加载状态
  }
}

// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
const handlerSave = async () => {
  try {
    await formRef.value.validate();
    setLoading("save", true);
    const res = await window.majesty.httpUtil.postAction(
      ycCsApi.bizSmokeMachineIncomingGoodsTb.updateAndInsert,
      formData
    );
    if (res.code === 200) {
      message.success("保存成功！");
      Object.assign(formData, res.data)
    } else {
      message.error(res.message)
      throw new Error(res.message);
    }
  } catch (error) {
    console.error("保存失败:", error);
    // message.error(error.message || "保存失败，请稍后再试");

  } finally {
    setLoading("save", false);
  }
};

// 回车计算保费
const handlerCalcPremium = async () => {
  try {

    await formRef.value.validate();
    // 重新计算保费
    const res = await window.majesty.httpUtil.postAction(
      ycCsApi.bizSmokeMachineIncomingGoodsTb.calcPremium,
      formData
    );

    if (res.code === 200) {
      // 重新设置保费的值
      formData.premium = res.data.premium
      // 重新设置保险金额
      formData.insuranceAmount = res.data.insuranceAmount
    }else{
      message.error(res.message)
    }

  }catch (error) {
    // message.error();
  }
}



// 币制列表
const currList = ref([])
// 客户列表
const customerList = ref([])

// 获取下拉数据源
const getDropDownDataList = async () => {
  try {
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineIncomingGoodsTb.getCommonKeyValueList,{});
    if (res.code === 200) {
      currList.value = res.data.currList
      customerList.value = res.data.customerList
    }
  }catch(error) {
    console.error("获取下拉数据源失败！", error);
  }
}


const generateFileLoading = ref(false)
// 生成投保单
const handlerGenerateTB = (val) =>{
  // 校验表单是否填写完整
   formRef.value.validate().then(()=>{
     generateFileLoading.value = true
     window.majesty.httpUtil.downloadFile(
       `${ycCsApi.bizSmokeMachineIncomingGoodsTb.generateTB}`, '',{
         type:val.key,
         headId:props.headId,
         id:formData.id,
         name:'投保单'
       },'post',null
     ).then(res => {

     }).catch((e) => {
       message.error(e.message)
     }).finally(() => {
       generateFileLoading.value = false
     })
   }).catch((e) => {
     console.error("e", e)
   })
}






const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    pCode.value = res;
  })
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }


  formData.parentId=props.headId
  formData.headId=props.headId


  getDropDownDataList()
  getTB()


  // 表头保存后刷新投保单信息
  onEvent('refresh-3-tb-info',val=>{
    if (val === true){
      getTB()
    }
  })
});


</script>

<style lang="less" scoped>


</style>



