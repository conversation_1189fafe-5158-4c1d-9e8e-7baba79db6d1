<template>
  <section>
    <a-card size="small" title="证件信息" class="cs-card-form">
      <div v-if="formLoading" class="cs-form" style="height:38vh;display: flex;align-items: center;justify-content: center;">
        <a-spin tip="数据加载中..."/>
      </div>
      <div class="cs-form" v-else>
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
            <!-- 准运证编号 -->
            <a-form-item name="permitNumber"   :label="'准运证编号'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable  || props.isAllConfirmed"  size="small" v-model:value="formData.permitNumber"  allow-clear/>
            </a-form-item>
            <!-- 到货日期 -->
            <a-form-item name="arrivalDate"   :label="'到货日期'" class="grid-item"  :colon="false">
              <a-date-picker
                allow-clear
                :disabled="showDisable  || props.isAllConfirmed"
                v-model:value="formData.arrivalDate"
                id="arrivalDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>
            <!-- 报关单号 -->
            <a-form-item name="entryNo"   :label="'报关单号'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable  || props.isAllConfirmed"  size="small" v-model:value="formData.entryNo" allow-clear />
            </a-form-item>
            <!-- 申报日期 -->
            <a-form-item name="entryDate"   :label="'申报日期'" class="grid-item"  :colon="false">
              <a-date-picker
                allow-clear
                :disabled="showDisable  || props.isAllConfirmed"
                v-model:value="formData.entryDate"
                id="entryDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>
            <!-- 放行日期 -->
            <a-form-item name="releaseDate"   :label="'放行日期'" class="grid-item"  :colon="false">
              <a-date-picker
                allow-clear
                :disabled="showDisable  || props.isAllConfirmed"
                v-model:value="formData.releaseDate"
                id="releaseDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>
            <!-- 备注 -->
            <a-form-item name="note"   :label="'备注'" class="grid-item merge-3"  :colon="false">
                <a-textarea :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.note"   :autosize="{ minRows: 3, maxRows: 10 }" allow-clear/>

            </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      :loading="buttonLoadingMap.save"
                      :disabled="props.isAllConfirmed"
                      v-if="!showDisable">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="handleOnBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
  import {editStatus, productClassify} from '@/view/common/constant'
  import {message} from "ant-design-vue";
  import {onMounted, reactive, ref} from "vue";
  import CsSelect from "@/components/select/CsSelect.vue";
  import {usePCode} from "@/view/common/usePCode";
  import ycCsApi from "@/api/ycCsApi";
  const { getPCode } = usePCode()

  import {useButtonLoading} from "@/view/utils/useBtnLoading";
  const { setLoading,buttonLoadingMap } = useButtonLoading()



  const props = defineProps({
    editConfig: {
      type: Object,
      default: () => {
      }
    },
    headId:{
      type:String,
      default:()=>''
    },
    operationStatus:{
      type:String,
      default:()=>''
    },
    /* 是否全部确认 */
    isAllConfirmed: {
      type: Boolean,
      default: () => false
    }
  });

  defineOptions({
    name: 'BizSmokeMachineIncomingGoodsDocumentEdit',
  })

  // 定义子组件 emit事件，用于子组件向父组件传递数据
  const emit = defineEmits(['onBack']);

  const handleOnBack = (val) => {
    emit('onEditBack', val);
  };

  // 是否禁用
  const showDisable = ref(false)

  // 表单数据
  const formData = reactive({
      // 主键ID
      id:'',
      // 业务类型
      businessType:'',
      // 数据状态
      dataState:'',
      // 版本号
      versionNo:'',
      // 企业10位编码
      tradeCode:'',
      // 组织机构代码
      sysOrgCode:'',
      // 父级ID
      parentId:'',
      // 创建人
      createBy:'',
      // 创建时间
      createTime:'',
      // 更新人
      updateBy:'',
      // 更新时间
      updateTime:'',
      // 插入用户名
      insertUserName:'',
      // 更新用户名
      updateUserName:'',
      // 扩展字段1
      extend1:'',
      // 扩展字段2
      extend2:'',
      // 扩展字段3
      extend3:'',
      // 扩展字段4
      extend4:'',
      // 扩展字段5
      extend5:'',
      // 扩展字段6
      extend6:'',
      // 扩展字段7
      extend7:'',
      // 扩展字段8
      extend8:'',
      // 扩展字段9
      extend9:'',
      // 扩展字段10
      extend10:'',
      // 准运证编号
      permitNumber:'',
      // 到货日期
      arrivalDate:'',
      // 报关单号
      entryNo:'',
      // 申报日期
      entryDate:'',
      // 放行日期
      releaseDate:'',
      // 备注
      note:'',
      // 进货单表头ID
      headId:''
  })
  // 校验规则
  const rules = {
      id:[
          {max: 40, message: '主键ID长度不能超过 40位字节', trigger: 'blur'}
      ],
      businessType:[
          {max: 60, message: '业务类型长度不能超过 60位字节', trigger: 'blur'}
      ],
      dataState:[
          {max: 10, message: '数据状态长度不能超过 10位字节', trigger: 'blur'}
      ],
      versionNo:[
          {max: 10, message: '版本号长度不能超过 10位字节', trigger: 'blur'}
      ],
      tradeCode:[
          {max: 10, message: '企业10位编码长度不能超过 10位字节', trigger: 'blur'}
      ],
      sysOrgCode:[
          {max: 10, message: '组织机构代码长度不能超过 10位字节', trigger: 'blur'}
      ],
      parentId:[
          {max: 40, message: '父级ID长度不能超过 40位字节', trigger: 'blur'}
      ],
      createBy:[
          {max: 50, message: '创建人长度不能超过 50位字节', trigger: 'blur'}
      ],
      createTime:[
      ],
      updateBy:[
          {max: 50, message: '更新人长度不能超过 50位字节', trigger: 'blur'}
      ],
      updateTime:[
      ],
      insertUserName:[
          {max: 50, message: '插入用户名长度不能超过 50位字节', trigger: 'blur'}
      ],
      updateUserName:[
          {max: 50, message: '更新用户名长度不能超过 50位字节', trigger: 'blur'}
      ],
      extend1:[
          {max: 200, message: '扩展字段1长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend2:[
          {max: 200, message: '扩展字段2长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend3:[
          {max: 200, message: '扩展字段3长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend4:[
          {max: 200, message: '扩展字段4长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend5:[
          {max: 200, message: '扩展字段5长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend6:[
          {max: 200, message: '扩展字段6长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend7:[
          {max: 200, message: '扩展字段7长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend8:[
          {max: 200, message: '扩展字段8长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend9:[
          {max: 200, message: '扩展字段9长度不能超过 200位字节', trigger: 'blur'}
      ],
      extend10:[
          {max: 200, message: '扩展字段10长度不能超过 200位字节', trigger: 'blur'}
      ],
      permitNumber:[
          {max: 60, message: '准运证编号长度不能超过 60 位字节', trigger: 'blur'}
      ],
      arrivalDate:[
      ],
      entryNo:[
          {max: 18, message: '报关单号长度不能超过 18位字节', trigger: 'blur'}
      ],
      entryDate:[
      ],
      releaseDate:[
      ],
      note:[
          {max: 200, message: '备注长度不能超过 200 位字节', trigger: 'blur'}
      ],
      headId:[
          {max: 50, message: '进货单表头ID长度不能超过 50位字节', trigger: 'blur'}
      ]
  }







  const formLoading = ref(false)
  const getDocument = async () => {
    try {
      console.log('props.headId', props.headId)
      formLoading.value = true // 开始加载状态

      const url = `${ycCsApi.bizSmokeMachineIncomingDocument.getDocumentByHeadId}/${props.headId}`
      const res = await window.majesty.httpUtil.postAction(url) // 使用 await 等待结果

      if (res.code === 200) {
        Object.assign(formData, res.data) // 成功时赋值
      } else {
        message.error(res.message) // 业务错误提示
      }
    } catch (error) {
      message.error('请求失败，请稍后再试') // 网络错误或异常处理
      console.error('请求异常:', error)
    } finally {
      formLoading.value = false // 结束加载状态
    }
  }

  // vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
  const formRef = ref(null);
  const handlerSave = async () => {
    try {
      await formRef.value.validate();
      setLoading("save", true);
      const res = await window.majesty.httpUtil.postAction(
        ycCsApi.bizSmokeMachineIncomingDocument.updateAndInsert,
        formData
      );
      if (res.code === 200) {
        message.success("保存成功！");
      } else {
        message.error(res.message)
        throw new Error(res.message);
      }
    } catch (error) {
      console.error("保存失败:", error);
      // message.error(error.message || "保存失败，请稍后再试");

    } finally {
      setLoading("save", false);
    }
  };


  const pCode = ref('')
  // 初始化操作
  onMounted(() => {
    getDocument()
    getPCode().then(res=>{
      console.log('res',res)
      pCode.value = res;
    })
    // 初始化数据
    if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
      showDisable.value = false
    }
    if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
      showDisable.value = true
    }

    formData.parentId=props.headId
    formData.headId=props.headId
  });

</script>

<style lang="less" scoped>


</style>



