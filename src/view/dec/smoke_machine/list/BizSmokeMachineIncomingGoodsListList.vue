<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 操作按钮区域 -->
      <!--
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:add']">
          <a-button size="small" @click="handlerAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{ localeContent('m.common.button.add') }}
          </a-button>
        </div>
      </div>
      -->
      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-table-item remove-table-border-add-bg"
          size="small"
          :height="430"
          :scroll="{ y:'100%', x: 400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
          <template #bodyCell="{text, record, index, column, key }">
            <!-- 数量 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'quantity'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].quantity"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
                :precision="6"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>

            <!-- 单价 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'unitPrice'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].unitPrice"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
                :precision="6"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


            <!-- 交货日期 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'deliveryDate'">
              <a-date-picker
                v-if="props.isEdit === true"
                size="small"
                placeholder=""
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                v-model:value="dataSourceList[index].deliveryDate"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
              />
              <span v-else>{{text}}</span>
            </template>



            <!-- 备注 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'note'">

              <a-input
                v-if="props.isEdit === true"
                v-model:value="dataSourceList[index].note"
                placeholder=""
                allow-clear
                size="small"
                style="width: 100%"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
              />
              <span v-else>{{text}}</span>
            </template>


          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>


  </section>


</template>

<script setup>

  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {createVNode, onMounted, provide, reactive, ref} from "vue";
  import {message, Modal} from "ant-design-vue";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  import {localeContent} from "@/view/utils/commonUtil";
  import ycCsApi from "@/api/ycCsApi";
  import {isNullOrEmpty} from "@/view/utils/common";
  import { useColumnsRender } from "@/view/common/useColumnsRender";
  import useEventBus from "@/view/common/eventBus";
  const  { inputFormatter, inputParser,formatNumber,formatSpecifiedNumber,cmbShowRender } = useColumnsRender()
  const { emitEvent } = useEventBus()

  /* 表头传入参数 */
  const props = defineProps({
    // 表头headId
    headId:{
      type: String,
      default: "",
    },
    // 是否可编辑
    isEdit:{
      type:Boolean,
      default:""
    },
    // 是否查看模式
    showDisable:{
      type:Boolean,
      default:""
    }

  })



  /* 引入通用方法 */
  const {
    editConfig,
    show,
    page,
    showSearch,
    headSearch,
    handleEditByRow,
    handleViewByRow,
    operationEdit,
    handleShowSearch,
    handlerSearch,
    dataSourceList,
    tableLoading,
    getTableScroll,
    exportLoading,
    ajaxUrl,
    doExport,
    handlerRefresh

  } = useCommon()


  defineOptions({
    name: 'BizSmokeMachineIncomingGoodsList',
  });


  const importShow = ref(false)


  /* table表格字段属性设置 */
  const totalColumns = ref([

    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'goodsName',
      key: 'goodsName',
    },
    {
      title: '产品型号',
      width: 200,
      align: 'center',
      dataIndex: 'productModel',
      key: 'productModel',
    },
    {
      title: '数量',
      width: 200,
      align: 'center',
      dataIndex: 'quantity',
      key: 'quantity',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      customRender: ({ text }) => {
        return cmbShowRender(text,unitList.value)
      }
    },

    {
      title: '单价',
      width: 200,
      align: 'center',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },

    {
      title: '金额',
      width: 200,
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '交货日期',
      width: 200,
      align: 'center',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
    },
    {
      title: '总价折美元',
      width: 200,
      align: 'center',
      dataIndex: 'totalUsd',
      key: 'totalUsd',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '备注',
      width: 300,
      align: 'center',
      dataIndex: 'note',
      key: 'note',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
    },

  ])





  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData: [],
    loading: false,
  });


  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };


  /* 按钮loading */
  const deleteLoading = ref(false)


  /* 删除数据 */
  const handlerDelete = () => {
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk() {
        deleteLoading.value = true
        deleteClient(gridData.selectedRowKeys).then(res => {
          if (res.code === 200) {
            message.success("删除成功！")
            getList()
          }
        }).finally(() => {
          deleteLoading.value = false
        })
      },
      onCancel() {

      },
    });

  }




  /* 获取列表 */
  // 方法定义
  const getList = async (val) => {
    let headId = await  val?val:props.headId
    // console.log('表头HeadId',headId1)
    tableLoading.value = true
    let params = {
      headId: headId
    }
    try {
      const res =  await window.majesty.httpUtil.postAction(`${ycCsApi.bizSmokeMachineInComingList.list}?page=${page.current}&limit=${page.pageSize}`,
        params
      );
      dataSourceList.value = res.data
      page.total = res.total
    }catch(err) {
      console.log(err)
      message.error(err.message)
    }finally {
      tableLoading.value = false
    }

  }


  const onPageChange = async (pageNumber, pageSize) =>{
    page.current = pageNumber
    page.pageSize = pageSize
    // 在这里添加处理页码变化的逻辑
    await getList()
    // await getListSumTotal()
  }



  /* 行内编辑通用方法 */
  const isEditLoading = ref(false)

  const handleQuantityChange = async (record, column) => {
    if (isEditLoading.value === true) {
      console.log('回车，失焦同时触发！');
      return;
    }

    isEditLoading.value = true;

    if (!record || !record.id) {
      isEditLoading.value = false;
      return;
    }
    const res = await  window.majesty.httpUtil.postAction(`${ycCsApi.bizSmokeMachineInComingList.getBizSmokeMachineIncomingGoodsListById}/${record.id}`,record);
    const dataTemp = res.data;
    try {


      if (res.code !== 200) {
        isEditLoading.value = false;
        return;
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改数量 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'quantity') {
        if (isNullOrEmpty(dataTemp) || dataTemp.quantity === record.quantity) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizSmokeMachineInComingList.updateQuantity}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.quantity',dataTemp.quantity);
          record.quantity = dataTemp.quantity; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改单价 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'unitPrice') {
        if (isNullOrEmpty(dataTemp) || dataTemp.unitPrice === record.unitPrice) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizSmokeMachineInComingList.updateUnitPrice}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          emitEvent('refresh-3-head-info')
        } else {
          record.unitPrice = dataTemp.unitPrice; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改交货日期 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'deliveryDate') {
        if (isNullOrEmpty(dataTemp) || dataTemp.deliveryDate === record.deliveryDate) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizSmokeMachineInComingList.updateCommonFiled}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
        } else {
          record.deliveryDate = dataTemp.deliveryDate; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改备注 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'note') {
        if (isNullOrEmpty(dataTemp) || dataTemp.note === record.note) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizSmokeMachineInComingList.updateCommonFiled}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
        } else {
          record.note = dataTemp.note; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }



    } catch (error) {
      // Object.assign(record, dataTemp)
      message.error(error.message);
    } finally {
      setTimeout(() => {
        isEditLoading.value = false;
      }, 100);
    }
  };



  // 获取单位键值对列表
  const unitList = ref([])

  const getUnitList = async () => {
    let params = {}
    const res = await window.majesty.httpUtil.postAction( ycCsApi.bizSmokeMachineInComingList.getCommonKeyValueList,params);
    // console.log('res', res);
    if (res.code === 200) {
      unitList.value =  res.data.unitList
    }else {
      message.error(res.message)
    }
  }




  onMounted(  () => {

    getUnitList()
    getList()

  })



  // 将数据对外暴露
  defineExpose({
    getList,
    dataSourceList
  })

</script>

<style lang="less" scoped>


</style>
