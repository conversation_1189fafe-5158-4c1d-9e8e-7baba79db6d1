import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()




/**
 * 第9条线-非国营贸易出口辅料-出货信息表头
 */
export function getColumns() {

  const commColumns = reactive([
    'id',
    'businessType',
    'dataState',
    'versionNo',
    'tradeCode',
    'sysOrgCode',
    'parentId',
    'createBy',
    'createTime',
    'updateBy',
    'updateTime',
    'insertUserName',
    'updateUserName',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10',
    'exportNo',
    'contractNo',
    'customer',
    'customerAddress',
    'supplier',
    'tradeCountry',
    'manageUnit',
    'paymentType',
    'currency',
    'transportType',
    'priceTerms',
    'priceTermsPort',
    'deliveryUnit',
    'packageType',
    'packageNum',
    'deliveryUnitLocation',
    'shipper',
    'consignee',
    'notifyParty',
    'grossWeight',
    'netWeight',
    'tareWeight',
    'mark',
    'portOfShipment',
    'portOfDestination',
    'shipmentDate',
    'insuranceType',
    'insuranceCurrency',
    'insuranceAddRate',
    'insuranceRate',
    'insuranceFee',
    'insurer',
    'freight',
    'freightCurrency',
    'warehouseAddress',
    'contactPerson',
    'contactPhone',
    'remark',
    'sendCustoms',
    'confirmTime',
    'transportPermitNo',
    'transportPermitApplyDate',
    'arrivalConfirmDate'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      width: 150,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '主键id',
      width: 200,
      align: 'center',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '业务类型',
      width: 200,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
    },
    {
      title: '数据状态',
      width: 200,
      align: 'center',
      dataIndex: 'dataState',
      key: 'dataState',
    },
    {
      title: '版本号',
      width: 200,
      align: 'center',
      dataIndex: 'versionNo',
      key: 'versionNo',
    },
    {
      title: '企业10位编码',
      width: 200,
      align: 'center',
      dataIndex: 'tradeCode',
      key: 'tradeCode',
    },
    {
      title: '组织机构代码',
      width: 200,
      align: 'center',
      dataIndex: 'sysOrgCode',
      key: 'sysOrgCode',
    },
    {
      title: '父级id',
      width: 200,
      align: 'center',
      dataIndex: 'parentId',
      key: 'parentId',
    },
    {
      title: '创建人',
      width: 200,
      align: 'center',
      dataIndex: 'createBy',
      key: 'createBy',
    },
    {
      title: '创建时间',
      width: 200,
      align: 'center',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '更新人',
      width: 200,
      align: 'center',
      dataIndex: 'updateBy',
      key: 'updateBy',
    },
    {
      title: '更新时间',
      width: 200,
      align: 'center',
      dataIndex: 'updateTime',
      key: 'updateTime',
    },
    {
      title: '插入用户名',
      width: 200,
      align: 'center',
      dataIndex: 'insertUserName',
      key: 'insertUserName',
    },
    {
      title: '更新用户名',
      width: 200,
      align: 'center',
      dataIndex: 'updateUserName',
      key: 'updateUserName',
    },
    {
      title: '扩展字段1',
      width: 200,
      align: 'center',
      dataIndex: 'extend1',
      key: 'extend1',
    },
    {
      title: '扩展字段2',
      width: 200,
      align: 'center',
      dataIndex: 'extend2',
      key: 'extend2',
    },
    {
      title: '扩展字段3',
      width: 200,
      align: 'center',
      dataIndex: 'extend3',
      key: 'extend3',
    },
    {
      title: '扩展字段4',
      width: 200,
      align: 'center',
      dataIndex: 'extend4',
      key: 'extend4',
    },
    {
      title: '扩展字段5',
      width: 200,
      align: 'center',
      dataIndex: 'extend5',
      key: 'extend5',
    },
    {
      title: '扩展字段6',
      width: 200,
      align: 'center',
      dataIndex: 'extend6',
      key: 'extend6',
    },
    {
      title: '扩展字段7',
      width: 200,
      align: 'center',
      dataIndex: 'extend7',
      key: 'extend7',
    },
    {
      title: '扩展字段8',
      width: 200,
      align: 'center',
      dataIndex: 'extend8',
      key: 'extend8',
    },
    {
      title: '扩展字段9',
      width: 200,
      align: 'center',
      dataIndex: 'extend9',
      key: 'extend9',
    },
    {
      title: '扩展字段10',
      width: 200,
      align: 'center',
      dataIndex: 'extend10',
      key: 'extend10',
    },
    {
      title: '出货单号',
      width: 200,
      align: 'center',
      dataIndex: 'exportNo',
      key: 'exportNo',
    },
    {
      title: '合同号',
      width: 200,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
    },
    {
      title: '客户',
      width: 200,
      align: 'center',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: '客户地址',
      width: 200,
      align: 'center',
      dataIndex: 'customerAddress',
      key: 'customerAddress',
    },
    {
      title: '供应商',
      width: 200,
      align: 'center',
      dataIndex: 'supplier',
      key: 'supplier',
    },
    {
      title: '贸易国别',
      width: 200,
      align: 'center',
      dataIndex: 'tradeCountry',
      key: 'tradeCountry',
    },
    {
      title: '经营单位',
      width: 200,
      align: 'center',
      dataIndex: 'manageUnit',
      key: 'manageUnit',
    },
    {
      title: '付款方式',
      width: 200,
      align: 'center',
      dataIndex: 'paymentType',
      key: 'paymentType',
    },
    {
      title: '币种',
      width: 200,
      align: 'center',
      dataIndex: 'currency',
      key: 'currency',
    },
    {
      title: '运输方式',
      width: 200,
      align: 'center',
      dataIndex: 'transportType',
      key: 'transportType',
    },
    {
      title: '价格条款',
      width: 200,
      align: 'center',
      dataIndex: 'priceTerms',
      key: 'priceTerms',
    },
    {
      title: '价格条款对应港口',
      width: 200,
      align: 'center',
      dataIndex: 'priceTermsPort',
      key: 'priceTermsPort',
    },
    {
      title: '发货单位',
      width: 200,
      align: 'center',
      dataIndex: 'deliveryUnit',
      key: 'deliveryUnit',
    },
    {
      title: '包装种类',
      width: 200,
      align: 'center',
      dataIndex: 'packageType',
      key: 'packageType',
    },
    {
      title: '包装数量',
      width: 200,
      align: 'center',
      dataIndex: 'packageNum',
      key: 'packageNum',
    },
    {
      title: '发货单位所在地',
      width: 200,
      align: 'center',
      dataIndex: 'deliveryUnitLocation',
      key: 'deliveryUnitLocation',
    },
    {
      title: '装运人shipper',
      width: 200,
      align: 'center',
      dataIndex: 'shipper',
      key: 'shipper',
    },
    {
      title: '收货人consignee',
      width: 200,
      align: 'center',
      dataIndex: 'consignee',
      key: 'consignee',
    },
    {
      title: '通知人notify party',
      width: 200,
      align: 'center',
      dataIndex: 'notifyParty',
      key: 'notifyParty',
    },
    {
      title: '总毛重',
      width: 200,
      align: 'center',
      dataIndex: 'grossWeight',
      key: 'grossWeight',
    },
    {
      title: '总净重',
      width: 200,
      align: 'center',
      dataIndex: 'netWeight',
      key: 'netWeight',
    },
    {
      title: '总皮重',
      width: 200,
      align: 'center',
      dataIndex: 'tareWeight',
      key: 'tareWeight',
    },
    {
      title: '唛头',
      width: 200,
      align: 'center',
      dataIndex: 'mark',
      key: 'mark',
    },
    {
      title: '装运港',
      width: 200,
      align: 'center',
      dataIndex: 'portOfShipment',
      key: 'portOfShipment',
    },
    {
      title: '目的地/港',
      width: 200,
      align: 'center',
      dataIndex: 'portOfDestination',
      key: 'portOfDestination',
    },
    {
      title: '装运期限',
      width: 200,
      align: 'center',
      dataIndex: 'shipmentDate',
      key: 'shipmentDate',
    },
    {
      title: '险别',
      width: 200,
      align: 'center',
      dataIndex: 'insuranceType',
      key: 'insuranceType',
    },
    {
      title: '保费币种',
      width: 200,
      align: 'center',
      dataIndex: 'insuranceCurrency',
      key: 'insuranceCurrency',
    },
    {
      title: '投保加成%',
      width: 200,
      align: 'center',
      dataIndex: 'insuranceAddRate',
      key: 'insuranceAddRate',
    },
    {
      title: '保费费率(%)',
      width: 200,
      align: 'center',
      dataIndex: 'insuranceRate',
      key: 'insuranceRate',
    },
    {
      title: '保险费',
      width: 200,
      align: 'center',
      dataIndex: 'insuranceFee',
      key: 'insuranceFee',
    },
    {
      title: '投保人',
      width: 200,
      align: 'center',
      dataIndex: 'insurer',
      key: 'insurer',
    },
    {
      title: '运费',
      width: 200,
      align: 'center',
      dataIndex: 'freight',
      key: 'freight',
    },
    {
      title: '运费币种',
      width: 200,
      align: 'center',
      dataIndex: 'freightCurrency',
      key: 'freightCurrency',
    },
    {
      title: '仓储地址',
      width: 200,
      align: 'center',
      dataIndex: 'warehouseAddress',
      key: 'warehouseAddress',
    },
    {
      title: '联系人',
      width: 200,
      align: 'center',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
    },
    {
      title: '联系电话',
      width: 200,
      align: 'center',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
    },
    {
      title: '备注',
      width: 200,
      align: 'center',
      dataIndex: 'remark',
      key: 'remark',
    },
    {
      title: '发送报关',
      width: 200,
      align: 'center',
      dataIndex: 'sendCustoms',
      key: 'sendCustoms',
    },
    {
      title: '确认时间',
      width: 200,
      align: 'center',
      dataIndex: 'confirmTime',
      key: 'confirmTime',
    },
    {
      title: '准运证编号，用户录入',
      width: 200,
      align: 'center',
      dataIndex: 'transportPermitNo',
      key: 'transportPermitNo',
    },
    {
      title: '准运证申办日期，用户录入',
      width: 200,
      align: 'center',
      dataIndex: 'transportPermitApplyDate',
      key: 'transportPermitApplyDate',
    },
    {
      title: '到货确认日期，用户录入',
      width: 200,
      align: 'center',
      dataIndex: 'arrivalConfirmDate',
      key: 'arrivalConfirmDate'
    }
     
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
