<template>
  <a-form layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form grid-container" >

    <!--  出货单号  -->
    <a-form-item name="exportNo"  label="出货单号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.exportNo"     allow-clear />
    </a-form-item>

    <!--  合同号  -->
    <a-form-item name="contractNo"  label="合同号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNo"   allow-clear />
    </a-form-item>


    <!--  供应商  -->
    <a-form-item name="supplier"  label="供应商" class="grid-item"  :colon="false">
      <cs-select
        :options="exportSupplierList"
        :combine-display="true"
        option-filter-prop="label"
        option-label-prop="key"
        allow-clear
        show-search
        v-model:value="searchParam.supplier"
        id="supplier"
      />
    </a-form-item>


    <!--  客户  -->
    <a-form-item name="customer"  label="客户" class="grid-item"  :colon="false">

      <cs-select
        :options="exportCustomerList"
        :combine-display="true"
        option-filter-prop="label"
        option-label-prop="key"
        allow-clear
        show-search
        v-model:value="searchParam.customer"
        id="customer"
      />
    </a-form-item>



    <!--  出货单据状态  -->
    <a-form-item name="dataState"  label="出货单据状态" class="grid-item"  :colon="false">
      <cs-select
        :options="productClassify.orderStatus"
        :combine-display="true"
        option-filter-prop="label"
        option-label-prop="key"
        allow-clear
        show-search
        v-model:value="searchParam.dataState"
        id="dataState"
      />
    </a-form-item>


    <!-- 外销发票状态 -->
    <a-form-item name="invoiceDataState"  label="外销发票状态" class="grid-item"  :colon="false">
      <cs-select
        :options="productClassify.orderStatus"
        :combine-display="true"
        option-filter-prop="label"
        option-label-prop="key"
        allow-clear
        show-search
        v-model:value="searchParam.invoiceDataState"
        id="invoiceDataState"
      />
    </a-form-item>




    <!-- 制单人 -->
    <a-form-item name="createBy"  label="制单人" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.createBy"          allow-clear/>
    </a-form-item>



    <!--  确认时间   -->
    <a-form-item name="confirmTime" label="制单时间"  class="grid-item" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createTimeFrom"
              id="createTimeFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createTimeTo"
              id="createTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>



  </a-form>
</template>

<script setup>
  import {inject, onMounted, reactive, ref} from 'vue'
  import ycCsApi from "@/api/ycCsApi";
  import {message} from "ant-design-vue";
  import CsSelect from "@/components/select/CsSelect.vue";
  import {productClassify} from "@/view/common/constant";
  import useEventBus from "@/view/common/eventBus";
  const {onEvent} = useEventBus()

  defineOptions({
    name: 'BizExportGoodsHeadSearch'
  })


  const exportCustomerList = ref([]);
  const exportSupplierList = ref([]);

  const getCommonKeyValueList = async () => {
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.getCommonKeyValueList,{});
    if (res.code === 200) {
      exportSupplierList.value = res.data.exportSupplierList;
      exportCustomerList.value = res.data.exportCustomerList;
    }else {
      message.error(res.message);
    }
  }


  const searchParam = reactive({
    id:'',
    businessType:'',
    dataState:'',
    versionNo:'',
    tradeCode:'',
    sysOrgCode:'',
    parentId:'',
    createBy:'',
    createTime:'',
    createTimeTo:'',
    createTimeFrom:'',
    updateBy:'',
    updateTime:'',
    updateTimeTo:'',
    updateTimeForm:'',
    insertUserName:'',
    updateUserName:'',
    extend1:'',
    extend2:'',
    extend3:'',
    extend4:'',
    extend5:'',
    extend6:'',
    extend7:'',
    extend8:'',
    extend9:'',
    extend10:'',
    exportNo:'',
    contractNo:'',
    customer:'',
    customerAddress:'',
    supplier:'',
    tradeCountry:'',
    manageUnit:'',
    paymentType:'',
    currency:'',
    transportType:'',
    priceTerms:'',
    priceTermsPort:'',
    deliveryUnit:'',
    packageType:'',
    packageNum:'',
    deliveryUnitLocation:'',
    shipper:'',
    consignee:'',
    notifyParty:'',
    grossWeight:'',
    netWeight:'',
    tareWeight:'',
    mark:'',
    portOfShipment:'',
    portOfDestination:'',
    shipmentDate:'',
    insuranceType:'',
    insuranceCurrency:'',
    insuranceAddRate:'',
    insuranceRate:'',
    insuranceFee:'',
    insurer:'',
    freight:'',
    freightCurrency:'',
    warehouseAddress:'',
    contactPerson:'',
    contactPhone:'',
    remark:'',
    sendCustoms:'',
    confirmTime:'',
    confirmTimeTo:'',
    confirmTimeForm:'',
    transportPermitNo:'',
    transportPermitApplyDate:'',
    transportPermitApplyDateTo:'',
    transportPermitApplyDateForm:'',
    arrivalConfirmDate:'',
    arrivalConfirmDateTo:'',
    arrivalConfirmDateForm:'',
    invoiceDataState:''
  })

  /* 定义重置方法(注意前后顺序) */
  const resetSearch = () => {
    Object.keys(searchParam).forEach(key => {
      searchParam[key] = '';
    });
  }





  defineExpose({searchParam,resetSearch});



  onMounted(() => {
    getCommonKeyValueList();

    onEvent('refresh-export-goods-head-search',()=>{
      getCommonKeyValueList()
    });
  });

</script>

<style lang='less' scoped>

</style>
