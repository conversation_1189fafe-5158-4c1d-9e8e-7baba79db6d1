<template>
  <!-- 第9条线-非国营贸易出口辅料-外销发票表体 -->
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 操作按钮区域 -->
      <!--
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:export_sell_list:add']">
          <a-button size="small" @click="handlerAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{ localeContent('m.common.button.add') }}
          </a-button>
        </div>
      </div>
      -->
      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
          :height="500"
          :scroll="{ y: '100%',x:400 }"
          :animate-rows="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>

        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <!-- 汇总信息 -->
        <div class="cs-margin-right cs-list-total-data ">
          数量：{{formatSpecifiedNumber(totalData.qtyTotal,true,2)}} ，金额：{{formatSpecifiedNumber(totalData.totalAmount,true,2)}}，毛重(KG)：{{formatSpecifiedNumber(totalData.grossWeightTotal,true,2)}}，净重(KG)：{{formatSpecifiedNumber(totalData.netWeightTotal,true,2)}}
        </div>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>


  </section>


</template>

<script setup>

  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {createVNode, onMounted, provide, reactive, ref} from "vue";
  import ycCsApi from "@/api/ycCsApi";
  import {message} from "ant-design-vue";
  import {useColumnsRender} from "@/view/common/useColumnsRender";
  const  { inputFormatter, inputParser,formatSpecifiedNumber,cmbShowRender } = useColumnsRender()


  /* 引入通用方法 */
  const {
    show,
    dataSourceList,
    tableLoading,
    getTableScroll,
  } = useCommon()


  defineOptions({
    name: 'BizExportGoodsSellListList',
  });


  const props = defineProps({
    parentId: {
      type: String,
      default: ''
    },
  })



  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'goodsName',
      key: 'goodsName',
    },
    {
      title: '商品描述',
      width: 200,
      align: 'center',
      dataIndex: 'goodsDesc',
      key: 'goodsDesc',
    },
    {
      title: '开票名称',
      width: 200,
      align: 'center',
      dataIndex: 'invoiceName',
      key: 'invoiceName',
    },
    {
      title: '数量',
      width: 200,
      align: 'center',
      dataIndex: 'quantity',
      key: 'quantity',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return cmbShowRender(text,unitList.value)
      }
    },
    {
      title: '包装数量',
      width: 200,
      align: 'center',
      dataIndex: 'packageQuantity',
      key: 'packageQuantity',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '包装',
      width: 200,
      align: 'center',
      dataIndex: 'packageStyle',
      key: 'packageStyle',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return cmbShowRender(text,packageList.value)
      }
    },
    {
      title: '单价',
      width: 200,
      align: 'center',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '金额',
      width: 200,
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '毛重',
      width: 200,
      align: 'center',
      dataIndex: 'grossWeight',
      key: 'grossWeight',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '净重',
      width: 200,
      align: 'center',
      dataIndex: 'netWeight',
      key: 'netWeight',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },


  ])



  const tableHeight = ref('')

  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData: [],
    loading: false,
  });


  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };

  const page = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  const totalData = ref({
    qtyTotal: 0,
    amountTotal: 0,
    grossWeightTotal: 0,
    netWeightTotal: 0,
  })

  const getListTotal = async () => {
    try {
      const res = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsSellList.getListTotal}`,{parentId:props.parentId})
      if (res.code === 200) {
        totalData.value = res.data
      }else {
        message.error(res.message)
      }
    }catch (error) {
      console.log(error)
    }finally {

    }
  }


  const getList = async () => {
    try {
      console.log('当前表头的ID为：',props.parentId)
      tableLoading.value = true
      const res = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsSellList.list}?page=${page.current}&limit=${page.pageSize}`,{parentId:props.parentId})
      if (res.code === 200) {
        dataSourceList.value = res.data
        page.total = res.total
        await getListTotal()
      }else {
        message.error(res.message)
      }
    }catch(err) {
      console.log(err)
    }finally {
      tableLoading.value = false
    }

  }



  /**
   * 分页
   * @param pageNumber
   * @param pageSize
   */
  const onPageChange = (pageNumber, pageSize) => {
    page.current = pageNumber
    page.pageSize = pageSize
    getList()
  }

  const unitList = ref([])
  const packageList = ref([])
  const getCommonKeyValueList = async () => {
    try {
      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.getCommonKeyValueList,{});
      if (res.code === 200) {
        unitList.value = res.data.unitList;
        packageList.value = res.data.packageList;
      }else {
        message.error(res.message);
      }
    }catch(err) {
      console.log(err);
    }finally {
    }

  }



  onMounted(() => {

    tableHeight.value = getTableScroll(100, '');
    getCommonKeyValueList()
    getList()
  })




</script>

<style lang="less" scoped>


</style>
