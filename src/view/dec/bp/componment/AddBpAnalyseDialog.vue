<template>
  <section class="cs-action">
    <!-- 查询区 -->
    <div class="header-search">
      <a-form layout="inline" label-align="right" :label-col="{ style: { width: '50px' } }" :model="searchParam"
              class="pw-form grid-container">
        <a-form-item name="contractNo" label="合同号" class="grid-item merge-3" :colon="false">
          <div style="display: flex">
            <a-input size="small" v-model:value="searchParam.contractNo" allow-clear/>
            <a-button class="cs-margin-left" type="primary" size="small" @click="getContractList">查询</a-button>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <!-- 表格区 -->
    <div>
      <s-table
        style="width: 100%;height: 100%;min-height: 100%;overflow-y: auto;overflow-x:auto"
        ref="tableRef"
        size="small"
        class="cs-action-table-item remove-table-border-add-bg"
        bordered
        height="50vh"
        :scroll="{ y: 400, x: 400 }"
        :pagination="false"
        :columns="showColumns"
        :data-source="dataSourceList"
        :row-selection="{
            type: 'radio',
            selectedRowKeys: gridData.selectedRowKeys,
            onChange: onSelectChange
        }"
        :loading="tableLoading"
        row-key="id"
        :range-selection="false"
      >
        <template #emptyText>
          <a-empty description="暂无数据"/>
        </template>
      </s-table>
    </div>
    <!-- 操作按钮区 -->
    <div style="margin-top: 16px; text-align: right;">
      <a-button @click="onCancel" size="small">返回</a-button>
      <a-button size="small" type="primary" class="cs-margin-left" @click="onSave" :disabled="!gridData.selectedRowKeys.length">
        保存
      </a-button>
    </div>
  </section>
</template>

<script setup>
import {ref, reactive, defineEmits, onMounted} from 'vue'
  import {message} from "ant-design-vue";
  import ycCsApi from "@/api/ycCsApi";

  const emit = defineEmits(['save', 'cancel'])

  // 查询参数
  const searchParam = reactive({
    contractNo: ''
  })

  /* 新增分析单 - 从外商合同提取 + 外商合同表体（出料加工） */
  defineOptions({
    name: 'AddBpAnalyseDialog',
  })

  // 表格数据
  const dataSourceList = ref([])
  const tableLoading = ref(false)

  // 选中数据
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData: [],
    loading: false,
  })

  // 表格列
  const showColumns = [
    {
      title: '合同号',
      dataIndex: 'contractNo',
      width: 150,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '签约日期',
      dataIndex: 'signDate',
      width: 150,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '客户',
      dataIndex: 'customerCode',
      width: 150,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '金额',
      dataIndex: 'totalMoney',
      width: 150,
      align: 'center',
      ellipsis: true,
    },

  ]

  // 多选事件
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData
    gridData.selectedRowKeys = selectedRowKeys
  }

  // 查询事件
  const getContractList = async () => {
    tableLoading.value = true
    try {

      let params = {
        contractNo:searchParam.contractNo
      }
      // 这里写你的接口请求，拿到数据后赋值给 dataSourceList
      // 示例：dataSourceList.value = await api.getContractList(searchParam)
      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderHead.getContractNoList,params)
      if (res.code === 200){
        dataSourceList.value = res.data
        // 情况选中数据
        gridData.selectedRowKeys = []
        gridData.selectedData = []
      }else {
        message.error(res.message)
      }
    }catch (err){
      console.log(err)
      message.error(err.message)
    }finally {
      tableLoading.value = false
    }

  }

  // 保存事件
  const onSave = () => {
    // 只能选择一条数据
    if (gridData.selectedData.length !== 1) {
      message.warn('只能选择一条数据，进行提取！')
    }else {
      emit('save', gridData.selectedData)
      gridData.selectedRowKeys = []
    }
    console.log('选择的数据：', gridData.selectedData)
  }

  onMounted(() => {
    getContractList()
  })

  // 返回事件
  const onCancel = () => {
    emit('cancel')
  }
</script>

<style lang="less" scoped>
.header-search {
  margin: 10px 0;
}

.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: calc(100vh);
  height: auto;

  .surely-table-body {
    min-height: calc(100vh);
  }
}
</style>
