<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 客商编码 -->

    <a-form-item name="status"   :label="'单据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.status" id="status">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.dataStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <a-form-item name="businessType"   :label="'业务类型'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.businessType" id="businessType">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.businessType2"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <a-form-item name="gName"   :label="'商品名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.gName" />
    </a-form-item>
    <!--    制单日期起止-->
    <a-form-item name="insertTime" label="制单日期起止" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeFrom"
              id="insertTimeFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>

    </a-form-item>
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'QuoSearch'
})
const searchParam = reactive({
  status:'0',
  gName:'',
  businessType:'',
  insertTimeFrom:'',
  insertTimeTo:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
