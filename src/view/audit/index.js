import { defineAsyncComponent } from "vue"

export default  [
  {
    path: '/audit/importedCigarettes/contract',
    name: 'AeoContractList',
    meta: {
      title: '国营进口卷烟合同'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "../audit/importedCigarettes/contract/ContractList.vue"))
  },

  {
    path: '/audit/importedCigarettes/order',
    name: 'AeoOrderList',
    meta: {
      title: '国营进口卷烟进货单'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "../audit/importedCigarettes/order/head/BizIOrderHeadList.vue"))
  },

  {
    path: '/audit/importedMaterial/smokeMachine',
    name: '<PERSON>eoOrderList',
    meta: {
      title: '国营进口卷烟设备-进货单'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "../audit/smoke_machine/purchase order/head/BizSmokeMachineIncomingGoodsHeadList.vue"))
  },


  {
    path: '/audit/importedMaterial/exportGoodHead',
    name: 'AeoExportGoodsHeadList',
    meta: {
      title: '国营进口卷烟设备-出货信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "../audit/export/goods_head/head/BizExportGoodsHeadList.vue"))
  },
]
