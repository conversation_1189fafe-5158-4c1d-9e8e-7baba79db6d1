<template>
  <a-form layout="inline"  label-align="right"  :label-col="{ style: { width: '150px' } }" :model="searchParam"   class="pw-form grid-container" >

    <!-- 审批状态 -->
    <a-form-item name="apprStatus"   :label="'审批状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.apprStatus" id="approvalStatus">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.approval_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <!--  订单单据状态  -->
    <a-form-item name="dataStatus"  label="订单单据状态" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.dataStatus" id="orderDataStatus">
        <a-select-option v-for="item in productClassify.orderStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>



    <!--  合同号  -->
    <a-form-item name="contractNo"  label="合同号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNo"  allow-clear/>
    </a-form-item>



    <!--  订单号  -->
    <a-form-item name="orderNo"  label="订单号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.orderNo"  allow-clear/>
    </a-form-item>




    <!--  进货单号  -->
    <a-form-item name="purchaseOrderNo"  label="进货单号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.purchaseOrderNo" allow-clear />
    </a-form-item>


    <!--  订单制单时间   -->
    <a-form-item name="insertTime" label="订单制单时间"  class="grid-item" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeFrom"
              id="insertTimeFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder="订单制单日期起"
              allow-clear
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              id="insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder="订单制单日期止"
              allow-clear
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>


    <!--  进货数据状态  -->
    <a-form-item name="purchaseDataStatus"  label="进货单据状态" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.purchaseDataStatus" id="purchaseDataStatus">
        <a-select-option v-for="item in productClassify.orderStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!--  销售数据状态  -->
    <a-form-item name="salesDataStatus"  label="销售单据状态" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.salesDataStatus" id="salesDataStatus">
        <a-select-option v-for="item in productClassify.orderStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>



    <!--  入库回单状态  -->
    <a-form-item name="inboundReceiptStatus"  label="入库回单状态" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.inboundReceiptStatus" id="inboundReceiptStatus">
        <a-select-option v-for="item in productClassify.orderStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>



    <!--  出库回单状态  -->
    <a-form-item name="outboundReceiptStatus"  label="出库回单状态" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.outboundReceiptStatus" id="outboundReceiptStatus">
        <a-select-option v-for="item in productClassify.orderStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>





<!--    &lt;!&ndash;  主建sid  &ndash;&gt;-->
<!--    <a-form-item name="sid"  label="主建sid" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.sid" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  制单人  &ndash;&gt;-->
<!--    <a-form-item name="insertUser"  label="制单人" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.insertUser" />-->
<!--    </a-form-item>-->







<!--    &lt;!&ndash;  创建人姓名  &ndash;&gt;-->
<!--    <a-form-item name="insertUserName"  label="创建人姓名" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.insertUserName" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  更新人  &ndash;&gt;-->
<!--    <a-form-item name="updateUser"  label="更新人" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.updateUser" />-->
<!--    </a-form-item>-->





<!--    &lt;!&ndash;  更新时间   &ndash;&gt;-->
<!--    <a-form-item name="updateTime" label="更新时间"  class="grid-item" :colon="false">-->
<!--      &lt;!&ndash; Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,-->
<!--            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest-->
<!--      &ndash;&gt;-->
<!--      <a-form-item-rest>-->
<!--        <a-row>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.updateTimeForm"-->
<!--              id="updateTimeForm"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              size="small"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--          <a-col :span="2" style="text-align: center">-->
<!--            - -->
<!--          </a-col>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.updateTimeTo"-->
<!--              id="updateTimeTo"-->
<!--              size="small"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--        </a-row>-->
<!--      </a-form-item-rest>-->
<!--    </a-form-item>-->

<!--    &lt;!&ndash;  更新人姓名  &ndash;&gt;-->
<!--    <a-form-item name="updateUserName"  label="更新人姓名" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.updateUserName" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  企业代码  &ndash;&gt;-->
<!--    <a-form-item name="tradeCode"  label="企业代码" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.tradeCode" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  业务类型  &ndash;&gt;-->
<!--    <a-form-item name="businessType"  label="业务类型" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.businessType" />-->
<!--    </a-form-item>-->






<!--    &lt;!&ndash;  合同编号  &ndash;&gt;-->
<!--    <a-form-item name="contractNo"  label="合同编号" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.contractNo" />-->
<!--    </a-form-item>-->






<!--    &lt;!&ndash;  甲方  &ndash;&gt;-->
<!--    <a-form-item name="partyA"  label="甲方" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.partyA" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  乙方  &ndash;&gt;-->
<!--    <a-form-item name="partyB"  label="乙方" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.partyB" />-->
<!--    </a-form-item>-->





<!--    &lt;!&ndash;  交货日期   &ndash;&gt;-->
<!--    <a-form-item name="deliveryDate" label="交货日期"  class="grid-item" :colon="false">-->
<!--      &lt;!&ndash; Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,-->
<!--            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest-->
<!--      &ndash;&gt;-->
<!--      <a-form-item-rest>-->
<!--        <a-row>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.deliveryDateForm"-->
<!--              id="deliveryDateForm"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              size="small"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--          <a-col :span="2" style="text-align: center">-->
<!--            - -->
<!--          </a-col>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.deliveryDateTo"-->
<!--              id="deliveryDateTo"-->
<!--              size="small"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--        </a-row>-->
<!--      </a-form-item-rest>-->
<!--    </a-form-item>-->

<!--    &lt;!&ndash;  付款方式  &ndash;&gt;-->
<!--    <a-form-item name="paymentMethod"  label="付款方式" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.paymentMethod" />-->
<!--    </a-form-item>-->





<!--    &lt;!&ndash;  进口发票号码  &ndash;&gt;-->
<!--    <a-form-item name="importInvoiceNo"  label="进口发票号码" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.importInvoiceNo" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  许可证号  &ndash;&gt;-->
<!--    <a-form-item name="licenseNo"  label="许可证号" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.licenseNo" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  准运证编号  &ndash;&gt;-->
<!--    <a-form-item name="transportPermitNo"  label="准运证编号" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.transportPermitNo" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  销售发票号  &ndash;&gt;-->
<!--    <a-form-item name="salesInvoiceNo"  label="销售发票号" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.salesInvoiceNo" />-->
<!--    </a-form-item>-->













<!--    &lt;!&ndash;  版本号 &ndash;&gt;-->
<!--    <a-form-item name="versionNo"  label="版本号" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.versionNo" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  数据状态  &ndash;&gt;-->
<!--    <a-form-item name="dataStatus"  label="数据状态" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.dataStatus" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段1  &ndash;&gt;-->
<!--    <a-form-item name="extend1"  label="拓展字段1" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend1" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段2  &ndash;&gt;-->
<!--    <a-form-item name="extend2"  label="拓展字段2" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend2" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段3  &ndash;&gt;-->
<!--    <a-form-item name="extend3"  label="拓展字段3" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend3" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段4  &ndash;&gt;-->
<!--    <a-form-item name="extend4"  label="拓展字段4" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend4" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段5  &ndash;&gt;-->
<!--    <a-form-item name="extend5"  label="拓展字段5" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend5" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段6  &ndash;&gt;-->
<!--    <a-form-item name="extend6"  label="拓展字段6" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend6" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段7  &ndash;&gt;-->
<!--    <a-form-item name="extend7"  label="拓展字段7" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend7" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段8  &ndash;&gt;-->
<!--    <a-form-item name="extend8"  label="拓展字段8" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend8" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段9  &ndash;&gt;-->
<!--    <a-form-item name="extend9"  label="拓展字段9" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend9" />-->
<!--    </a-form-item>-->



<!--    &lt;!&ndash;  拓展字段10  &ndash;&gt;-->
<!--    <a-form-item name="extend10"  label="拓展字段10" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.extend10" />-->
<!--    </a-form-item>-->


<!--    &lt;!&ndash;  签订日期   &ndash;&gt;-->
<!--    <a-form-item name="dateOfSigning" label="签订日期"  class="grid-item" :colon="false">-->
<!--      &lt;!&ndash; Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,-->
<!--            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest-->
<!--      &ndash;&gt;-->
<!--      <a-form-item-rest>-->
<!--        <a-row>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.dateOfSigningForm"-->
<!--              id="dateOfSigningForm"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              size="small"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--          <a-col :span="2" style="text-align: center">-->
<!--            - -->
<!--          </a-col>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.dateOfSigningTo"-->
<!--              id="dateOfSigningTo"-->
<!--              size="small"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--        </a-row>-->
<!--      </a-form-item-rest>-->
<!--    </a-form-item>-->

<!--    &lt;!&ndash;  计划编号  &ndash;&gt;-->
<!--    <a-form-item name="planNo"  label="计划编号" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.planNo" />-->
<!--    </a-form-item>-->





<!--    &lt;!&ndash;  订单确认时间   &ndash;&gt;-->
<!--    <a-form-item name="orderConfirmationTime" label="订单确认时间"  class="grid-item" :colon="false">-->
<!--      &lt;!&ndash; Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,-->
<!--            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest-->
<!--      &ndash;&gt;-->
<!--      <a-form-item-rest>-->
<!--        <a-row>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.orderConfirmationTimeForm"-->
<!--              id="orderConfirmationTimeForm"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              size="small"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--          <a-col :span="2" style="text-align: center">-->
<!--            - -->
<!--          </a-col>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.orderConfirmationTimeTo"-->
<!--              id="orderConfirmationTimeTo"-->
<!--              size="small"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--        </a-row>-->
<!--      </a-form-item-rest>-->
<!--    </a-form-item>-->

<!--    &lt;!&ndash;  审批状态  &ndash;&gt;-->
<!--    <a-form-item name="apprStatus"  label="审批状态" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.apprStatus" />-->
<!--    </a-form-item>-->




  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'BizIOrderHeadSearch'
})

/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}

const searchParam = reactive({
  sid:'',
  insertUser:'',
  insertTime:'',
  insertUserName:'',
  updateUser:'',
  updateTime:'',
  updateUserName:'',
  tradeCode:'',
  businessType:'1',
  orderDataStatus:'',
  contractNo:'',
  orderNo:'',
  partyA:'',
  partyB:'',
  deliveryDate:'',
  paymentMethod:'',
  purchaseOrderNo:'',
  importInvoiceNo:'',
  licenseNo:'',
  transportPermitNo:'',
  salesInvoiceNo:'',
  salesContractNo:'',
  purchaseDataStatus:'',
  salesDataStatus:'',
  inboundReceiptStatus:'',
  outboundReceiptStatus:'',
  insertTimeFrom:'',
  insertTimeTo:'',
  updateTimeTo:'',
  updateTimeFrom:'',
  deliveryDateForm:'',
  deliveryDateTo:'',
  versionNo:'',
  dataStatus:'',
  extend1:'',
  extend2:'',
  extend3:'',
  extend4:'',
  extend5:'',
  extend6:'',
  extend7:'',
  extend8:'',
  extend9:'',
  extend10:'',
  dateOfSigning:'',
  planNo:'',
  orderConfirmationTime:'',
  orderConfirmationTimeTo:'',
  orderConfirmationTimeForm:'',
  apprStatus:'2'

})

defineExpose({searchParam,resetSearch});

onMounted(() => {

});


</script>

<style lang='less' scoped>

</style>
