import {ref} from "vue";
import {isNullOrEmpty} from "@/view/utils/common";
import {getOrderSupplierList} from "@/api/cs_api_constant";

/**
 * 自定义Hook函数
 */
export function useOrderColumnsCommon() {



  /**
   * 数据列表
   */
  const supplierList = ref([])





    /* 转换Columns配置 将关键属性转为 key,value形式，并且过滤操作等属性 */
  const getSupplierList  = () =>{
    getOrderSupplierList({}).then(res=>{
      // console.log('获取供应商信息未',res)
      if (!isNullOrEmpty(res.data)){
        supplierList.value = res.data
      }
    })
  }






  // 返回状态 和 方法
  return {
    supplierList,
    getSupplierList
  }

}
