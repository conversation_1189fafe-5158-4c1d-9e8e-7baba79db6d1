<template>
  <div class="cs-form">
    <a-form :loading="fromDataLoading"   ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
            :model="formData"   class=" grid-container">

      <!--  准允证编号 -->
      <a-form-item name="transportPermitNo"   :label="'准运证编号'" class="grid-item"  :colon="false">
        <a-input :disabled="showDisable"  size="small" v-model:value="formData.transportPermitNo" allow-clear />
      </a-form-item>

      <!-- 到货确认日期 -->
      <a-form-item name="arrivalConfirmDate"   :label="'到货确认日期'" class="grid-item"  :colon="false">
        <a-date-picker
          v-model:value="formData.arrivalConfirmDate"
          id="arrivalConfirmDate"
          size="small"
          valueFormat="YYYY-MM-DD"
          format="YYYY-MM-DD"
          :locale="locale"
          style="width: 100%"
          :disabled="showDisable"
          placeholder=""
        />
      </a-form-item>


      <div class="cs-submit-btn merge-3">
        <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                  :disabled="showDisable"
                  v-show="props.editConfig.editStatus !==  editStatus.SHOW " :loading="saveLoading">保存
        </a-button>
        <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
      </div>
    </a-form>


  </div>
</template>

<script setup>

  import {editStatus, productClassify} from "@/view/common/constant";
  import CsSelect from "@/components/select/CsSelect.vue"
  import {onMounted, reactive, ref, watch} from "vue";
  import ycCsApi from "@/api/ycCsApi";
  import {message} from "ant-design-vue";

  const props = defineProps({
    editConfig: {
      type: Object,
      default: () => ({})
    },
    isAllConfirmed: {
      type: Boolean,
      default: false
    }
  })

  const emit = defineEmits(['onBack'])

  const showDisable = ref(false)
  const saveLoading = ref(false)
  const formRef = ref(null);

  const formData = reactive({
    // 主键id
    id:'',
    // 业务类型
    businessType:'',
    // 数据状态
    dataState:'',
    // 版本号
    versionNo:'',
    // 企业10位编码
    tradeCode:'',
    // 组织机构代码
    sysOrgCode:'',
    // 父级id
    parentId:'',
    // 创建人
    createBy:'',
    // 创建时间
    createTime:'',
    createTimeTo:'',
    createTimeForm:'',
    // 更新人
    updateBy:'',
    // 更新时间
    updateTime:'',
    updateTimeTo:'',
    updateTimeForm:'',
    // 插入用户名
    insertUserName:'',
    // 更新用户名
    updateUserName:'',
    // 扩展字段1
    extend1:'',
    // 扩展字段2
    extend2:'',
    // 扩展字段3
    extend3:'',
    // 扩展字段4
    extend4:'',
    // 扩展字段5
    extend5:'',
    // 扩展字段6
    extend6:'',
    // 扩展字段7
    extend7:'',
    // 扩展字段8
    extend8:'',
    // 扩展字段9
    extend9:'',
    // 扩展字段10
    extend10:'',
    // 出货单号
    exportNo:'',
    // 合同号
    contractNo:'',
    // 客户
    customer:'',
    // 客户地址
    customerAddress:'',
    // 供应商
    supplier:'',
    // 贸易国别
    tradeCountry:'',
    // 经营单位
    manageUnit:'',
    // 付款方式
    paymentType:'',
    // 币种
    currency:'',
    // 运输方式
    transportType:'',
    // 价格条款
    priceTerms:'',
    // 价格条款对应港口
    priceTermsPort:'',
    // 发货单位
    deliveryUnit:'',
    // 包装种类
    packageType:'',
    // 包装数量
    packageNum:'',
    // 发货单位所在地
    deliveryUnitLocation:'',
    // 装运人shipper
    shipper:'',
    // 收货人consignee
    consignee:'',
    // 通知人notify party
    notifyParty:'',
    // 总毛重
    grossWeight:'',
    // 总净重
    netWeight:'',
    // 总皮重
    tareWeight:'',
    // 唛头
    mark:'',
    // 装运港
    portOfShipment:'',
    // 目的地/港
    portOfDestination:'',
    // 装运期限
    shipmentDate:'',
    // 险别
    insuranceType:'',
    // 保费币种
    insuranceCurrency:'',
    // 投保加成%
    insuranceAddRate:'',
    // 保费费率(%)
    insuranceRate:'',
    // 保险费
    insuranceFee:'',
    // 投保人
    insurer:'',
    // 运费
    freight:'',
    // 运费币种
    freightCurrency:'',
    // 仓储地址
    warehouseAddress:'',
    // 联系人
    contactPerson:'',
    // 联系电话
    contactPhone:'',
    // 备注
    remark:'',
    // 发送报关
    sendCustoms:'',
    // 确认时间
    confirmTime:'',
    confirmTimeTo:'',
    confirmTimeForm:'',
    // 准运证编号，用户录入
    transportPermitNo:'',
    // 准运证申办日期，用户录入
    transportPermitApplyDate:'',
    transportPermitApplyDateTo:'',
    transportPermitApplyDateForm:'',
    // 到货确认日期，用户录入
    arrivalConfirmDate:'',
    arrivalConfirmDateTo:'',
    arrivalConfirmDateForm:''
  })

  const fromDataLoading = ref(false)




  // 校验规则
  const rules = {
    transportPermitNo:[
      {max: 100, message: '准运证编号，用户录入长度不能超过 100位字节', trigger: 'blur'}
    ],
    transportPermitApplyDate:[
    ],
    arrivalConfirmDate:[
    ]
  }

  const onBack = (val) => {
    emit('onBack', val);
  };

  const handlerSave = () => {
    formRef.value
      .validate()
      .then(() => {
        saveLoading.value = true;
        // 插入数据
        window.majesty.httpUtil.putAction(`${ycCsApi.bizExportGoodsHead.update}/${formData.id}`,formData).then(res=>{
          if (res.code === 200) {
            message.success('修改成功')
            // 重新赋值当前数据
            Object.assign(formData, res.data);
            // 重新赋值下拉框数据
            // emitEvent('refresh-export-goods-head-search')
          }else {
            message.error(res.message);
          }
        }).finally(() => {
          saveLoading.value = false;
        })

      })
      .catch(error => {
        console.log('validate failed', error);
      })
  };


  onMounted(()=>{
    if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
      showDisable.value = false
      Object.assign(formData, {});
    }
    // 初始化数据
    if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
      Object.assign(formData, props.editConfig.editData)
      showDisable.value = false
    }
    if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
      Object.assign(formData, props.editConfig.editData)
      showDisable.value = true
    }

    if(props.isAllConfirmed){
      showDisable.value = true;
    }
  })

  /* 监控是否全部确认 */
  watch(()=>props.isAllConfirmed, (val)=>{
    console.log('----------------val----------------',val)
    if (val === true) {
      // 全部确认后 可以进行保存
      showDisable.value = true
    }else {
      showDisable.value = false
    }
  },{deep: true})
</script>

<style lang="less" scoped>


</style>
