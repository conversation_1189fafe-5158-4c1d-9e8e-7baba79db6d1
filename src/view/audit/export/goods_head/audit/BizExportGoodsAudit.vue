<template>
  <section>
    <a-card :bordered="false">
      <s-table
        ref="aeoTable"
        class="cs-action-item remove-table-border-add-bg"
        size="small"
        bordered
        :columns="aeoColumns"
        :data-source="dataAeoSource"
        row-key="apprDate"
        :pagination="false"
        :row-height="30"
        :row-hover-delay="5"
        :header-height="30"
        :range-selection="false"
      >
        <!-- 空数据 -->
        <template #emptyText>
          <a-empty description="暂无数据" />
        </template>
      </s-table>
    </a-card>
  </section>
</template>

<script setup>
import {ref, watch, onMounted, computed} from 'vue'
import {message} from 'ant-design-vue'
import ycCsApi from '@/api/ycCsApi'

// props
const props = defineProps({
  headId: {
    type: String,
    default: () => ''
  },
})

// 定义组件名称
defineOptions({
  name: 'BizExportGoodsAudit',
})
// 审批数据
const dataAeoSource = ref([])

// 列定义
const aeoColumns = computed(() => [
  {
    width: 160,
    ellipsis: true,
    align: 'center',
    dataIndex: 'statusName',
    title: '节点'
  },
  {
    width: 160,
    ellipsis: true,
    align: 'center',
    dataIndex: 'userName',
    title: '操作人'
  },
  {
    width: 160,
    ellipsis: true,
    align: 'center',
    dataIndex: 'apprDate',
    title: '时间'
  },
  {
    ellipsis: true,
    align: 'center',
    dataIndex: 'apprNote',
    title: '内审意见'
  }
])

// 获取数据
const getAeoData = async (sid) => {
  if (!sid) {
    dataAeoSource.value = []
    return
  }
  let url = ycCsApi.aeoManage.aeoReview.selectListBySid

  try {
    const res = await window.majesty.httpUtil.postAction(url, {businessSid: sid})
    dataAeoSource.value = res

  } catch (e) {
    dataAeoSource.value = []
    message.error('获取内审信息失败')
  }
}

// 监听sid变化
watch(() => props.headId, (val) => {
  getAeoData(val)
})

// 首次加载
onMounted(() => {
  if (props.headId) {
    getAeoData(props.headId)
  }
})


</script>

<style lang="less" scoped>
.s-table .ant-table-placeholder {
  min-height: 100px;
}
</style>
