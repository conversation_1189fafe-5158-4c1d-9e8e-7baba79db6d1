<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header" v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle">
        <a-tab-pane key="headTab" tab="出货信息1">
          <biz-export-goods-head-edit ref="headTab" :edit-config="editConfig" @on-back="editBack"  />
        </a-tab-pane>
        <!-- 外销发票 -->
        <a-tab-pane key="invoiceTab" tab="外销发票" v-if="showBody">
          <biz-export-goods-sell-head-edit :edit-config="editConfig" :head-id="headId"  :operation-status="editConfig.editStatus" @on-back="editBack" :is-all-confirmed="isAllConfirmed" />
        </a-tab-pane>
        <a-tab-pane key="documentTab" tab="准运与到货">
          <biz-export-goods-document ref="documentTab" :editConfig="editConfig" :is-all-confirmed="isAllConfirmed" @on-back="editBack" />
        </a-tab-pane>
        <a-tab-pane key="attachTab" tab="归档附件">
          <biz-export-goods-attach :head-id="headId"  :operation-status="editConfig.editStatus" :edit-config="editConfig" :is-all-confirmed="isAllConfirmed" />
        </a-tab-pane>
        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000" />
          </div>
        </template>
      </a-tabs>
    </div>
  </section>
</template>

<script setup>
import {ref, reactive, watch, onMounted} from 'vue';
import BizExportGoodsHeadEdit from '@/view/audit/export/goods_head/head/BizExportGoodsHeadEdit.vue';
import { GlobalIcon } from '@/components/icon';
import {editStatus} from "@/view/common/constant";
import BizExportGoodsDocument from "@/view/audit/export/goods_head/document/BizExportGoodsDocument.vue";
import BizExportGoodsAttach from "@/view/audit/export/goods_head/attach/BizExportGoodsAttch.vue";
import BizExportGoodsSellHeadEdit from "@/view/audit/export/goods_head/invoice/BizExportGoodsSellHeadEdit.vue";

  defineOptions({
    name: 'BizExportGoodsHeadTab',
  });

  const emit = defineEmits(['onEditBack']);

  const props = defineProps({
    editConfig: {
      type: Object,
      default: () => ({}),
    },
  });

  const tabBarStyle = {
    background: '#fff',
    position: 'sticky',
    top: '0',
    zIndex: '100',
  };

  const tabName = ref('headTab');
  const headId = ref('');
  // 是否显示子模块
  const showBody = ref(false)
  const isAllConfirmed = ref(false)

  /* 返回tab界面 */
  const editBack = (val) => {
    // console.log('val', val)
    if (val.editStatus === editStatus.EDIT){
      showBody.value = val.showBody
      if(val.editData != null){
        headId.value =  val.editData.id
        props.editConfig.editStatus = val.editStatus
        props.editConfig.editData = val.editData
        // 这个界面只有一个确认
        showBody.value = val.editData.dataState === '1';
        isAllConfirmed.value = val.isAllConfirmed
      }
    }else {
      // 如果val === false 返回进行刷新界面
      emit('onEditBack', val)
    }
  }

  onMounted(()=>{
    // console.log('props.editConfig', props.editConfig)
    if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
      console.log('编辑：',props.editConfig.editData)
      headId.value = props.editConfig.editData.id
      // 判断表头单据是否已经确认
      showBody.value = props.editConfig.editData.dataState === '1';
      // 判断是否所有子模块都已经确认
      isAllConfirmed.value = (props.editConfig.editData.dataState === '1' && props.editConfig.editData.invoiceDataState === '1')
                             ||
                             (props.editConfig.editData.dataState === '2' && props.editConfig.editData.invoiceDataState === '2');

      console.log('是否全部确认',isAllConfirmed.value)
    }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW ) {
      headId.value = props.editConfig.editData.id
      // 判断表头单据是否已经确认
      showBody.value = props.editConfig.editData.dataState === '1';
      // 判断是否所有子模块都已经确认
      isAllConfirmed.value = true
    }

  })


</script>

<style lang="less" scoped>

</style>
