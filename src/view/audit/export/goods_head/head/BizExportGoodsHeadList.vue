 <template>
  <!-- 第9条线-非国营贸易出口辅料-出货信息表头 -->
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  查询
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <!-- 查询按钮组件 -->
          <div ref="area_search">
            <div v-show="showSearch">
              <biz-export-goods-head-search ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
<!--          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:add']">-->
<!--            <a-button size="small" @click="handlerAdd" >-->
<!--              <template #icon>-->
<!--                <GlobalIcon type="plus" style="color:green"/>-->
<!--              </template>-->
<!--              {{localeContent('m.common.button.add')}}-->
<!--            </a-button>-->
<!--          </div>-->
<!--          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:update']">-->
<!--            <a-button  size="small"  @click="handlerEdit">-->
<!--              <template #icon>-->
<!--                <GlobalIcon type="form" style="color:orange"/>-->
<!--              </template>-->
<!--              {{localeContent('m.common.button.update')}}-->
<!--            </a-button>-->
<!--          </div>-->
<!--          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:delete']">-->
<!--            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">-->
<!--              <template #icon>-->
<!--                <GlobalIcon type="delete" style="color:red"/>-->
<!--              </template>-->
<!--              {{localeContent('m.common.button.delete')}}-->
<!--            </a-button>-->
<!--          </div>-->
<!--          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:export']">-->
<!--            <a-button  size="small" :loading="exportLoading" @click="handlerExport">-->
<!--              <template #icon>-->
<!--                <GlobalIcon type="folder-open" style="color:orange"/>-->
<!--              </template>-->
<!--              {{localeContent('m.common.button.export')}}-->
<!--            </a-button>-->
<!--          </div>-->
<!--          <div class="cs-action-btn-item"  v-has="['yc-cs:smoke_machine:import']">-->
<!--            <a-button  size="small"  @click="handlerCancelExport" :loading="cancelLoading">-->
<!--              <template #icon>-->
<!--                <GlobalIcon type="close" style="color:red"/>-->
<!--              </template>-->
<!--              作废-->
<!--            </a-button>-->
<!--          </div>-->
          <!-- 添加内审通过按钮 -->
          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:audit']">
            <a-button size="small" :loading="auditLoading" @click="handlerAudit">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              内审通过
            </a-button>
          </div>
          <!-- 添加内审退回按钮 -->
          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:reject']">
            <a-button size="small" :loading="rejectLoading" @click="handlerReject">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              内审退回
            </a-button>
          </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            v-if="totalColumns.length > 0"
            :resId="tableKey"
            :tableKey="tableKey+'-smoke_machine_code'"
            :initSettingColumns="totalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>


      </div>

      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-item  remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          :custom-row="customRow"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
          :animate-rows="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
<!--                <div>-->
<!--                  <a-button-->
<!--                    size="small"-->
<!--                    type="link"-->
<!--                    @click="handleEditByRow(record)"-->
<!--                    :style="operationEdit('edit')">-->

<!--                    <template #icon>-->
<!--                      <GlobalIcon type="form" style="color:#e93f41"/>-->
<!--                    </template>-->
<!--                  </a-button>-->
<!--                </div>-->


                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleViewByRow(record)"
                    :style="operationEdit('view')"
                  >
                    <template #icon>
                      <GlobalIcon type="search" style="color:#1677ff"/>
                    </template>
                  </a-button>
                </div>
              </div>
            </template>
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <!-- <biz-export-goods-head-edit :editConfig="editConfig" @on-back="handlerOnBack" /> -->
      <!-- <smoke_machine-head-edit :editConfig="editConfig" @on-back="handlerOnBack" /> -->
      <biz-export-goods-head-tab :edit-config="editConfig" @on-edit-back="handlerOnBack" />
    </div>


    <!-- 导入数据 -->
    <ImportIndex :importShow="importShow" :importConfig="importConfig"   @onImportSuccess="importSuccess"></ImportIndex>


  </section>


</template>

<script setup>
  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {createVNode, h, onMounted, provide, reactive, ref} from "vue";
  import {message, Modal, Tag} from "ant-design-vue";
  import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  import {ImportIndex} from 'yao-import'
  import {localeContent} from "@/view/utils/commonUtil";
  import { useImport } from "@/view/common/useImport";
  import ycCsApi from "@/api/ycCsApi";
  import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
  import {useRoute} from "vue-router";
  import {editStatus, productClassify} from "@/view/common/constant";
  import BizExportGoodsHeadEdit from "@/view/audit/export/goods_head/head/BizExportGoodsHeadEdit.vue";
  import BizExportGoodsHeadSearch from "@/view/audit/export/goods_head/head/BizExportGoodsHeadSearch.vue";
  import BizExportGoodsHeadTab from "@/view/audit/export/goods_head/BizExportGoodsHeadTab.vue";
  import {useColumnsRender} from "@/view/common/useColumnsRender";
  const  { importConfig } = useImport()
  const  { inputFormatter, inputParser,formatSpecifiedNumber,cmbShowRender } = useColumnsRender()
  /* 引入通用方法 */
  const {
    editConfig,
    show,
    page,
    showSearch,
    headSearch,
    handleEditByRow,
    handleViewByRow,
    operationEdit,
    onPageChange,
    handleShowSearch,
    handlerSearch,
    dataSourceList,
    tableLoading,
    getTableScroll,
    exportLoading,
    getList,
    ajaxUrl,
    doExport,
    handlerRefresh,
    customRow

  } = useCommon()



  defineOptions({
    name: 'BizExportGoodsHeadList',
  });

  const totalColumns = ref([
    {
      width: 150,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '审核状态',
      width: 200,
      align: 'center',
      dataIndex: 'approvalStatus',
      key: 'approvalStatus',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red','black'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.approval_status))
      },
    },
    {
      title: '出货单号',
      width: 200,
      align: 'center',
      dataIndex: 'exportNo',
      key: 'exportNo',
    },
    {
      title: '合同号',
      width: 200,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
    },
    {
      title: '供应商',
      width: 200,
      align: 'center',
      dataIndex: 'supplier',
      key: 'supplier',
      customRender: ({ text }) => {
        return cmbShowRender(text,customerList.value)
      }
    },
    {
      title: '客户',
      width: 200,
      align: 'center',
      dataIndex: 'customer',
      key: 'customer',
      customRender: ({ text }) => {
        return cmbShowRender(text,customerList.value)
      }
    },
    {
      title: '总金额',
      width: 200,
      align: 'center',
      dataIndex: 'listTotal',
      key: 'listTotal',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '目的地/港',
      width: 200,
      align: 'center',
      dataIndex: 'portOfDestination',
      key: 'portOfDestination',
      customRender: ({ text }) => {
        return cmbShowRender(text,portList.value)
      }
    },
    {
      title: '币种',
      width: 200,
      align: 'center',
      dataIndex: 'currency',
      key: 'currency',
      customRender: ({ text }) => {
        return cmbShowRender(text,currList.value)
      }
    },
    {
      title: '出货单据状态',
      width: 200,
      align: 'center',
      dataIndex: 'dataState',
      key: 'dataState',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red','black'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderStatus))
      },
    },
    {
      title: '出货单确认时间',
      width: 200,
      align: 'center',
      dataIndex: 'confirmTime',
      key: 'confirmTime',
    },
    {
      title: '制单人',
      width: 200,
      align: 'center',
      dataIndex: 'createBy',
      key: 'createBy',
    },
    {
      title: '制单时间',
      width: 200,
      align: 'center',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '发送财务系统',
      width: 200,
      align: 'center',
      dataIndex: 'invoiceSendFinance',
      key: 'invoiceSendFinance',
      customRender: ({ text }) => {
        return cmbShowRender(text,productClassify.yesOrNoStatus)
      }
    },
    {
      title: '是否红冲',
      width: 200,
      align: 'center',
      dataIndex: 'invoiceIsRedFlush',
      key: 'invoiceIsRedFlush',
      customRender: ({ text }) => {
        return cmbShowRender(text,productClassify.yesOrNoStatus)
      }
    },
    {
      title: '外销发票状态',
      width: 200,
      align: 'center',
      dataIndex: 'invoiceDataState',
      key: 'invoiceDataState',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red','black'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderStatus))
      },
    },
    {
      title: '外销发票确认时间',
      width: 200,
      align: 'center',
      dataIndex: 'invoiceConfirmTime',
      key: 'invoiceConfirmTime',
    }

  ])



  const importShow = ref(false)





  const tableHeight = ref('')

  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData:[],
    loading: false,
  });



  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };


  /* 按钮loading */
  const deleteLoading = ref(false)
  const auditLoading = ref(false)
  const rejectLoading = ref(false)


  /* 返回事件 */
  const handlerOnBack = (flag) => {
    show.value = !show.value;
    // 返回清空选择数据
    gridData.selectedData = [];
    gridData.selectedRowKeys = [];
    editConfig.editData = {}
    if (flag){
      getList()
    }
  }

  /* 新增数据 */
  const handlerAdd = ()=>{
    editConfig.value.editStatus = editStatus.ADD
    show.value = !show.value;
  }


  /* 编辑数据 */
  const handlerEdit = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1){
      message.warning('只能选择一条数据')
      return
    }
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData =  gridData.selectedData[0]

    show.value =!show.value;
  }


  /* 删除数据 */
  const handlerDelete = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk() {
        deleteLoading.value = true
        window.majesty.httpUtil.deleteAction(`${ycCsApi.bizExportGoodsHead.delete}/${gridData.selectedRowKeys}`).then(res => {
          if (res.code === 200) {
            message.success('删除成功');
          }else {
            message.error(res.message);
          }
        }).finally(()=>{
          deleteLoading.value = false
        })
      },
      onCancel() {

      },
    });

  }





  /* 导出事件 */
  const handlerExport = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
    doExport(`出货信息表头${timestamp}.xlsx`, totalColumns)
  }




  /* 自定义设置 */
  /* 显示列数据 */
  const showColumns =  ref([])

  /* 唯一键 */
  const tableKey = ref('')
  tableKey.value = window.$vueApp ? window.majesty.router.patch : useRoute().path

  /* 选中visible为true的数据进行显示 */
  const customColumnChange = (settingColumns)  => {
    let tempColumns = []
    tempColumns = settingColumns.filter((item) => item.visible === true);
    showColumns.value = [...tempColumns]
  }



  const customerList = ref([]);
  const currList = ref([]);
  const priceTermList = ref([]);
  const packageList = ref([]);
  const cityList = ref([]);
  const insuranceTypeList = ref([]);
  const portList = ref([]);
  const fromDataLoading = ref(false);
  const getCommonKeyValueList = async () => {
    fromDataLoading.value = true;
    try {
      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.getCommonKeyValueList,{});
      if (res.code === 200) {
        customerList.value = res.data.customerList;
        currList.value = res.data.currList;
        priceTermList.value = res.data.priceTermList;
        packageList.value = res.data.packageList;
        cityList.value = res.data.cityList;
        insuranceTypeList.value = res.data.insuranceTypeList;
        portList.value = res.data.portList;
      }else {
        message.error(res.message);
      }
    }catch(err) {
      console.log(err);
    }finally {
      fromDataLoading.value = false;
    }

  }


  const cancelLoading = ref(false);
  const handlerCancelExport = () => {
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '作废',
      cancelText: '取消',
      content: '确认作废所选项吗？',
      onOk() {
        cancelLoading.value = true
        window.majesty.httpUtil.putAction(`${ycCsApi.bizExportGoodsHead.cancel}/${gridData.selectedRowKeys}`).then(res => {
          if (res.code === 200) {
            message.success('作废成功');
          }else {
            message.error(res.message);
          }
        }).finally(()=>{
          cancelLoading.value = false
        })
      },
      onCancel() {

      },
    });
  }

  /* 内审通过事件 */
  const handlerAudit = () => {
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }

    // 审核意见输入框
    const auditOpinion = ref('同意审批')

    // 弹出审核确认框
    Modal.confirm({
      title: '内审通过',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: createVNode('div', {}, [
        createVNode('div', { style: 'margin-top: 10px;' }, [
          createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
          createVNode('textarea', {
            value: auditOpinion.value,
            onInput: (e) => { auditOpinion.value = e.target.value },
            style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
            placeholder: '请输入审核意见'
          })
        ])
      ]),
      onOk() {
        auditLoading.value = true
        const params = {
          ids: gridData.selectedRowKeys,
          apprMessage: auditOpinion.value || '同意审批',
          businessType: '9',
          billType: 'order',
        }

        // 调用audit接口
        window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.audit, params)
          .then(res => {
            if (res.code === 200) {
              message.success("内审通过成功！")
              // 刷新列表
              getList()
            } else {
              message.error(res.message || '内审失败')
            }
          })
          .catch(error => {
            console.error('内审失败:', error)
            message.error(error.message || error || '内审失败，请重试')
          })
          .finally(() => {
            auditLoading.value = false
          })
      },
      onCancel() {
        // 取消操作
      },
    });
  }

  /* 内审退回事件 */
  const handlerReject = () => {
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }

    // 审核意见输入框
    const auditOpinion = ref('审批退回')

    // 弹出审核退回确认框
    Modal.confirm({
      title: '内审退回',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: createVNode('div', {}, [
        createVNode('div', { style: 'margin-top: 10px;' }, [
          createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
          createVNode('textarea', {
            value: auditOpinion.value,
            onInput: (e) => { auditOpinion.value = e.target.value },
            style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
            placeholder: '请输入审核意见'
          })
        ])
      ]),
      onOk() {
        rejectLoading.value = true
        const params = {
          ids: gridData.selectedRowKeys,
          apprMessage: auditOpinion.value || '审批退回',
          businessType: '9',
          billType: 'order',
        }

        // 调用reject接口进行退回
        window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.reject, params)
          .then(res => {
            if (res.code === 200) {
              message.success("内审退回成功！")
              // 刷新列表
              getList()
            } else {
              throw new Error(res.message || '内审退回失败')
            }
          })
          .catch(error => {
            console.error('内审退回失败:', error)
            message.error(error.message || error || '内审退回失败，请重试')
          })
          .finally(() => {
            rejectLoading.value = false
          })
      },
      onCancel() {
        // 取消操作
      },
    });
  }


  onMounted(fn => {
    getCommonKeyValueList()

    ajaxUrl.selectAllPage = ycCsApi.bizExportGoodsHead.aeoList
    ajaxUrl.exportUrl = ycCsApi.bizExportGoodsHead.export

    tableHeight.value = getTableScroll(100,'');

    getList()


  })

</script>

<style lang="less" scoped>


</style>
