<template>
  <section>
    <!-- 第9条线-非国营贸易出口辅料-出货信息表头 -->
    <a-card size="small" title="出货信息表头" class="cs-card-form">
      <div class="cs-form">
        <a-form :loading="fromDataLoading"   ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

            <!-- 出货单号 字符型（60） 文本 是 用户录入 唯一性校验 -->
            <a-form-item name="exportNo"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('exportNo')"
                    @click="handleLabelClick('exportNo')"
                  >
                    出货单号
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.exportNo" allow-clear />
            </a-form-item>
            <!-- 合同号 字符型（60） 文本 是 <新增>操作带出，不允许修改 -->
            <a-form-item name="contractNo"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('contractNo')"
                    @click="handleLabelClick('contractNo')"
                  >
                    合同号
                  </span>
                </template>
                <a-input :disabled="true"  size="small" v-model:value="formData.contractNo"  allow-clear />
            </a-form-item>
            <!-- 客户 字符型（200） 下拉框 是 <新增>操作带出-客户，不允许修改 -->
            <a-form-item name="customer" class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('customer')"
                  @click="handleLabelClick('customer')"
                >
                  客户
                </span>
              </template>
              <cs-select
                :disabled="true"
                :options="customerList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                v-model:value="formData.customer"
                id="customer"
              />
            </a-form-item>
            <!-- 客户地址 字符型（60） 文本 是 <新增>操作带出，允许修改 根据客户，关联【客商信息】带出"客商地址" -->
            <a-form-item name="customerAddress"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('customerAddress')"
                    @click="handleLabelClick('customerAddress')"
                  >
                    客户地址
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.customerAddress"   allow-clear/>
            </a-form-item>
            <!-- 供应商 字符型（200） 下拉框 是 <新增>操作带出-供应商，不允许修改 -->
            <a-form-item name="supplier"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('supplier')"
                  @click="handleLabelClick('supplier')"
                >
                  供应商
                </span>
              </template>
              <cs-select
                :options="customerList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="true"
                v-model:value="formData.supplier"
                id="supplier"
              />
            </a-form-item>
            <!-- 贸易国别 字符型（60） 文本 是 <新增>操作带出，允许修改 根据合同客户，关联【客商信息】带出"贸易国别" -->
            <a-form-item name="tradeCountry"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('tradeCountry')"
                    @click="handleLabelClick('tradeCountry')"
                  >
                    贸易国别
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.tradeCountry"   allow-clear />
            </a-form-item>
            <!-- 经营单位 字符型（200） 下拉框 是 【基础资料-客商信息】 默认"中国烟草上海进出口有限责任公司"允许修改 -->
            <a-form-item name="manageUnit"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('manageUnit')"
                  @click="handleLabelClick('manageUnit')"
                >
                  经营单位
                </span>
              </template>
              <cs-select
                :options="customerList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.manageUnit"
                id="manageUnit"
              />
            </a-form-item>
            <!-- 付款方式 字符型（50） 文本 否 <新增>操作带出-收汇方式，不允许修改 -->
            <a-form-item name="paymentType"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('paymentType')"
                    @click="handleLabelClick('paymentType')"
                  >
                    付款方式
                  </span>
                </template>
                <a-input :disabled="true"  size="small" v-model:value="formData.paymentType"  allow-clear />
            </a-form-item>
            <!-- 币种 字符型（10） 下拉框 是 <新增>操作带出，不允许修改 -->
            <a-form-item name="currency"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('currency')"
                  @click="handleLabelClick('currency')"
                >
                  币种
                </span>
              </template>
              <cs-select
                :options="currList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="true"
                v-model:value="formData.currency"
                id="currency"
              />
            </a-form-item>
            <!-- 运输方式 字符型（10） 下拉框 否 默认0海运，允许修改 系统参数0海运1空运2陆运 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="transportType"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('transportType')"
                  @click="handleLabelClick('transportType')"
                >
                  运输方式
                </span>
              </template>
              <cs-select
                :options="productClassify.transportMode"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.transportType"
                id="transportType"
              />
            </a-form-item>
            <!-- 价格条款 字符型（20） 下拉框 否 <新增>操作带出，不允许修改 -->
            <a-form-item name="priceTerms"  class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('priceTerms')"
                  @click="handleLabelClick('priceTerms')"
                >
                  价格条款
                </span>
              </template>
              <cs-select
                :options="priceTermList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="true"
                v-model:value="formData.priceTerms"
                id="priceTerms"
              />
            </a-form-item>
            <!-- 价格条款对应港口 字符型（50） 下拉框 否 <新增>操作带出，不允许修改 -->
            <a-form-item name="priceTermsPort"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('priceTermsPort')"
                  @click="handleLabelClick('priceTermsPort')"
                >
                  价格条款对应港口
                </span>
              </template>
              <cs-select
                :options="productClassify.priceTermPort"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="true"
                v-model:value="formData.priceTermsPort"
                id="priceTermsPort"
              />
            </a-form-item>
            <!-- 发货单位 字符型（60） 文本 是 <新增>操作带出-供应商，不允许修改 -->
            <a-form-item name="deliveryUnit"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('deliveryUnit')"
                    @click="handleLabelClick('deliveryUnit')"
                  >
                    发货单位
                  </span>
                </template>
                <a-input :disabled="true"  size="small" v-model:value="formData.deliveryUnit"   allow-clear />
            </a-form-item>
            <!-- 包装种类 字符型（30） 下拉框 否 【企业自定义参数-包装信息】 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="packageType"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('packageType')"
                  @click="handleLabelClick('packageType')"
                >
                  包装种类
                </span>
              </template>
              <cs-select
                :options="packageList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.packageType"
                id="packageType"
              />
            </a-form-item>
            <!-- 包装数量 数值型（10） 文本 否 用户录入 <新增>操作，根据合同号关联<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="packageNum"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('packageNum')"
                    @click="handleLabelClick('packageNum')"
                  >
                    包装数量
                  </span>
                </template>
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.packageNum"  style="width: 100%" allow-clear />
            </a-form-item>
            <!-- 发货单位所在地 字符型（50） 下拉框 否 企业自定义参数-城市 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="deliveryUnitLocation"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('deliveryUnitLocation')"
                  @click="handleLabelClick('deliveryUnitLocation')"
                >
                  发货单位所在地
                </span>
              </template>
              <cs-select
                :options="cityList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.deliveryUnitLocation"
                id="deliveryUnitLocation"
              />
            </a-form-item>
            <!-- 装运人SHIPPER 数值型（300） 文本 否 <新增>操作带出-根据供应商关联【客商信息】-装运人SHIPPER，可修改 -->
            <a-form-item name="shipper"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('shipper')"
                    @click="handleLabelClick('shipper')"
                  >
                    装运人SHIPPER
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.shipper"   allow-clear/>
            </a-form-item>
            <!-- 收货人CONSIGNEE 数值型（300） 文本 否 <新增>操作带出-根据合同客户关联【客商信息】-收货人CONSIGNEE，可修改 -->
            <a-form-item name="consignee"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('consignee')"
                    @click="handleLabelClick('consignee')"
                  >
                    收货人CONSIGNEE
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.consignee"  allow-clear />
            </a-form-item>
            <!-- 通知人NOTIFY PARTY 数值型（300） 文本 否 <新增>操作带出-根据客户关联【客商信息】-通知人NOTIFY PARTY，可修改 -->
            <a-form-item name="notifyParty"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('notifyParty')"
                    @click="handleLabelClick('notifyParty')"
                  >
                    通知人NOTIFY PARTY
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.notifyParty"  allow-clear />
            </a-form-item>
            <!-- 总毛重 数值型（19，6） 文本 否 用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="grossWeight"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('grossWeight')"
                    @click="handleLabelClick('grossWeight')"
                  >
                    总毛重
                  </span>
                </template>
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.grossWeight" style="width: 100%"  allow-clear />
            </a-form-item>
            <!-- 总净重 数值型（19，6） 文本 否 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="netWeight"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('netWeight')"
                    @click="handleLabelClick('netWeight')"
                  >
                    总净重
                  </span>
                </template>
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.netWeight" style="width: 100%" allow-clear />
            </a-form-item>
            <!-- 总皮重 数值型（19，6） 文本 否 系统计算=总毛重-总净重，不允许修改 -->
            <a-form-item name="tareWeight"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('tareWeight')"
                    @click="handleLabelClick('tareWeight')"
                  >
                    总皮重
                  </span>
                </template>
                <a-input-number :disabled="true"  size="small" v-model:value="formData.tareWeight" style="width: 100%" allow-clear />
            </a-form-item>
            <!-- 唛头 数值型（300） 文本 否 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="mark"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('mark')"
                    @click="handleLabelClick('mark')"
                  >
                    唛头
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.mark"  allow-clear />
            </a-form-item>
            <!-- 装运港 字符型（50） 下拉框 否 海关参数-港口 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="portOfShipment"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('portOfShipment')"
                  @click="handleLabelClick('portOfShipment')"
                >
                  装运港
                </span>
              </template>
              <cs-select
                :options="portList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.portOfShipment"
                id="portOfShipment"
              />
            </a-form-item>
            <!-- 目的地/港 字符型（50） 下拉框 否 海关参数-港口 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="portOfDestination"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('portOfDestination')"
                  @click="handleLabelClick('portOfDestination')"
                >
                  目的地/港
                </span>
              </template>
              <cs-select
                :options="portList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.portOfDestination"
                id="portOfDestination"
              />
            </a-form-item>
            <!-- 装运期限 日期型（10） 日期控件 否 用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="shipmentDate"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('shipmentDate')"
                  @click="handleLabelClick('shipmentDate')"
                >
                  装运期限
                </span>
              </template>
              <a-date-picker
                v-model:value="formData.shipmentDate"
                id="shipmentDate"
                size="small"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                style="width: 100%"
                :disabled="showDisable"
                placeholder=""
              />
            </a-form-item>
            <!-- 险别 字符型（100） 下拉框 否 企业自定义参数-保险类别 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="insuranceType"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('insuranceType')"
                  @click="handleLabelClick('insuranceType')"
                >
                  险别
                </span>
              </template>
              <cs-select
                :options="insuranceTypeList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                :disabled="showDisable"
                show-search
                v-model:value="formData.insuranceType"
                id="insuranceType"
              />
            </a-form-item>
            <!-- 保费币种 字符型（10） 下拉框 否 海关参数自定义，显示为三位英文字母，默认为USD，允许修改 -->
            <a-form-item name="insuranceCurrency"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('insuranceCurrency')"
                  @click="handleLabelClick('insuranceCurrency')"
                >
                  保费币种
                </span>
              </template>
              <cs-select
                :options="currList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                :disabled="showDisable"
                show-search
                v-model:value="formData.insuranceCurrency"
                id="insuranceCurrency"
              />
            </a-form-item>
            <!-- 投保加成% 数值型（19，6） 文本 否 默认1%，允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="insuranceAddRate"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('insuranceAddRate')"
                    @click="handleLabelClick('insuranceAddRate')"
                  >
                    投保加成%
                  </span>
                </template>
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.insuranceAddRate" style="width: 100%"  addon-after="%" allow-clear/>
            </a-form-item>
            <!-- 保费费率(%) 数值型（19，6） 文本 否 默认0.0267%,允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="insuranceRate"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('insuranceRate')"
                    @click="handleLabelClick('insuranceRate')"
                  >
                    保费费率(%)
                  </span>
                </template>
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.insuranceRate" style="width: 100%"  addon-after="%" allow-clear/>
            </a-form-item>
            <!-- 保险费 数值型（19，6） 文本 否 系统计算=表体金额汇总*（1+投保加成）*保费费率 不允许修改 -->
            <a-form-item name="insuranceFee"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('insuranceFee')"
                    @click="handleLabelClick('insuranceFee')"
                  >
                    保险费
                  </span>
                </template>
                <a-input-number :disabled="true"  size="small" v-model:value="formData.insuranceFee" style="width: 100%;"  allow-clear />
            </a-form-item>
            <!-- 投保人 字符型（200） 下拉框 是 【基础资料-客商信息】 -->
            <a-form-item name="insurer"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('insurer')"
                  @click="handleLabelClick('insurer')"
                >
                  投保人
                </span>
              </template>
              <cs-select
                :options="customerList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.insurer"
                id="insurer"
              />
            </a-form-item>
            <!-- 运费 数值型（19，6） 文本 否 用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="freight"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('freight')"
                    @click="handleLabelClick('freight')"
                  >
                    运费
                  </span>
                </template>
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.freight" style="width: 100%"  allow-clear/>
            </a-form-item>
            <!-- 运费币种 字符型（10） 下拉框 否 海关参数自定义，显示为三位英文字母，默认为USD，允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="freightCurrency"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('freightCurrency')"
                    @click="handleLabelClick('freightCurrency')"
                  >
                    运费币种
                  </span>
                </template>
                <cs-select
                  :options="currList"
                  :combine-display="true"
                  option-filter-prop="label"
                  option-label-prop="key"
                  allow-clear
                  show-search
                  :disabled="showDisable"
                  v-model:value="formData.freightCurrency"
                  id="freightCurrency"
                />
            </a-form-item>
            <!-- 仓储地址 数值型（300） 文本 否 <新增>操作带出-根据委托方关联【客商信息】-仓储地址，可修改 -->
            <a-form-item name="warehouseAddress"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('warehouseAddress')"
                    @click="handleLabelClick('warehouseAddress')"
                  >
                    仓储地址
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.warehouseAddress"  allow-clear/>
            </a-form-item>
            <!-- 联系人 数值型（20） 文本 否 <新增>操作带出-根据委托方关联【客商信息】-联系人，可修改 -->
            <a-form-item name="contactPerson"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('contactPerson')"
                    @click="handleLabelClick('contactPerson')"
                  >
                    联系人
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.contactPerson"  allow-clear/>
            </a-form-item>
            <!-- 联系电话 数值型（20） 文本 否 <新增>操作带出-根据委托方关联【客商信息】-联系电话可修改 -->
            <a-form-item name="contactPhone"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('contactPhone')"
                    @click="handleLabelClick('contactPhone')"
                  >
                    联系电话
                  </span>
                </template>
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.contactPhone"  allow-clear/>
            </a-form-item>

            <!-- 发送报关 字符型（10） 下拉框 是 0是1否 系统带出，不允许修改 数据新增时为1否，操作<发送报关>成功时，置为0是 -->
            <a-form-item name="sendCustoms"   class="grid-item"  :colon="false">
                <template #label>
                  <span
                    class="form-label"
                    :class="getLabelClass('sendCustoms')"
                    @click="handleLabelClick('sendCustoms')"
                  >
                    发送报关
                  </span>
                </template>
                <cs-select
                  :options="productClassify.sendEntryStatus"
                  :combine-display="true"
                  option-filter-prop="label"
                  option-label-prop="key"
                  allow-clear
                  show-search
                  :disabled="true"
                  v-model:value="formData.sendCustoms"
                  id="sendCustoms"
                />
            </a-form-item>
            <!-- 确认时间 日期型（18） 日期控件 否 点击"确认"功能按钮，且成功提交的时间：yyyy-mm-dd hh:mm:ss 不允许修改，置灰 -->
            <a-form-item name="confirmTime"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('confirmTime')"
                  @click="handleLabelClick('confirmTime')"
                >
                  确认时间
                </span>
              </template>
              <a-date-picker
                v-model:value="formData.confirmTime"
                id="confirmTime"
                size="small"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :locale="locale"
                style="width: 100%"
                placeholder=""
                :disabled="true"
                />
            </a-form-item>

            <!-- 单据状态 -->
            <a-form-item name="dataState"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('dataState')"
                  @click="handleLabelClick('dataState')"
                >
                  单据状态
                </span>
              </template>
              <cs-select
                :options="productClassify.orderStatus"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                v-model:value="formData.dataState"
                id="dataState"
                :disabled="true"
              />
            </a-form-item>
            <!-- 制单人 -->
            <a-form-item name="createBy"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('createBy')"
                  @click="handleLabelClick('createBy')"
                >
                  制单人
                </span>
              </template>
              <a-input :disabled="true"  size="small" v-model:value="formData.createBy"  allow-clear />
            </a-form-item>
            <!-- 制单时间 -->
            <a-form-item name="createTime"   class="grid-item"  :colon="false">
              <template #label>
                <span
                  class="form-label"
                  :class="getLabelClass('createTime')"
                  @click="handleLabelClick('createTime')"
                >
                  制单时间
                </span>
              </template>
              <a-date-picker
                v-model:value="formData.createTime"
                id="createTime"
                size="small"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :locale="locale"
                style="width: 100%"
                placeholder=""
                :disabled="true"
              />
            </a-form-item>
<!--            &lt;!&ndash; 准运证编号 字符型（100） 文本 否 用户录入 &ndash;&gt;-->
<!--            <a-form-item name="transportPermitNo"   :label="'准运证编号'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.transportPermitNo" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 准运证申办日期 日期型（10） 日期控件 否 用户录入 &ndash;&gt;-->
<!--            <a-form-item name="transportPermitApplyDate"   :label="'准运证申办日期'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.transportPermitApplyDate" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 到货确认日期 日期型（10） 日期控件 否 用户录入 &ndash;&gt;-->
<!--            <a-form-item name="arrivalConfirmDate"   :label="'到货确认日期'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.arrivalConfirmDate" />-->
<!--            </a-form-item>-->

          <!-- 备注 字符型（200） 文本 否 用户录入 -->
          <a-form-item name="remark"   class="grid-item merge-3"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('remark')"
                @click="handleLabelClick('remark')"
              >
                备注
              </span>
            </template>
            <a-textarea
              :disabled="showDisable"
              size="small"
              v-model:value="formData.remark"
              :auto-size="{ minRows: 2, maxRows: 3 }"
              allow-clear
            />
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small"
                      type="primary"
                      :disabled="showDisable"
                      @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !==  editStatus.SHOW " :loading="saveLoading">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            <!-- 审核通过 -->
            <a-button size="small" :loading="auditLoading" @click="handleAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <!-- 审核退回 -->
            <a-button size="small" :loading="invalidLoading" @click="handleInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <!-- 确认 -->
            <a-button size="small" type="ghost" @click="handleConfirm" class="cs-margin-right"
                      :loading="confirmOrderLoading"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check" style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>
          </div>
        </a-form>


      </div>
    </a-card>



    <!-- 第9条线-非国营贸易出口辅料-出货信息表体 -->
    <a-card size="small" title="出货信息表体" class="cs-card-form">
      <biz-export-goods-list-list :parentId="props.editConfig.editData.id"
                                  :is-all-confirmed="props.isAllConfirmed"
                                  :is-edit="!showDisable"
                                  :show-disable="props.editConfig.editStatus === editStatus.SHOW"
      />
    </a-card>

  </section>
</template>

<script setup>
    import {editStatus, productClassify} from '@/view/common/constant'
    import {message, Modal} from "ant-design-vue";
    import {createVNode, onMounted, reactive, ref, watch} from "vue";
    import CsSelect from "@/components/select/CsSelect.vue";
    import {usePCode} from "@/view/common/usePCode";
    import ycCsApi from "@/api/ycCsApi";
    import useEventBus from "@/view/common/eventBus";
    import BizExportGoodsListList from "@/view/audit/export/goods_head/list/BizExportGoodsListList.vue";
    import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
    import {useFieldMarking} from "@/utils/useFieldMarking";
    const { getPCode } = usePCode()
    const { emitEvent } = useEventBus()

    // 使用字段标记功能
    const {
      fieldMarkings,
      handleLabelClick,
      getLabelClass,
      setFieldMarkings,
      getFieldMarkings,
      clearFieldMarkings,
      getMarkedFieldsCount,
      saveCurrentMarkings,
      getBySidAndFormType
    } = useFieldMarking()

    // 字段名称映射（英文字段名 -> 中文显示名）
    const fieldNameMap = {
      exportNo: '出货单号',
      contractNo: '合同号',
      customer: '客户',
      customerAddress: '客户地址',
      supplier: '供应商',
      tradeCountry: '贸易国别',
      manageUnit: '经营单位',
      paymentType: '付款方式',
      currency: '币种',
      transportType: '运输方式',
      priceTerms: '价格条款',
      priceTermsPort: '价格条款对应港口',
      deliveryUnit: '发货单位',
      packageType: '包装种类',
      packageNum: '包装数量',
      deliveryUnitLocation: '发货单位所在地',
      shipper: '装运人SHIPPER',
      consignee: '收货人CONSIGNEE',
      notifyParty: '通知人NOTIFY PARTY',
      grossWeight: '总毛重',
      netWeight: '总净重',
      tareWeight: '总皮重',
      mark: '唛头',
      portOfShipment: '装运港',
      portOfDestination: '目的地/港',
      shipmentDate: '装运期限',
      insuranceType: '险别',
      insuranceCurrency: '保费币种',
      insuranceAddRate: '投保加成%',
      insuranceRate: '保费费率(%)',
      insuranceFee: '保险费',
      insurer: '投保人',
      freight: '运费',
      freightCurrency: '运费币种',
      warehouseAddress: '仓储地址',
      contactPerson: '联系人',
      contactPhone: '联系电话',
      remark: '备注',
      sendCustoms: '发送报关',
      confirmTime: '确认时间',
      dataState: '单据状态',
      createBy: '制单人',
      createTime: '制单时间'
    }


    const props = defineProps({
      editConfig: {
        type: Object,
        default: () => {
        }
      },
      isAllConfirmed:{
        type: Boolean,
        default: false
      }
    });

    // 定义子组件 emit事件，用于子组件向父组件传递数据
    const emit = defineEmits(['onBack']);

    const onBack = (val) => {
      emit('onBack', val);
    };

    // 是否禁用
    const showDisable = ref(false)

    // 表单数据
    const formData = reactive({
        // 主键id
        id:'',
        // 业务类型
        businessType:'',
        // 数据状态
        dataState:'',
        // 版本号
        versionNo:'',
        // 企业10位编码
        tradeCode:'',
        // 组织机构代码
        sysOrgCode:'',
        // 父级id
        parentId:'',
        // 创建人
        createBy:'',
        // 创建时间
        createTime:'',
        createTimeTo:'',
        createTimeForm:'',
        // 更新人
        updateBy:'',
        // 更新时间
        updateTime:'',
        updateTimeTo:'',
        updateTimeForm:'',
        // 插入用户名
        insertUserName:'',
        // 更新用户名
        updateUserName:'',
        // 扩展字段1
        extend1:'',
        // 扩展字段2
        extend2:'',
        // 扩展字段3
        extend3:'',
        // 扩展字段4
        extend4:'',
        // 扩展字段5
        extend5:'',
        // 扩展字段6
        extend6:'',
        // 扩展字段7
        extend7:'',
        // 扩展字段8
        extend8:'',
        // 扩展字段9
        extend9:'',
        // 扩展字段10
        extend10:'',
        // 出货单号
        exportNo:'',
        // 合同号
        contractNo:'',
        // 客户
        customer:'',
        // 客户地址
        customerAddress:'',
        // 供应商
        supplier:'',
        // 贸易国别
        tradeCountry:'',
        // 经营单位
        manageUnit:'',
        // 付款方式
        paymentType:'',
        // 币种
        currency:'',
        // 运输方式
        transportType:'',
        // 价格条款
        priceTerms:'',
        // 价格条款对应港口
        priceTermsPort:'',
        // 发货单位
        deliveryUnit:'',
        // 包装种类
        packageType:'',
        // 包装数量
        packageNum:'',
        // 发货单位所在地
        deliveryUnitLocation:'',
        // 装运人shipper
        shipper:'',
        // 收货人consignee
        consignee:'',
        // 通知人notify party
        notifyParty:'',
        // 总毛重
        grossWeight:'',
        // 总净重
        netWeight:'',
        // 总皮重
        tareWeight:'',
        // 唛头
        mark:'',
        // 装运港
        portOfShipment:'',
        // 目的地/港
        portOfDestination:'',
        // 装运期限
        shipmentDate:'',
        // 险别
        insuranceType:'',
        // 保费币种
        insuranceCurrency:'',
        // 投保加成%
        insuranceAddRate:'',
        // 保费费率(%)
        insuranceRate:'',
        // 保险费
        insuranceFee:'',
        // 投保人
        insurer:'',
        // 运费
        freight:'',
        // 运费币种
        freightCurrency:'',
        // 仓储地址
        warehouseAddress:'',
        // 联系人
        contactPerson:'',
        // 联系电话
        contactPhone:'',
        // 备注
        remark:'',
        // 发送报关
        sendCustoms:'',
        // 确认时间
        confirmTime:'',
        confirmTimeTo:'',
        confirmTimeForm:'',
        // 准运证编号，用户录入
        transportPermitNo:'',
        // 准运证申办日期，用户录入
        transportPermitApplyDate:'',
        transportPermitApplyDateTo:'',
        transportPermitApplyDateForm:'',
        // 到货确认日期，用户录入
        arrivalConfirmDate:'',
        arrivalConfirmDateTo:'',
        arrivalConfirmDateForm:''
    })
    /**
     * 校验出货单号是否唯一
     * @param rule 校验规则
     * @param value 校验值
     * @param callback 回调函数
     */
    const checkExportNo = async (rule, value) => {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await window.majesty.httpUtil.postAction(
            ycCsApi.bizExportGoodsHead.checkExportNo,
            formData
          );

          if (res.code === 200) {
            if (res && res.data && res.data === 999) {
              reject(res.message);
            } else if (res.data > 0) {
              reject('出货单号已经存在！');
            } else {
              resolve(); // 校验通过
            }
          } else {
            resolve(); // 或根据业务逻辑决定是否 reject
          }
        } catch (error) {
          console.error("校验请求失败:", error);
          reject("网络异常，请重试");
        }
      });
    };

    // 校验规则
    const rules = {
        id:[
        ],
        businessType:[
            {max: 120, message: '业务类型长度不能超过 120位字节', trigger: 'blur'}
        ],
        dataState:[
            {required: true, message: '单据状态不能为空', trigger: 'blur'},
            {max: 20, message: '单据状态长度不能超过 20位字节', trigger: 'blur'}
        ],
        versionNo:[
            {max: 20, message: '版本号长度不能超过 20位字节', trigger: 'blur'}
        ],
        tradeCode:[
            {max: 20, message: '企业10位编码长度不能超过 20位字节', trigger: 'blur'}
        ],
        sysOrgCode:[
            {max: 20, message: '组织机构代码长度不能超过 20位字节', trigger: 'blur'}
        ],
        parentId:[
        ],
        createBy:[
          {required: true, message: '制单人不能为空', trigger: 'blur'},
          {max: 100, message: '创建人长度不能超过 100位字节', trigger: 'blur'}
        ],
        createTime:[
          {required: true, message: '制单时间不能为空', trigger: 'blur'},
        ],
        updateBy:[
            {max: 100, message: '更新人长度不能超过 100位字节', trigger: 'blur'}
        ],
        updateTime:[
        ],
        insertUserName:[
            {max: 100, message: '插入用户名长度不能超过 100位字节', trigger: 'blur'}
        ],
        updateUserName:[
            {max: 100, message: '更新用户名长度不能超过 100位字节', trigger: 'blur'}
        ],
        extend1:[
            {max: 400, message: '扩展字段1长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend2:[
            {max: 400, message: '扩展字段2长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend3:[
            {max: 400, message: '扩展字段3长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend4:[
            {max: 400, message: '扩展字段4长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend5:[
            {max: 400, message: '扩展字段5长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend6:[
            {max: 400, message: '扩展字段6长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend7:[
            {max: 400, message: '扩展字段7长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend8:[
            {max: 400, message: '扩展字段8长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend9:[
            {max: 400, message: '扩展字段9长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend10:[
            {max: 400, message: '扩展字段10长度不能超过 400位字节', trigger: 'blur'}
        ],
        exportNo:[
            {required: true, message: '出货单号不能为空', trigger: 'blur'},
            {max: 60, message: '出货单号长度不能超过 60位字节', trigger: 'blur'},
            {validator: checkExportNo, trigger: 'blur'}
        ],
        contractNo:[
            {required: true, message: '合同号不能为空', trigger: 'blur'},
            {max: 60, message: '合同号长度不能超过 60位字节', trigger: 'blur'}
        ],
        customer:[
            {required: true, message: '客户不能为空', trigger: 'blur'},
            {max: 200, message: '客户长度不能超过 200位字节', trigger: 'blur'}
        ],
        customerAddress:[
            {required: true, message: '客户地址不能为空', trigger: 'blur'},
            {max: 60, message: '客户地址长度不能超过 60位字节', trigger: 'blur'}
        ],
        supplier:[
            {required: true, message: '供应商不能为空', trigger: 'blur'},
            {max: 200, message: '供应商长度不能超过 200位字节', trigger: 'blur'}
        ],
        tradeCountry:[
            {required: true, message: '贸易国别不能为空', trigger: 'blur'},
            {max: 60, message: '贸易国别长度不能超过 60位字节', trigger: 'blur'}
        ],
        manageUnit:[
            {required: true, message: '经营单位不能为空', trigger: 'blur'},
            {max: 200, message: '经营单位长度不能超过 200位字节', trigger: 'blur'}
        ],
        paymentType:[
            {max: 50, message: '付款方式长度不能超过 50位字节', trigger: 'blur'}
        ],
        currency:[
            {required: true, message: '币种不能为空', trigger: 'blur'},
            {max: 10, message: '币种长度不能超过 10位字节', trigger: 'blur'}
        ],
        transportType:[
            {max: 10, message: '运输方式长度不能超过 10位字节', trigger: 'blur'}
        ],
        priceTerms:[
            {max: 20, message: '价格条款长度不能超过 20位字节', trigger: 'blur'}
        ],
        priceTermsPort:[
            {max: 50, message: '价格条款对应港口长度不能超过 50位字节', trigger: 'blur'}
        ],
        deliveryUnit:[
            {required: true, message: '发货单位不能为空', trigger: 'blur'},
            {max: 60, message: '发货单位长度不能超过 60位字节', trigger: 'blur'}
        ],
        packageType:[
            {max: 30, message: '包装种类长度不能超过 30位字节', trigger: 'blur'}
        ],
        packageNum:[

        ],
        deliveryUnitLocation:[
            {max: 50, message: '发货单位所在地长度不能超过 50位字节', trigger: 'blur'}
        ],
        shipper:[
            {max: 300, message: '装运人shipper长度不能超过 300位字节', trigger: 'blur'}
        ],
        consignee:[
            {max: 300, message: '收货人consignee长度不能超过 300位字节', trigger: 'blur'}
        ],
        notifyParty:[
            {max: 300, message: '通知人notify party长度不能超过 300位字节', trigger: 'blur'}
        ],
        grossWeight:[

        ],
        netWeight:[

        ],
        tareWeight:[

        ],
        mark:[
            {max: 300, message: '唛头长度不能超过 300位字节', trigger: 'blur'}
        ],
        portOfShipment:[
            {max: 50, message: '装运港长度不能超过 50位字节', trigger: 'blur'}
        ],
        portOfDestination:[
            {max: 50, message: '目的地/港长度不能超过 50位字节', trigger: 'blur'}
        ],
        shipmentDate:[
        ],
        insuranceType:[
            {max: 100, message: '险别长度不能超过 100位字节', trigger: 'blur'}
        ],
        insuranceCurrency:[
            {max: 10, message: '保费币种长度不能超过 10位字节', trigger: 'blur'}
        ],
        insuranceAddRate:[

        ],
        insuranceRate:[

        ],
        insuranceFee:[

        ],
        insurer:[
            {required: true, message: '投保人不能为空', trigger: 'blur'},
            {max: 200, message: '投保人长度不能超过 200位字节', trigger: 'blur'}
        ],
        freight:[

        ],
        freightCurrency:[
            {max: 10, message: '运费币种长度不能超过 10位字节', trigger: 'blur'}
        ],
        warehouseAddress:[
            {max: 300, message: '仓储地址长度不能超过 300位字节', trigger: 'blur'}
        ],
        contactPerson:[
            {max: 20, message: '联系人长度不能超过 20位字节', trigger: 'blur'}
        ],
        contactPhone:[
            {max: 20, message: '联系电话长度不能超过 20位字节', trigger: 'blur'}
        ],
        remark:[
            {max: 200, message: '备注长度不能超过 200位字节', trigger: 'blur'}
        ],
        sendCustoms:[
            {required: true, message: '发送报关不能为空', trigger: 'blur'},
            {max: 10, message: '发送报关长度不能超过 10位字节', trigger: 'blur'}
        ],
        confirmTime:[
        ],
        transportPermitNo:[
            {max: 100, message: '准运证编号，用户录入长度不能超过 100位字节', trigger: 'blur'}
        ],
        transportPermitApplyDate:[
        ],
        arrivalConfirmDate:[
        ]
    }



    const pCode = ref('')

    const customerList = ref([]);
    const currList = ref([]);
    const priceTermList = ref([]);
    const packageList = ref([]);
    const cityList = ref([]);
    const insuranceTypeList = ref([]);
    const portList = ref([]);
    const fromDataLoading = ref(false);
    const getCommonKeyValueList = async () => {
      fromDataLoading.value = true;
      try {
        const res = await window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.getCommonKeyValueList,{});
        if (res.code === 200) {
          customerList.value = res.data.customerList;
          currList.value = res.data.currList;
          priceTermList.value = res.data.priceTermList;
          packageList.value = res.data.packageList;
          cityList.value = res.data.cityList;
          insuranceTypeList.value = res.data.insuranceTypeList;
          portList.value = res.data.portList;
        }else {
          message.error(res.message);
        }
      }catch(err) {
        console.log(err);
      }finally {
        fromDataLoading.value = false;
      }

    }

    /* 确认 */
    const confirmOrderLoading = ref(false);
    const handleConfirm = ()=> {
      if (formData.dataState === '2') {
        message.warning('该数据已作废，不允许进行确认操作！')
        return
      }

      if (formData.dataState === '1') {
        message.warning('该数据已经确认，无需重复操作！')
        return
      }

      // 弹出确认框
      Modal.confirm({
        title: '提醒?',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        content: '是否确认所选项？',
        onOk() {
          confirmOrderLoading.value = true
          window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsHead.confirm}`,formData).then(res=>{
            if (res.code === 200) {
              message.success("确认成功！")
              Object.assign(formData, res.data)
              onBack({
                editData: res.data,
                editStatus: editStatus.EDIT
              })
            }else {
              message.error(res.message)
            }
          }).finally(() => {
            confirmOrderLoading.value = false
          })
          // confirmIOrderHead(formData.value).then(res => {
          //   if (res.code === 200) {
          //     message.success("确认成功！")
          //     Object.assign(formData.value, res.data)
          //     onBack({
          //       editData: res.data,
          //       showBodyPurchaseHead: true,
          //       showBody: true,
          //       editStatus: editStatus.EDIT
          //     })
          //   }else {
          //     message.error(res.message)
          //   }
          // }).finally(() => {
          //   confirmOrderLoading.value = false
          // })
        },
        onCancel() {
          confirmOrderLoading.value = false
        },
      });
    }

    const auditLoading = ref(false)
    const invalidLoading = ref(false)

    /* 审核通过事件 */
    const handleAudit = () => {
      // 校验是否有红色标识错误的数据
      const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
      if (redFieldNames.length > 0) {
        message.error('存在待确认数据，不允许审批通过')
        return
      }

      // 审核意见输入框
      const auditOpinion = ref('同意审批')

      // 弹出审核确认框
      Modal.confirm({
        title: '审核通过',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        content: createVNode('div', {}, [
          createVNode('div', { style: 'margin-top: 10px;' }, [
            createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
            createVNode('textarea', {
              value: auditOpinion.value,
              onInput: (e) => { auditOpinion.value = e.target.value },
              style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
              placeholder: '请输入审核意见'
            })
          ])
        ]),
        onOk() {
          auditLoading.value = true
          const params = {
            ids: [formData.id],
            apprMessage: auditOpinion.value || '同意审批',
            businessType: '9',
            billType: 'order',
          }

          // 调用audit接口
          window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.audit, params)
            .then(res => {
              if (res.code === 200) {
                message.success("审核通过成功！")
                // 返回列表页面
                onBack(true)
              } else {
                message.error(res.message || '审核失败')
              }
            })
            .catch(error => {
              console.error('审核失败:', error)
              message.error(error.message || error  || '审核失败，请重试')
            })
            .finally(() => {
              auditLoading.value = false
            })
        },
        onCancel() {
          // 取消操作
        },
      });
    }

    /* 审核退回事件 */
    const handleInvalid = () => {
      // 直接读取当前页面的标记信息
      let markedFields = ''
      const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
      if (redFieldNames.length > 0) {
        // 将英文字段名转换为中文显示
        const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
        markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
      }

      // 审核意见输入框
      const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')

      // 弹出审核退回确认框
      Modal.confirm({
        title: '审核退回',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        content: createVNode('div', {}, [
          createVNode('div', { style: 'margin-top: 10px;' }, [
            createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
            createVNode('textarea', {
              value: auditOpinion.value,
              onInput: (e) => { auditOpinion.value = e.target.value },
              style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
              placeholder: '请输入审核意见'
            })
          ])
        ]),
        onOk() {
          invalidLoading.value = true
          const params = {
            ids: [formData.id],
            apprMessage: auditOpinion.value || '审批退回',
            businessType: '9',
            billType: 'order',
          }

          // 调用reject接口进行退回
          window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.reject, params)
            .then(res => {
              console.log('调用内审退回接口',res)
              if (res.code === 200) {
                // 审核退回成功后，调用标记保存接口
                return saveCurrentMarkings(formData.id, 'default', fieldMarkings.value)
              } else {
                throw new Error(res.message || '审核退回失败')
              }
            })
            .then(res => {
              message.success("审核退回成功！")
              // 返回列表页面
              onBack(true)
            })
            .catch(error => {
              console.error('审核退回失败:', error)
              message.error(error.message || error || '审核退回失败，请重试')
            })
            .finally(() => {
              invalidLoading.value = false
            })
        },
        onCancel() {
          // 取消操作
        },
      });
    }

    // 初始化操作
    onMounted(() => {
      // 获取当前界面标记的id
      if (props.editConfig && props.editConfig.editData && props.editConfig.editData.id) {
        getBySidAndFormType(props.editConfig.editData.id, 'default')
      }

      getCommonKeyValueList();
      getPCode().then(res=>{
        console.log('res',res)
        pCode.value = res;
      })
      // 初始化数据
      if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
        Object.assign(formData, props.editConfig.editData);
        showDisable.value = false
      }
      if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
        Object.assign(formData, props.editConfig.editData);
        showDisable.value = true
      }

      // 设置经营单位默认值
      if (!formData.manageUnit) {
        formData.manageUnit = '中国烟草上海进出口有限责任公司';
      }

      // 设置运输方式默认值（0海运）
      if (!formData.transportType) {
        formData.transportType = '0';
      }

      // 设置保费币种默认值（USD）
      if (!formData.insuranceCurrency) {
        formData.insuranceCurrency = 'USD';
      }

      // 设置运费币种默认值（USD）
      if (!formData.freightCurrency) {
        formData.freightCurrency = 'USD';
      }

      // 设置投保加成默认值（1%）
      if (!formData.insuranceAddRate) {
        formData.insuranceAddRate = 1;
      }

      // 设置保费费率默认值（0.0267%）
      if (!formData.insuranceRate) {
        formData.insuranceRate = 0.0267;
      }

      // 设置发送报关默认值（1否）
      if (!formData.sendCustoms) {
        formData.sendCustoms = '1';
      }

      // 设置单据状态默认值（0编制）
      if (!formData.dataState) {
        formData.dataState = '0';
      }

      // 设置制单时间默认值（当前时间）
      if (!formData.createTime) {
        const now = new Date();
        formData.createTime = now.toISOString().slice(0, 19).replace('T', ' ');
      }

      // 设置制单人默认值（当前用户）
      if (!formData.createBy) {
        // 从系统获取当前用户信息
        const currentUser = window.majesty?.userInfo?.username || 'system';
        formData.createBy = currentUser;
      }
    });



    // vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
    const formRef = ref(null);
    const saveLoading = ref(false);
    // 保存
    const handlerSave = () => {
      formRef.value
        .validate()
        .then(() => {
          saveLoading.value = true;
          // 插入数据
          window.majesty.httpUtil.putAction(`${ycCsApi.bizExportGoodsHead.update}/${formData.id}`,formData).then(res=>{
            if (res.code === 200) {
              message.success('修改成功')
              // 重新赋值当前数据
              Object.assign(formData, res.data);
              // 重新赋值下拉框数据
              emitEvent('refresh-export-goods-head-search')
            }else {
              message.error(res.message);
            }
          }).finally(() => {
            saveLoading.value = false;
          })


          // 更新数据
          // window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsHead.update}/${formData.id}`, formData).then((res)=>{
          //   if (res.code === 200){
          //     message.success('修改成功!')
          //     onBack(true)
          //   }
          // }).catch(error => {
          //   console.log('validate failed', error);
          // })
        })
        .catch(error => {
          console.log('validate failed', error);
        })
    };


    /* 监控 grossWeight（总毛重） 、 netWeight（总净重） */
    watch(()=>[formData.grossWeight,formData.netWeight],([grossWeight,netWeight])=>{
      if (grossWeight && netWeight) {
        // 总皮重 = 总毛重 - 总净重
        formData.tareWeight = grossWeight - netWeight;
      }
    })


    /* 监控formData中，dataState的变化 */
    watch(()=>formData.dataState, (newVal,oldVal)=>{
      if (newVal === '1' || newVal === '2') {
        showDisable.value = true
      }else {
        showDisable.value = false
      }
    })


</script>

<style lang="less" scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.form-label:hover {
  opacity: 0.8;
}

.label-green {
  background-color: #52c41a;
  color: white;
}

.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>



