<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search" ref="searchArea">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  查询
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <!-- 查询按钮组件 -->
          <div ref="area_search">
            <div v-show="showSearch">
              <biz-smoke-machine-incoming-goods-head-search ref="headSearch"/>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:smokeMachine-incoming-head:audit']">
          <a-button size="small" :loading="auditLoading" @click="handlerAudit">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue"/>
            </template>
            审核通过
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:smokeMachine-incoming-head:refuse']">
          <a-button size="small" :loading="invalidLoading" @click="handlerInvalid">
            <template #icon>
              <GlobalIcon type="close-square" style="color:red"/>
            </template>
            审核退回
          </a-button>
        </div>
        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-suppler_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0" ref="containerRef">
        <s-table
          ref="tableRef"
          class="cs-action-item remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight, x: 400 }"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns.value"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"

          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据"/>
          </template>
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <div>
                  <a-button
                    size="small"
                    type="link"
                    @click="handleViewByRow(record)"
                    :style="operationEdit('view')"
                  >
                    <template #icon>
                      <GlobalIcon type="search" style="color:#1677ff"/>
                    </template>
                  </a-button>
                </div>
              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0" ref="paginationRef">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>
    <!-- 详情查看 -->
    <div v-if="!show">
      <smoke-machine-tab :edit-config="editConfig" @onEditBack="handlerOnBack"/>
    </div>
  </section>
</template>

<script setup>
import {useCommon} from '@/view/common/useCommon'
import {message, Modal, Tag} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus, productClassify} from "@/view/common/constant";
import BizSmokeMachineIncomingGoodsHeadSearch from "@/view/audit/smoke_machine/purchase order/head/BizSmokeMachineIncomingGoodsHeadSearch.vue";
import {createVNode, onMounted, ref, watch, reactive, h} from "vue";
import {deepClone} from "@/view/utils/common";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {GlobalIcon} from "@/components/icon";
import SmokeMachineTab from "@/view/audit/smoke_machine/purchase order/SmokeMachineTab.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import { useFieldMarking } from '@/utils/useFieldMarking';

const {inputFormatter, inputParser, formatNumber, formatSpecifiedNumber, cmbShowRender} = useColumnsRender()

const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  getList,
  ajaxUrl,
  handlerRefresh,
  gridData,
  onSelectChange
} = useCommon()

defineOptions({
  name: 'BizSmokeMachineIncomingGoodsHeadList',
});

// 表格字段
const totalColumns = ref([
  {
    width: 150,
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
    fixed: 'left',
  },
  {
    title: '审核状态',
    width: 200,
    align: 'center',
    dataIndex: 'approvalStatus',
    key: 'approvalStatus',
    customRender: ({ text }) => {
      const colors = ['black','green', 'blue','green','red',];
      return h(Tag,{
        color: colors[parseInt(text)],
        size: 'small',
      }, cmbShowRender(text,productClassify.approval_status))
    },
  },
  {
    title: '合同号',
    width: 200,
    align: 'center',
    dataIndex: 'contractNo',
    key: 'contractNo',
  },
  {
    title: '进货单号',
    width: 200,
    align: 'center',
    dataIndex: 'purchaseNo',
    key: 'purchaseNo',
  },
  {
    title: '客户',
    width: 200,
    align: 'center',
    dataIndex: 'customer',
    key: 'customer',
    customRender: ({text}) => {
      return cmbShowRender(text, customerList.value)
    }
  },
  {
    title: '合同金额',
    width: 200,
    align: 'center',
    dataIndex: 'contractAmount',
    key: 'contractAmount',
  },
  {
    title: '报关单号',
    width: 200,
    align: 'center',
    dataIndex: 'entryNo',
    key: 'entryNo',
  },
  {
    title: '报关日期',
    width: 200,
    align: 'center',
    dataIndex: 'entryDate',
    key: 'entryDate',
  },
  {
    title: '目的地/港',
    width: 200,
    align: 'center',
    dataIndex: 'destination',
    key: 'destination',
    customRender: ({text}) => {
      return cmbShowRender(text, portList.value)
    }
  },
  {
    title: '制单人',
    width: 200,
    align: 'center',
    dataIndex: 'createBy',
    key: 'createBy',
  },
  {
    title: '制单日期',
    width: 200,
    align: 'center',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '单据状态',
    width: 200,
    align: 'center',
    dataIndex: 'dataState',
    key: 'dataState',
    customRender: ({ text }) => {
      const colors = ['green', 'blue', 'red','black'];
      return h(Tag,{
        color: colors[parseInt(text)],
        size: 'small',
      }, cmbShowRender(text,productClassify.orderIncomingStatus))
    },
  },
  {
    title: '确认时间',
    width: 200,
    align: 'center',
    dataIndex: 'confirmTime',
    key: 'confirmTime',
  }
])

// 自定义显示列
const showColumns = ref([])
const tableKey = ref('')
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()

const initCustomColumn = () => {
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  originalColumns.value = dealColumns;
}

const customColumnChange = (settingColumns) => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
}

watch(totalColumns.value, (newValue, oldValue) => {
  if (!window.$vueApp) {
    showColumns.value = [...totalColumns.value];
  } else {
    if (newValue.length === 0) {
      showColumns.value = [...totalColumns.value];
    } else {
      showColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
      totalColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
    }
  }
}, {immediate: true, deep: true})

const tableHeight = ref(getTableScroll(73, ''))

// 自定义行属性
// const customRow = (record) => {
//   return {
//     onDblclick: () => {
//       // 选中当前行
//       // gridData.selectedRowKeys = [record.id];
//       // handleRowDblclick(record);
//     },
//     style: { cursor: 'pointer' }
//   };
// };
const handleRowDblclick = (record) => {
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = record
  show.value = !show.value;
};

const { getBySidAndFormType } = useFieldMarking();

const auditLoading = ref(false)
const invalidLoading = ref(false)

// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  contractNo: '合同号',
  purchaseNo: '进货单号',
  customer: '客户',
  contractAmount: '合同金额',
  entryNo: '报关单号',
  entryDate: '报关日期',
  destination: '目的地/港',
  createBy: '制单人',
  createTime: '制单日期',
  dataState: '单据状态',
  confirmTime: '确认时间',
}

// 审核通过
const handlerAudit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择数据')
    return
  }
  const auditOpinion = ref('同意审批')
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: gridData.selectedRowKeys,
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'smokeMachineIncomingGoodsHead',
      }
      window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.audit, params)
        .then(res => {
          if (res.code === 200) {
            message.success("审核通过成功！")
            getList()
            gridData.selectedRowKeys = []
            gridData.selectedData = []
          } else {
            message.error(res.message || '审核失败')
          }
        })
        .catch(error => {
          console.error('审核失败:', error)
          message.error('审核失败，请重试')
        })
        .finally(() => {
          auditLoading.value = false
        })
    },
    onCancel() {},
  });
}

// 审核退回
const handlerInvalid = async () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  let markedFields = ''
  try {
    const fieldMarkings = await getBySidAndFormType(gridData.selectedRowKeys[0], 'default')
    if (fieldMarkings && typeof fieldMarkings === 'object') {
      const redFieldNames = Object.keys(fieldMarkings).filter(key => fieldMarkings[key] === 'red')
      if (redFieldNames.length > 0) {
        const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
        markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
      }
    }
  } catch (error) {
    console.warn('获取标红字段失败:', error)
  }
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: gridData.selectedRowKeys,
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'smokeMachineIncomingGoodsHead',
      }
      window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.reject, params)
        .then(res => {
          if (res.code === 200) {
            message.success("审核退回成功！")
            getList()
            gridData.selectedRowKeys = []
            gridData.selectedData = []
          } else {
            message.error(res.message || '审核退回失败')
          }
        })
        .catch(error => {
          console.error('审核退回失败:', error)
          message.error('审核退回失败，请重试')
        })
        .finally(() => {
          invalidLoading.value = false
        })
    },
    onCancel() {},
  });
}

// 供应商列表
const customerList = ref([])
const getCustomerList = async () => {
  const res = await window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.getCustomerList, {})
  if (res.code === 200) {
    customerList.value = res.data
  }
  // unitList.value = res.data
}
// 港口列表
const portList = ref([])
const getPortList = async () => {
  const res = await window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.getPortList, {})
  if (res.code === 200) {
    portList.value = res.data
  }
}




// 返回
const handlerOnBack = (flag)=>{
  show.value = !show.value;
  // 返回清空选择数据
  gridData.selectedData = [];
  gridData.selectedRowKeys = [];
  editConfig.editData = {}
  if (flag){
    getList()
  }
}

onMounted(fn => {
  ajaxUrl.selectAllPage = ycCsApi.bizSmokeMachineInComingHead.list
  // ajaxUrl.exportUrl = ycCsApi.bizSmokeMachineInComingHead.export
  getList()
  initCustomColumn()
  getCustomerList()
  getPortList()
})
</script>

<style lang="less" scoped>

</style>
