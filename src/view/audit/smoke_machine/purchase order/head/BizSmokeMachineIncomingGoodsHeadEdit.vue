<template>
  <section>
    <a-card size="small" title="进货单表头" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <!-- 默认3-国营贸易进口烟机设备，置灰，不允许修改 -->
          <a-form-item name="businessType"    class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('businessType')"
                @click="handleLabelClick('businessType')"
              >
                业务类型
              </span>
            </template>
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!-- 合同号 <新增>操作带出，不允许修改、指【外商合同】表头合同号-->
          <div style="display: flex; align-items: center;justify-content: space-between;">
            <a-form-item name="contractNo" class="grid-item merge-3"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('contractNo')"
                @click="handleLabelClick('contractNo')"
              >
                合同号
              </span>
            </template>
              <a-input :disabled="true"  size="small" v-model:value="formData.contractNo" allow-clear />
            </a-form-item>
            <!--  只有非新增，才能进行点击 -->
            <!-- <a-button size="small" type="primary" @click="handlerSelectContract" :disabled="!isNullOrEmpty(formData.id)">选择</a-button> -->
          </div>


          <!-- 进货单号 系统带出，按外商合同号+2位流水号，允许修改，唯一性校验、注：2位流水号指该外商合同号下生成的进货单流水号 -->
          <a-form-item name="purchaseNo" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('purchaseNo')"
                @click="handleLabelClick('purchaseNo')"
              >
                进货单号
              </span>
            </template>
            <a-input :disabled="showDisable || (isEdit  === false)"  size="small" v-model:value="formData.purchaseNo" allow-clear />
          </a-form-item>

          <!-- 客户 -->
          <a-form-item name="customer" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('customer')"
                @click="handleLabelClick('customer')"
              >
                客户
              </span>
            </template>
            <cs-select :disabled="showDisable || (isEdit  === false)"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.customer" id="customer">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>



          <!-- 供应商 <新增>操作带出，不允许修改 -->
          <a-form-item name="supplier" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('supplier')"
                @click="handleLabelClick('supplier')"
              >
                供应商
              </span>
            </template>
            <cs-select :disabled="showDisable || (isEdit  === false)"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.supplier" id="supplier">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>






          <!-- 发票号 -->
          <a-form-item name="invoiceNo" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('invoiceNo')"
                @click="handleLabelClick('invoiceNo')"
              >
                发票号
              </span>
            </template>
            <a-input :disabled="showDisable || (isEdit  === false)"  size="small" v-model:value="formData.invoiceNo" allow-clear/>
          </a-form-item>




          <!-- 启运港  允许修改，<新增>操作带出外商合同的装运港 -->
          <a-form-item name="portOfDeparture" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('portOfDeparture')"
                @click="handleLabelClick('portOfDeparture')"
              >
                启运港
              </span>
            </template>
            <cs-select :disabled="showDisable || (isEdit  === false)"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.portOfDeparture" id="portOfDeparture">
              <a-select-option class="cs-select-dropdown" v-for="item in portList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>



          <!-- 目的地/港  允许修改，<新增>操作带出外商合同的目的港-->
          <a-form-item name="destination" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('destination')"
                @click="handleLabelClick('destination')"
              >
                目的地/港
              </span>
            </template>
            <cs-select :disabled="showDisable || (isEdit  === false)"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.destination" id="destination">
              <a-select-option class="cs-select-dropdown" v-for="item in portList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>



          <!-- 购销合同号 -->
          <!--          <a-form-item name="purchaseContractNo"   :label="'购销合同号'" class="grid-item"  :colon="false">-->
          <!--            <a-input :disabled="true"  size="small" v-model:value="formData.purchaseContractNo" />-->
          <!--          </a-form-item>-->









          <!-- 付款方式 <新增>操作带出外商合同付款方式，不允许修改
           系统参数
                0付款交单
                1即期信用证
                2电汇
                3预付款
          -->
          <a-form-item name="paymentMethod" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('paymentMethod')"
                @click="handleLabelClick('paymentMethod')"
              >
                付款方式
              </span>
            </template>
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.paymentMethod" id="paymentMethod">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.paymentMethod"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>


          <!-- 价格条款 -->
          <a-form-item name="priceTerm" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('priceTerm')"
                @click="handleLabelClick('priceTerm')"
              >
                价格条款
              </span>
            </template>
            <cs-select :disabled="showDisable || (isEdit  === false)"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.priceTerm" id="priceTerm">
              <a-select-option class="cs-select-dropdown" v-for="item in priceTermList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>



          <!-- 价格条款对应港口 -->
          <a-form-item name="priceTermPort" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('priceTermPort')"
                @click="handleLabelClick('priceTermPort')"
              >
                价格条款对应港口
              </span>
            </template>
            <cs-select :disabled="showDisable || (isEdit  === false)"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.priceTermPort" id="priceTermPort">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.priceTermPort"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!-- 船名航次 -->
          <a-form-item name="vesselVoyage" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('vesselVoyage')"
                @click="handleLabelClick('vesselVoyage')"
              >
                船名航次
              </span>
            </template>
            <a-input :disabled="showDisable || (isEdit  === false)"  size="small" v-model:value="formData.vesselVoyage" allow-clear />
          </a-form-item>



          <!-- 开航日期 -->
          <a-form-item name="sailingDate" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('sailingDate')"
                @click="handleLabelClick('sailingDate')"
              >
                开航日期
              </span>
            </template>
            <a-date-picker
              allow-clear
              :disabled="showDisable || (isEdit  === false)"
              v-model:value="formData.sailingDate"
              id="releaseDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>



          <!-- 预计抵达日期 -->
          <a-form-item name="expectedArrivalDate" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('expectedArrivalDate')"
                @click="handleLabelClick('expectedArrivalDate')"
              >
                预计抵达日期
              </span>
            </template>
            <a-date-picker
              allow-clear
              :disabled="showDisable || (isEdit  === false)"
              v-model:value="formData.expectedArrivalDate"
              id="releaseDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>




          <!-- 作销日期 -->
          <a-form-item name="salesDate" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('salesDate')"
                @click="handleLabelClick('salesDate')"
              >
                作销日期
              </span>
            </template>
            <a-date-picker
              allow-clear
              :disabled="showDisable || (isEdit  === false)"
              v-model:value="formData.salesDate"
              id="releaseDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>



          <!-- 合同金额  19,4-->
          <a-form-item name="contractAmount" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('contractAmount')"
                @click="handleLabelClick('contractAmount')"
              >
                合同金额
              </span>
            </template>
            <a-input-number :disabled="true"
                            size="small"
                            style="width: 100%"
                            v-model:value="formData.contractAmount"
                            :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                            :parser="value => inputParser(value)"
            />
          </a-form-item>


          <!-- 保险费率%  19,4 -->
          <a-form-item name="insuranceRate" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('insuranceRate')"
                @click="handleLabelClick('insuranceRate')"
              >
                保险费率%
              </span>
            </template>
            <!--
            <a-input-number
              style="width: 100%"
              v-model:value="formData.insuranceRate"
              :disabled="showDisable || (isEdit  === false)"
              size="small"
              :default-value="100"
              :min="0"
              :max="99999999999999999999999999999999999999999"
              :formatter="value => `${value}%`"
              :parser="value => value.replace('%', '')"
            />
            -->
            <a-input-number :disabled="showDisable || (isEdit  === false)"
                            size="small"
                            style="width: 100%"
                            v-model:value="formData.insuranceRate"
                            :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                            :parser="value => inputParser(value)"
            />

          </a-form-item>



          <!-- 投保加成% 19,4 -->
          <a-form-item name="insuranceMarkup" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('insuranceMarkup')"
                @click="handleLabelClick('insuranceMarkup')"
              >
                投保加成%
              </span>
            </template>
            <a-input-number :disabled="showDisable || (isEdit  === false)"
                            size="small"
                            style="width: 100%"
                            v-model:value="formData.insuranceMarkup"
                            :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                            :parser="value => inputParser(value)"
            />
          </a-form-item>




          <!-- 报关单号 -->
          <!--
          <a-form-item name="entryNo"   :label="'报关单号'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable || (isEdit  === false)"  size="small" v-model:value="formData.entryNo" allow-clear />
          </a-form-item>
          -->


          <!-- 报关日期 -->
          <!--
          <a-form-item name="entryDate"   :label="'报关日期'" class="grid-item"  :colon="false">
            <a-date-picker
              allow-clear
              :disabled="showDisable || (isEdit  === false)"
              v-model:value="formData.entryDate"
              id="releaseDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          -->



          <!-- 制单人 -->
          <a-form-item name="createBy" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('createBy')"
                @click="handleLabelClick('createBy')"
              >
                制单人
              </span>
            </template>
            <a-input :disabled="true"  size="small" v-model:value="formData.createBy" allow-clear />
          </a-form-item>

          <!-- 制单日期 -->
          <a-form-item name="createTime" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('createTime')"
                @click="handleLabelClick('createTime')"
              >
                制单日期
              </span>
            </template>
            <a-date-picker
              allow-clear
              :disabled="true"
              v-model:value="formData.createTime"
              id="releaseDate"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>





          <!-- 单据状态 -->
          <a-form-item name="dataState" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('dataState')"
                @click="handleLabelClick('dataState')"
              >
                单据状态
              </span>
            </template>
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.dataState" id="dataState">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.orderStatus"  :key="item.value +' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!-- 确认时间 -->
          <a-form-item name="confirmTime" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('confirmTime')"
                @click="handleLabelClick('confirmTime')"
              >
                确认时间
              </span>
            </template>
            <a-date-picker
              :disabled="true"
              v-model:value="formData.confirmTime"
              id="releaseDate"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>


          <!-- 审批状态 -->
          <a-form-item name="approvalStatus" class="grid-item"  :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('approvalStatus')"
                @click="handleLabelClick('approvalStatus')"
              >
                审批状态
              </span>
            </template>
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.approvalStatus" id="approvalStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.approval_status"  :key="item.value +' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>



          <!-- 备注 -->
          <!--          <a-form-item name="note"   :label="'备注'" class="grid-item merge-3"  :colon="false">-->
          <!--            <a-textarea :disabled="showDisable || (isEdit  === false)"  size="small" v-model:value="formData.note" :autoSize="{ minRows: 3, maxRows: 4 }"></a-textarea>-->
          <!--          </a-form-item>-->





          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      :loading="buttonLoadingMap.save"
                      v-show="props.editConfig?.editStatus !== 'show' " :disabled=" formData.dataState !== '0' || (isEdit  === false)">保存
            </a-button>

            <a-button size="small" class="cs-margin-right cs-warning" @click="handlerOnBack(true)">返回</a-button>
            <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <a-button size="small" type="ghost" @click="handlerConfirm" class="cs-margin-right"
                      :loading="buttonLoadingMap.confirm"
                      v-show="props.editConfig?.editStatus !== 'show' ">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check" style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>

          </div>
        </a-form>
      </div>
    </a-card>
    <a-card size="small" title="进货单表体" class="cs-card-form" v-if="editConfig">
      <!--  :head-id="props.editConfig.editData.sid" :is-edit="formData.dataState" :showDisable="showDisable" ref="comingGoodsListRef" -->
      <!-- 不为空才进行传入 props.editConfig.editData.id -->
      <biz-smoke-machine-incoming-goods-list :head-id="props.editConfig.editData.id" :is-edit="(formData.dataState === '0')" :showDisable="showDisable" ref="comingGoodsListRef" />
      <!--      <biz-incoming-goods-list :head-id="formData.id ? formData.id : props.editConfig?.editData?.id" :is-edit="isEdit" :showDisable="showDisable" ref="comingGoodsListRef" ></biz-incoming-goods-list>-->
    </a-card>


    <!-- 新增合同号 -->
    <cs-modal :visible="isShowExtract"   title="新增进货单"   :width="1000" :footer="false" @cancel="handlerBackExtract">
      <template #customContent>
        <add-incoming-dialog ref="contractModalRef" @cancel="handlerBackExtract" @save="handlerExtract"></add-incoming-dialog>
      </template>
    </cs-modal>



  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {createVNode, onMounted, reactive, ref, watch} from "vue";

import {usePCode} from "@/view/common/usePCode";
import {getInComingListBySid, insertInComingHead, updateInComingHead} from "@/api/cs_api_constant";
import BizIncomingGoodsList from "@/view/dec/incoming/list/BizIncomingGoodsListList.vue";
import CsSelect from "@/components/select/CsSelect.vue";
import useEventBus from "@/view/common/eventBus";

const {emitEvent,onEvent} = useEventBus()

// 导入数值类型格式化方法
import { useColumnsRender} from "@/view/common/useColumnsRender";
const { inputParser,inputFormatter }= useColumnsRender()


// 引入客户
import { useIncomingCommon } from '@/view/dec/incoming/common/IncomingCommon';
import {isNullOrEmpty} from "@/view/utils/common";

const { supplierList, portList, currList, priceTermList } = useIncomingCommon({ immediate: true });


// 是否显示新增合同号
const isShowExtract = ref(false)

// 引入按钮Loading
import {useButtonLoading} from "@/view/utils/useBtnLoading";
import ycCsApi from "@/api/ycCsApi";
import AddIncomingDialog from "@/view/dec/incoming/componment/AddIncomingDialog.vue";
import CsModal from "@/components/modal/cs-modal.vue";
import BizSmokeMachineIncomingGoodsList from "@/view/dec/smoke_machine/list/BizSmokeMachineIncomingGoodsListList.vue";
const { setLoading,buttonLoadingMap } = useButtonLoading()

const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const handlerOnBack = (val) => {
  emit('onEditBack', val);
};

// 定义组件名称
defineOptions({
  name: 'BizSmokeMachineIncomingGoodsHeadEdit',
})

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  // 主键ID
  id:'',
  // 业务类型
  businessType:'3',
  // 数据状态
  dataState:'',
  // 版本号
  versionNo:'',
  // 企业10位编码
  tradeCode:'',
  // 组织机构代码
  sysOrgCode:'',
  // 父级ID
  parentId:'',
  // 创建人
  createBy:'',
  // 创建时间
  createTime:'',
  // 更新人
  updateBy:'',
  // 更新时间
  updateTime:'',
  // 插入用户名
  insertUserName:'',
  // 更新用户名
  updateUserName:'',
  // 扩展字段1
  extend1:'',
  // 扩展字段2
  extend2:'',
  // 扩展字段3
  extend3:'',
  // 扩展字段4
  extend4:'',
  // 扩展字段5
  extend5:'',
  // 扩展字段6
  extend6:'',
  // 扩展字段7
  extend7:'',
  // 扩展字段8
  extend8:'',
  // 扩展字段9
  extend9:'',
  // 扩展字段10
  extend10:'',
  // 合同号
  contractNo:'',
  // 进货单号
  purchaseNo:'',
  // 客户
  customer:'',
  // 供应商
  supplier:'',
  // 发票号
  invoiceNo:'',
  // 起运港
  portOfDeparture:'',
  // 目的地/港
  destination:'',
  // 付款方式
  paymentMethod:'',
  // 价格条款
  priceTerm:'',
  // 价格条款对应港口
  priceTermPort:'',
  // 船名航次
  vesselVoyage:'',
  // 开航日期
  sailingDate:'',
  // 预计抵达日期
  expectedArrivalDate:'',
  // 作销日期
  salesDate:'',
  // 币种
  currency: 'EUR',
  // 合同金额
  contractAmount:'',
  // 保险费率%
  insuranceRate:'',
  // 投保加成%
  insuranceMarkup:'',
  // 制单人
  documentCreator:'',
  // 制单时间
  documentDate:'',
  // 单据状态
  documentStatus:'',
  // 确认时间
  confirmTime:'',
  // 审批状态
  approvalStatus:'',
  // 签约日期
  dateOfContract:'',
  // 购销合同号
  purchaseContractNo:'',
  // 报关单号
  entryNo:'',
  // 報關日期
  entryDate:'',
  // 序号
  serialNo:'',
  // 备注
  note:'',
  // 表体数据
  incomingList:[]
})
// 校验规则
const rules = {
  id:[
    {max: 40, message: '主键ID长度不能超过 40位字节', trigger: 'blur'}
  ],
  businessType:[
    {required: true, message: '业务类型不能为空', trigger: 'blur'},
    {max: 60, message: '业务类型长度不能超过 60位字节', trigger: 'blur'}
  ],
  dataState:[
    {max: 10, message: '数据状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  versionNo:[
    {max: 10, message: '版本号长度不能超过 10位字节', trigger: 'blur'}
  ],
  tradeCode:[
    {max: 10, message: '企业10位编码长度不能超过 10位字节', trigger: 'blur'}
  ],
  sysOrgCode:[
    {max: 10, message: '组织机构代码长度不能超过 10位字节', trigger: 'blur'}
  ],
  parentId:[
    {max: 40, message: '父级ID长度不能超过 40位字节', trigger: 'blur'}
  ],
  createBy:[
    {required: true, message: '制单人不能为空', trigger: 'blur'},
    {max: 50, message: '创建人长度不能超过 50位字节', trigger: 'blur'}
  ],
  createTime:[
    {required: true, message: '制单时间不能为空', trigger: 'blur'}
  ],
  updateBy:[
    {max: 50, message: '更新人长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateTime:[
  ],
  insertUserName:[
    {max: 50, message: '插入用户名长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateUserName:[
    {max: 50, message: '更新用户名长度不能超过 50位字节', trigger: 'blur'}
  ],
  extend1:[
    {max: 200, message: '扩展字段1长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend2:[
    {max: 200, message: '扩展字段2长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend3:[
    {max: 200, message: '扩展字段3长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend4:[
    {max: 200, message: '扩展字段4长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend5:[
    {max: 200, message: '扩展字段5长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend6:[
    {max: 200, message: '扩展字段6长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend7:[
    {max: 200, message: '扩展字段7长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend8:[
    {max: 200, message: '扩展字段8长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend9:[
    {max: 200, message: '扩展字段9长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend10:[
    {max: 200, message: '扩展字段10长度不能超过 200位字节', trigger: 'blur'}
  ],
  contractNo:[
    {required: true, message: '合同号不能为空', trigger: 'blur'},
    {max: 60, message: '合同号长度不能超过 60位字节', trigger: 'blur'}
  ],
  purchaseNo:[
    {required: true, message: '进货单号不能为空', trigger: 'blur'},
    {max: 60, message: '进货单号长度不能超过 60位字节', trigger: 'blur'}
  ],
  customer:[
    {required: true, message: '客户不能为空', trigger: 'blur'},
    {max: 200, message: '客户长度不能超过 200位字节', trigger: 'blur'}
  ],
  supplier:[
    {max: 200, message: '供应商长度不能超过 200位字节', trigger: 'blur'}
  ],
  invoiceNo:[
    {max: 60, message: '发票号长度不能超过 60位字节', trigger: 'blur'}
  ],
  portOfDeparture:[
    {max: 50, message: '起运港长度不能超过 50位字节', trigger: 'blur'}
  ],
  destination:[
    {max: 50, message: '目的地/港长度不能超过 50位字节', trigger: 'blur'}
  ],
  paymentMethod:[
    {max: 20, message: '付款方式长度不能超过 20位字节', trigger: 'blur'}
  ],
  priceTerm:[
    {max: 10, message: '价格条款长度不能超过 10位字节', trigger: 'blur'}
  ],
  priceTermPort:[
    {max: 20, message: '价格条款对应港口长度不能超过 20位字节', trigger: 'blur'}
  ],
  vesselVoyage:[
    {max: 100, message: '船名航次长度不能超过 100位字节', trigger: 'blur'}
  ],
  sailingDate:[
  ],
  expectedArrivalDate:[
  ],
  salesDate:[
  ],
  contractAmount:[
    {required: true, message: '合同金额不能为空', trigger: 'blur'},
    // { type: 'number', message: '合同金额不是有效的数字!'},
  ],
  insuranceRate:[
    // { type: 'number', message: '保险费率%不是有效的数字!'},
  ],
  insuranceMarkup:[
    // { type: 'number', message: '投保加成%不是有效的数字!'},
  ],
  documentCreator:[
    {max: 10, message: '制单人长度不能超过 10位字节', trigger: 'blur'}
  ],
  documentDate:[
  ],
  documentStatus:[
    {max: 10, message: '单据状态不能为空', trigger: 'blur'},
    {max: 10, message: '单据状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  confirmTime:[
  ],
  approvalStatus:[
    {max: 10, message: '审批状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  dateOfContract:[
  ],
  currency: [],
  purchaseContractNo:[
    {max: 60, message: '购销合同号长度不能超过 60位字节', trigger: 'blur'}
  ],
  entryNo:[
    {max: 200, message: '报关单号长度不能超过 200位字节', trigger: 'blur'}
  ],
  entryDate:[

  ],
  note:[
    {max: 200, message: '备注长度不能超过 200位字节', trigger: 'blur'}
  ]
}


// 重新获取表头信息
const getHeadDataById = async ()=>{
  const res = await window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.getIncomingGoodsHeadById, { id:formData.id })
  if (res.code === 200) {
    // 重新赋值表头合同金额栏位
    formData.contractAmount = res.data.contractAmount
  }
}

const pCode = ref('')


// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
// 进货单表体ref
const comingGoodsListRef = ref(null)
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      setLoading("save",true)
      formData.incomingList = comingGoodsListRef.dataSourceList
      window.majesty.httpUtil.putAction(`${ycCsApi.bizSmokeMachineInComingHead.update}/${formData.id}`, formData).then(res=>{
        if (res.code === 200){
          message.success('修改成功!')
          handlerOnBack({
            editData: res.data,
            showBody: true,
            editStatus: editStatus.EDIT,
          })
          Object.assign(formData, res.data);
          emitEvent('refresh-3-tb-info',true)

        }else {
          message.error(res.message)
        }
      }).finally(() => {
        setLoading("save",false)
      })
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};





const handlerConfirm = async () => {
  // 确认当前表单数据
  await formRef.value.validate();
  try {
    // 判断该数据是否已经确认过
    if (formData.dataState === '1') {
      message.warning('该数据已确认，不允许进行确认操作！')
      return
    }else if (formData.dataState === '2') {
      message.warning('该数据已作废，不允许进行确认操作！')
      return
    }

    setLoading("confirm",true)
    // 校验通过后，执行确认操作
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.confirmIncomingGoodsHead,formData)
    if (res.code === 200){
      message.success('确认成功!')
      // 将信息刷新
      Object.assign(formData, res.data);
      handlerOnBack({
        editData: res.data,
        showBody: true,
        showTB:true,
        editStatus: editStatus.EDIT,
        showBodyReceiptSell:true
      })

    }else{
      message.error(res.message)
    }
  }catch(error) {
    console.log('validate failed', error);
  }finally {
    // handlerOnBack({
    //   editData: null,
    //   showBody: true,
    //   editStatus: editStatus.EDIT,
    //   showBodyReceiptSell:true
    // })
    setLoading("confirm",false)
  }
}


// 打开提取合同弹框
const handlerSelectContract =  () => {
  // 重置tab页数据
  handlerOnBack({
    editData: null,
    showBody: false,
    editStatus: editStatus.ADD,

  })
  isShowExtract.value = true

}

// 关闭提取合同弹框
const handlerBackExtract = () => {
  isShowExtract.value = false
}


// 保存提取合同
const handlerExtract = (data) => {
  console.log('data',data)
  // 关闭提取合同弹框
  isShowExtract.value = false
  // 刷新表格数据
  // handlerSearch()
  window.majesty.httpUtil.postAction(ycCsApi.bizInComingHead.extractContract,data[0]).then((res)=>{
    // console.log('res',res)

    if (res.code !== 200){
      message.error(res.message)
      return
    }else {
      // 提取完成后，重新刷新数据，
      Object.assign(formData, res.data.head)
      // 通过Bus发送事件，刷新表体数据
      emitEvent('refreshIncomingGoodsList',res.data.head.id)
      handlerOnBack({
        editData: null,
        showBody: true,
        editStatus: editStatus.EDIT,
        showBodyReceiptSell:false
      })
    }


  })
}



const isEdit = ref(true)
watch(() => formData.dataState, (newVal, oldVal) => {
  if (newVal === '1' || newVal === '2') {
    isEdit.value = false
  } else {
    isEdit.value = true
  }
},{immediate: true,deep:true})





// =========================== 内审 ====================================
import {useFieldMarking} from "@/utils/useFieldMarking";
import {ExclamationCircleOutlined} from "@ant-design/icons-vue";
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()

// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  businessType: '业务类型',
  contractNo: '合同号',
  purchaseNo: '进货单号',
  customer: '客户',
  supplier: '供应商',
  invoiceNo: '发票号',
  portOfDeparture: '启运港',
  destination: '目的地/港',
  paymentMethod: '付款方式',
  priceTerm: '价格条款',
  priceTermPort: '价格条款对应港口',
  vesselVoyage: '船名航次',
  sailingDate: '开航日期',
  expectedArrivalDate: '预计抵达日期',
  salesDate: '作销日期',
  contractAmount: '合同金额',
  insuranceRate: '保险费率%',
  insuranceMarkup: '投保加成%',
  createBy: '制单人',
  createTime: '制单日期',
  dataState: '单据状态',
  confirmTime: '确认时间',
  approvalStatus: '审批状态'
}


const auditLoading = ref(false)
const invalidLoading = ref(false)

/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }

  // 审核意见输入框
  const auditOpinion = ref('同意审批')

  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.id],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'contract',
      }

      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.audit, params)
        .then(res => {
          if (res.code === 200) {
            message.success("审核通过成功！")
            // 返回列表页面
            handlerOnBack(true)
          } else {
            message.error(res.message || '审核失败')
          }
        })
        .catch(error => {
          console.error('审核失败:', error)
          message.error(error.message || error  || '审核失败，请重试')
        })
        .finally(() => {
          auditLoading.value = false
        })
    },
    onCancel() {
      // 取消操作
    },
  });
}

/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }

  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')

  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.id],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'contract',
      }

      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.reject, params)
        .then(res => {
          console.log('调用内审退回接口',res)
          if (res.code === 200) {
            // 审核退回成功后，调用标记保存接口
            return saveCurrentMarkings(formData.id, 'default', fieldMarkings.value)
          } else {
            throw new Error(res.message || '审核退回失败')
          }
        })
        .then(res => {
          message.success("审核退回成功！")
          // 返回列表页面
          handlerOnBack(true)
        })
        .catch(error => {

          console.error('审核退回失败:', error)
          message.error(error.message || error || '审核退回失败，请重试')
        })
        .finally(() => {
          invalidLoading.value = false
        })
    },
    onCancel() {
      // 取消操作
    },
  });
}


// 初始化操作
onMounted(() => {
  // 获取当前界面标记的id
  getBySidAndFormType(props.editConfig.editData.id, 'default')
  getPCode().then(res=>{
    pCode.value = res;
  })
  console.log('editConfig', props.editConfig)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    // 不要用空对象覆盖formData，新增状态下保持默认值即可
    // Object.assign(formData, {});

  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    // 根据表头id获取数据
    if (props.editConfig.editData && props.editConfig.editData.id) {
      // 调用接口获取数据
      window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.getIncomingGoodsHeadById, {id: props.editConfig.editData.id}).then(res => {
        if (res.code === 200) {
          // 处理返回的数据
          Object.assign(formData, res.data);
        }else {
          message.error(res.message)
        }
      })
    }
    // Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    // 根据表头id获取数据
    if (props.editConfig.editData && props.editConfig.editData.id) {
      // 调用接口获取数据
      window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.getIncomingGoodsHeadById, {id: props.editConfig.editData.id}).then(res => {
        if (res.code === 200) {
          // 处理返回的数据
          Object.assign(formData, res.data);
        }else {
          message.error(res.message)
        }
      })
    }
    showDisable.value = true
  }


  onEvent('refresh-3-head-info',()=>{
    getHeadDataById()
  })


});


</script>

<style lang="less" scoped>

.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.form-label:hover {
  opacity: 0.8;
}

.label-green {
  background-color: #52c41a;
  color: white;
}

.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>



