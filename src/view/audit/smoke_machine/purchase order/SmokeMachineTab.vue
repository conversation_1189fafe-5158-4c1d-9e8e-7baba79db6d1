<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="进货单" >
          <biz-smoke-machine-incoming-goods-head-edit ref="headTab" :edit-config="editConfig" @onEditBack="editBack" :operation-status="editConfig.editStatus"  ></biz-smoke-machine-incoming-goods-head-edit>
<!--          <biz-incoming-goods-head-edit ref="headTab" :edit-config="editConfig" @onEditBack="editBack" :operation-status="editConfig.editStatus"></biz-incoming-goods-head-edit>-->
        </a-tab-pane>
        <!-- 证件信息 -->
        <a-tab-pane key="documentTab" tab="证件信息" v-if="showBody">
          <biz-smoke-machine-incoming-goods-document-edit ref="shipFrom" :edit-config="editConfig" :operation-status="editConfig.editStatus" :head-id="headId"  @onEditBack="editBack" :is-all-confirmed="isAllConfirmed"></biz-smoke-machine-incoming-goods-document-edit>
        </a-tab-pane>

        <!-- 投保信息 -->
        <a-tab-pane key="tbTab" tab="投保信息" v-if="showBody">
          <biz-smoke-machine-incoming-goods-tb-edit  ref="shipFrom" :edit-config="editConfig" :operation-status="editConfig.editStatus" :head-id="headId"  @onEditBack="editBack" :is-all-confirmed="tbIsEdit"></biz-smoke-machine-incoming-goods-tb-edit>
        </a-tab-pane>

        <!-- 附件信息 -->
        <a-tab-pane key="attachTab" tab="附件信息" v-if="showBody">
          <biz-smoke-machine-attach ref="shipFrom" :edit-config="editConfig" :is-all-confirmed="isAllConfirmed" :operation-status="editConfig.editStatus" :head-id="headId"  @onEditBack="editBack"></biz-smoke-machine-attach>
        </a-tab-pane>

        <!-- 审核界面  -->
        <a-tab-pane key="auditTab" tab="审核界面" v-if="showBody">
          <biz-smoke-machine-audit ref="shipFrom" :edit-config="editConfig" :operation-status="editConfig.editStatus" :head-id="headId"  @onEditBack="editBack"></biz-smoke-machine-audit>
        </a-tab-pane>



        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000"/>
          </div>
        </template>
      </a-tabs>
    </div>

  </section>
</template>

<script setup>

import BizSmokeMachineIncomingGoodsDocumentEdit from "@/view/dec/smoke_machine/document/BizSmokeMachineIncomingGoodsDocumentEdit.vue";

import {onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import useEventBus from "@/view/common/eventBus";
import BizSmokeMachineIncomingGoodsHeadEdit from "@/view/audit/smoke_machine/purchase order/head/BizSmokeMachineIncomingGoodsHeadEdit.vue";
import BizSmokeMachineIncomingGoodsTbEdit from "@/view/audit/smoke_machine/purchase order/tb/BizSmokeMachineIncomingGoodsTbEdit.vue";
import BizSmokeMachineAttach from "@/view/audit/smoke_machine/purchase order/attach/BizSmokeMachineAttach.vue";
import BizSmokeMachineAudit from "@/view/audit/smoke_machine/purchase order/audit/BizSmokeMachineAudit.vue";
import {isNullOrEmpty} from "@/view/utils/common";
const {onEvent} = useEventBus()

// import BizIncomingGoodsHeadEdit from "@/view/dec/incoming/head/BizIncomingGoodsHeadEdit.vue";
// import BizIncomingGoodsDocumentEdit from "@/view/dec/incoming/document/BizIncomingGoodsDocumentEdit.vue";
// import InComingAttach from "@/view/dec/incoming/attach/BpAnalyseAttach.vue";
// import BizIAttach from "@/view/dec/imported_cigarettes/imported_attach/BizIAttach.vue";
// import InComingAudit from "@/view/dec/incoming/audit/InComingAudit.vue";
// import BizIncomingGoodsInsureEdit from "@/view/dec/incoming/insure/BizIncomingGoodsInsureEdit.vue";
// import BizISellHeadEdit from "./sell/head/BizISellHeadEdit.vue";
defineOptions({
  name:'SmokeMachineTab',
})



const emit = defineEmits(['onEditBack'])

/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});



/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}


const isAllConfirmed = ref(false)
const tbIsEdit = ref(false)

/* 激活Tab key */
const tabName = ref('headTab');

/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  shipFrom:true,
})

/* 表头headId */
const headId = ref('')


// 是否显示子模块
const showBody = ref(false)
// 是否显示投保信息
const showTB = ref(false)

/* 返回tab界面 */
const editBack = (val) => {
  // console.log('val', val)
  if (val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    showTB.value = val.showTB
    if(val.editData != null){
      headId.value =  val.editData.id
      props.editConfig.editStatus = val.editStatus
      props.editConfig.editData = val.editData
      // 这个界面只有一个确认
      if (val.editData.dataState === '1'){
        isAllConfirmed.value = true
      }
    }
  }else if (val.editStatus === editStatus.ADD){
    headId.value =  ''
    showBody.value = val.showBody
    showTB.value = val.showTB
    props.editConfig.editStatus = val.editStatus
    props.editConfig.editData = val.editData
  } else {
    // 如果val === false 返回进行刷新界面
    emit('onEditBack', val)
  }
}


/* 初始化操作 */
onMounted(()=>{
  // console.log('props.editConfig', props.editConfig)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    headId.value = ''
    if (props.editConfig.editData) {
      props.editConfig.editData = {}
    }
    showBody.value = false
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showBody.value = true

    console.log('编辑：',props.editConfig.editData)
    headId.value = props.editConfig.editData.id
  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW ) {
    headId.value = props.editConfig.editData.id
    showBody.value = true
  }
  isAllConfirmed.value = false

  if (props.editConfig.editData.dataState === '2') {
    tbIsEdit.value = true
    isAllConfirmed.value = true
  }

  // 判断只有当用户点击保存时 显示 【证件信息】、【附件信息】、【审核界面】
  // 用户点击确认时，显示投保信息
  showBody.value = !isNullOrEmpty(props.editConfig.editData.updateTime);
  // 当用户的状态为【已确认】时，显示【投保信息】
  showTB.value = props.editConfig.editData.status === '1';

  onEvent('update_incoming_all',()=>{
    isAllConfirmed.value = true
  })
})






/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true

})

</script>

<style lang="less" scoped>

</style>
