import { Tag } from 'ant-design-vue'
import { baseColumns } from '@/view/common/baseColumns'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import { productClassify, DATA_STATUS } from '@/view/common/constant'

const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()

export function getHeadColumns() {
  const commColumns = [
    'id',
    'businessType',
    'contractNo',
    'buyer',
    'seller',
    'contractValidityDate',
    'versionNo',
    'documentMaker',
    'documentMakeDate',
    'dataStatus',
    'confirmTime'
  ]

  // 导出字段设置
  const excelColumnsConfig = [
    ...commColumns,
    ...baseColumnsExport
  ]

  // 表格字段设置
  const columnsConfig = [
    ...commColumns,
    ...baseColumnsShow
  ]

  const totalColumns = [
    {
      minWidth: 80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left'
    },
    {
      minWidth: 155,
      title: '业务类型',
      align: 'center',
      dataIndex: 'businessType',
      resizable: true,
      key: 'businessType',
      customRender: ({ text }) => {
        return (<span>{cmbShowRender(text, productClassify.businessType)}</span>)
      }
    },
    {
      minWidth: 140,
      title: '合同号',
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo'
    },
    {
      minWidth: 190,
      title: '买方',
      align: 'center',
      dataIndex: 'buyer',
      resizable: true,
      key: 'buyer'
    },
    {
      minWidth: 190,
      title: '卖方',
      align: 'center',
      dataIndex: 'seller',
      resizable: true,
      key: 'seller'
    },
    {
      minWidth: 120,
      title: '合同有效期',
      align: 'center',
      dataIndex: 'contractValidityDate',
      resizable: true,
      key: 'contractValidityDate'
    },
    {
      minWidth: 60,
      title: '版本号',
      align: 'center',
      dataIndex: 'versionNo',
      resizable: true,
      key: 'versionNo'
    },
    {
      title: '制单人',
      minWidth: 100,
      align: 'center',
      dataIndex: 'documentMaker',
      resizable: true,
      key: 'documentMaker'
    },
    {
      minWidth: 150,
      title: '制单时间',
      align: 'center',
      dataIndex: 'documentMakeDate',
      resizable: true,
      key: 'documentMakeDate'
    },
    {
      minWidth: 100,
      title: '单据状态',
      align: 'center',
      dataIndex: 'dataStatus',
      resizable: true,
      key: 'dataStatus',
      customRender: ({ text }) => {
        const color = text === DATA_STATUS.DRAFT ? 'success' : (text === DATA_STATUS.CONFIRMED ? 'processing' : 'error')
        return (<Tag color={color}>{cmbShowRender(text, productClassify.data_status)}</Tag>)
      }
    },
    {
      minWidth: 150,
      title: '确认时间',
      align: 'center',
      dataIndex: 'confirmTime',
      resizable: true,
      key: 'confirmTime'
    }
  ]

  return {
    tableColumns: totalColumns.filter(item => columnsConfig.includes(item.key)),
    excelColumns: totalColumns.filter(item => excelColumnsConfig.includes(item.key))
  }
}

export function getBodyColumns() {
  const columnsConfig = [
    ...baseColumnsShow,
    'id',
    'gName',
    'gModel',
    'unit',
    'qty',
    'boxNum',
    'grossWeight',
    'unitPrice',
    'moneyAmount',
    'note'
  ]

  const bodyColumns = [
    {
      minWidth: 160,
      title: '商品名称',
      align: 'center',
      dataIndex: 'gName',
      resizable: true,
      key: 'gName'
    },
    {
      minWidth: 150,
      title: '产品型号',
      align: 'center',
      dataIndex: 'gModel',
      resizable: true,
      key: 'gModel'
    },
    {
      minWidth: 100,
      title: '单位',
      align: 'center',
      dataIndex: 'unit',
      resizable: true,
      key: 'unit'
    },
    {
      minWidth: 120,
      title: '数量',
      align: 'center',
      dataIndex: 'qty',
      resizable: true,
      key: 'qty',
      precision: 4
    },
    {
      minWidth: 120,
      title: '箱数',
      align: 'center',
      dataIndex: 'boxNum',
      resizable: true,
      key: 'boxNum',
      precision: 4
    },
    {
      minWidth: 120,
      title: '毛重',
      align: 'center',
      dataIndex: 'grossWeight',
      resizable: true,
      key: 'grossWeight',
      precision: 4
    },
    {
      minWidth: 120,
      title: '单价',
      align: 'center',
      dataIndex: 'unitPrice',
      resizable: true,
      key: 'unitPrice',
      precision: 8
    },
    {
      minWidth: 120,
      title: '金额',
      align: 'center',
      dataIndex: 'moneyAmount',
      resizable: true,
      key: 'moneyAmount'
    },
    {
      minWidth: 100,
      title: '备注',
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note'
    }
  ]

  return bodyColumns.filter(item => columnsConfig.includes(item.key))
}

export function getMaterialColumns() {
  const columnsConfig = [
    'id',
    'gName',
    'fullEnName',
    'merchandiseCategories',
    'supplier'
  ]

  const materialColumns = [
    {
      width: '25%',
      title: '商品名称',
      align: 'center',
      dataIndex: 'gName',
      resizable: true,
      key: 'gName'
    },
    {
      width: '25%',
      title: '英文名称',
      align: 'center',
      dataIndex: 'fullEnName',
      resizable: true,
      key: 'fullEnName'
    },
    {
      width: '15%',
      title: '商品类别',
      align: 'center',
      dataIndex: 'merchandiseCategories',
      resizable: true,
      key: 'merchandiseCategories'
    },
    {
      width: '35%',
      title: '供应商',
      align: 'center',
      dataIndex: 'supplier',
      resizable: true,
      key: 'supplier'
    }
  ]

  return materialColumns.filter(item => columnsConfig.includes(item.key))
}
