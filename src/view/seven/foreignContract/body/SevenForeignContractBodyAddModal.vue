<template>
  <a-modal v-model:open="open" title="选择物料信息" :keyboard="false" :width="1200"
           :mask-closable="false" :destroy-on-close="false">
    <div class="cs-action">
      <!-- 表格区域 -->
      <a-table
        :animate-rows="false"
        ref="tableRef"
        :loading="tableLoading"
        :columns="columns"
        :custom-row="customRow"
        :data-source="datasourceList"
        :row-selection="{ selectedRowKeys: selectedKeys , onChange: onSelectChange }"
        row-key="id"
        :scroll="{ y: 300}"
        :pagination="false"
        size="small"
        style="font-size: 12px"
      >
        <template #bodyCell="{ column, record, text}">
          <template v-if="column.dataIndex === 'supplier'">
            <span>{{ cmbShowRender(text, optionsConfig.merchantOptions) }}</span>
          </template>

          <template v-if="column.dataIndex === 'merchandiseCategories'">
            <span>{{ cmbShowRender(text, optionsConfig.productTypeOptions) }}</span>
          </template>
        </template>
      </a-table>
    </div>

    <template #footer>
      <a-button type="primary" @click="handleSave" :loading="saveLoading">保存</a-button>
      <a-button @click="closeModal">返回</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { onBeforeUnmount, ref, shallowRef, watch, toRaw, inject } from 'vue'
import { message } from 'ant-design-vue'
import { EMPTY } from '@/view/common/constant'
import { getMaterialColumns } from '@/view/seven/foreignContract/js/columns.jsx'
import { getMaterial } from '@/api/seven/foreignContract'
import { OPTIONS_CONFIG } from '@/view/seven/foreignContract/js/handle'
import { useColumnsRender } from '@/view/common/useColumnsRender'

const optionsConfig = inject(OPTIONS_CONFIG)

const { cmbShowRender } = useColumnsRender()

// 物料显示列
const columns = getMaterialColumns()

// emit
const emit = defineEmits(['saveBody'])

// 表格loading
const tableLoading = ref(false)

// 保存按钮loading
const saveLoading = ref(false)

// 打开状态
const open = ref(false)

/**
 * 打开模态框
 */
function openModal() {
  open.value = true
}

/**
 * 关闭模态框
 */
function closeModal() {
  open.value = false
}

// open侦听器
const stopWatchOpen = watch(open, (openStatus) => {
  if (openStatus) {
    saveLoading.value = false
    selectedKeys.value = []
    getList()
  }
})

// 选中键列表
const selectedKeys = ref([])

/**
 * 选中行变化回调函数
 * @param selectedRowKeys 行keys
 */
function onSelectChange(selectedRowKeys) {
  selectedKeys.value = selectedRowKeys
}

/**
 * 自定义行
 * @param record 数据记录
 */
function customRow(record) {
  return {
    onDblclick: () => {
      const currentKeys = selectedKeys.value
      if (currentKeys['includes'](record.id)) {
        selectedKeys.value = currentKeys.filter(key => key !== record.id)
      } else {
        currentKeys.push(record.id)
      }
    },
    style: { cursor: 'pointer' }
  }
}

// 数据源列表
const datasourceList = shallowRef([])

/**
 * 获取数据列表
 */
async function getList() {
  tableLoading.value = true
  try {
    const res = await getMaterial()
    if (!res.success) {
      selectedKeys.value = []
      datasourceList.value = EMPTY.ARRAY
      message.error(res.message)
      return
    }
    datasourceList.value = res.data || EMPTY.ARRAY
  } finally {
    tableLoading.value = false
  }
}

/**
 * 保存
 */
async function handleSave() {
  if (selectedKeys.value.length < 1) {
    message.warn('请选择物料')
    return
  }
  const ids = toRaw(selectedKeys.value) || EMPTY.ARRAY
  const records = datasourceList.value.filter(item => ids.includes(item.id))
  emit('saveBody', { records, ids })
}

onBeforeUnmount(() => {
  stopWatchOpen()
})

defineExpose({ openModal, closeModal, saveLoading })

defineOptions({
  name: 'SevenForeignContractBodyAddModal'
})
</script>
