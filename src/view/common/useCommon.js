import {nextTick, reactive, ref} from "vue";
import {downloadFile, getAction, postAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import {isNullOrEmpty} from "@/view/utils/common";
import {editStatus} from "@/view/common/constant";
import {getMerchantCodeValueClient} from "../../api/bi/bi_client_info";

/**
 * 自定义Hook函数
 */
export function useCommon() {

  /**
   * 表格是否显示正在加载
   */
  const tableLoading = ref(false)


  /**
   * 数据列表
   */
  const dataSourceList = ref([])


  /**
   * 搜索地址URL
   */
  const ajaxUrl = reactive({
    selectAllPage: '',
    exportUrl: ''
  })

  /**
   * 是否显示新增/编辑界面
   */
  const show = ref(true)


  /**
   * 当前表单数据
   */
  const editConfig = ref({
    editStatus: editStatus.ADD
  })


  /**
   * 分页信息
   */
  /* 当前页 */
  const page = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
  })


  /**
   * 是否显示搜素信息
   */
  const showSearch = ref(true)


  /**
   * 搜素框的ref
   */
  const headSearch = ref(null)


  /**
   * 导出Loading
   */
  const exportLoading = ref(false)


  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData:[],
    loading: false,
  });

  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  function onSelectChange (selectedRowKeys, rowSelectData){
    gridData.selectedData = rowSelectData
    gridData.selectedRowKeys = selectedRowKeys
  }


  /**
   * 点击编辑按钮
   * @param row 表格行数据
   */
  function handleEditByRow(row) {
    // 在这里添加处理编辑行的逻辑
    show.value = !show.value
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData = row
  }


  /**
   * 点击查看按钮
   * @param row 表格行数据
   */
  function handleViewByRow(row) {
    console.log('查看:', row)
    // 在这里添加处理查看行的逻辑
    show.value = !show.value
    editConfig.value.editStatus = editStatus.SHOW
    editConfig.value.editData = row
  }


  /**
   * 表格点击编辑/查看按钮
   */
  function operationEdit(type) {
    return type === 'edit' && !operationEditShow()
      ? {display: 'none'}
      : {marginLeft: type === 'view' && operationEditShow() ? '15px' : '0'}
  }


  /**
   * 是否显示编辑标签
   * @returns {boolean}
   */
  function operationEditShow() {
    // 根据实际情况返回是否显示编辑按钮的状态
    return true // 示例中总是返回 true
  }


  /**
   * 页码 或者 PageSize发生变化时触发
   * @param pageNumber 当前页
   * @param pageSize 每页显示条数
   */
  function onPageChange(pageNumber, pageSize) {
    // console.log('PageNumber:', pageNumber)
    // console.log('PageNumber:', pageSize)
    // console.log('页码或者PageSize发生变化时触发')
    page.current = pageNumber
    page.pageSize = pageSize
    // 在这里添加处理页码变化的逻辑
    doSearch()
  }


  /**
   * 打开搜索
   */
  function handleShowSearch() {
    showSearch.value = !showSearch.value
  }

  function handlerSearch() {
    // 在这里添加处理搜索的逻辑
    // 通过refs获取到组件的实例，然后获取输入框的值
    // console.log('搜索参数:', headSearch.value.searchParam)
    // vue2 ref不能修改，vue3可以
    // headSearch.value.searchParam.shipToId = '1'
    doSearch(ajaxUrl.selectAllPage, 'post')
  }


  /* 重置搜索框内容 */
  function handlerRefresh() {
      if (headSearch.value) {
        headSearch.value.resetSearch()
        page.current = 1
        page.pageSize = 20
      }
    doSearch()
  }



  /* 查询数据 */
  function doSearch() {
    tableLoading.value = true
    window.majesty.httpUtil.postAction(`${ajaxUrl.selectAllPage}?page=${page.current}&limit=${page.pageSize}`,
      getSearchParams()
    ).then(res => {
      dataSourceList.value = res.data
      page.total = res.total
      // 重置选择数据
      restSelectData()
    }).finally(() => {
      tableLoading.value = false
    })
  }



  /* 获取数据方法 */
  function getList() {
    return doSearch()
  }

  /* 通过ref获取搜索框参数 */
  function getSearchParams() {
    return Object.assign({}, (headSearch.value ? headSearch.value.searchParam : {}))
  }


  /**
   * 获取第一个表格的可视化高度
   * @param {*} extraHeight 额外的高度(表格底部的内容高度 Number类型,默认为74)
   * @param {*} id 当前页面中有多个table时需要制定table的id
   */
  function getTableScroll(extraHeight, id) {
    if (typeof extraHeight == "undefined") {
      //  默认底部分页64 + 边距10
      extraHeight = 74
    }
    let tHeader = null
    if (id) {
      tHeader = document.getElementById(id) ? document.getElementById(id).getElementsByClassName("ant-table-thead")[0] : null
    } else {
      tHeader = document.getElementsByClassName("surely-table-header")[0]
    }
    //表格内容距离顶部的距离
    let tHeaderBottom = 0
    if (tHeader) {
      tHeaderBottom = tHeader.getBoundingClientRect().bottom
    }
    // console.log('tHeaderBottom', tHeaderBottom)
    // console.log('extraHeight', extraHeight)
    //表格内容顶部的高度
    // let tHeaderBottom = document.getElementsByClassName("surely-table-header")[0].getBoundingClientRect().bottom
    //表格内容底部的高度
    // let extraHeight = document.getElementsByClassName("surely-table-footer")[0].getBoundingClientRect().height
    //窗体高度-表格内容顶部的高度-表格内容底部的高度
    // let height = document.body.clientHeight - tHeaderBottom - extraHeight
    let height = `calc(100vh - ${tHeaderBottom + extraHeight}px)`
    // console.log('height', height)
    return height
  }


  /**
   * 导出文件
   * @param fileName 导出文件名
   * @param exportColumns 导出查询参数
   * @param exportHeader 导出字段配置，excel表头键值对 { key: 'customerType', value: '客户类型'}
   */
  function doExport(fileName, exportHeader, exportColumns) {
    exportLoading.value = true
    window.window.majesty.httpUtil.downloadFile( ajaxUrl.exportUrl,
      fileName,
      {
        exportColumns: exportColumns ? exportColumns : getSearchParams(),
        name: fileName,
        header: exportHeader ? filterExportParams(exportHeader) : []
      },
      'post',
      () => {
      }
    ).then((res) => {

    }).finally(() => {
      exportLoading.value = false
    })
  }


  /* 转换Columns配置 将关键属性转为 key,value形式，并且过滤操作等属性 */
  function filterExportParams(params) {
    let tempArr = []
    if (isNullOrEmpty(params)) {
      return []
    }
    params.value.forEach(item => {
      // 如果当前字段中dataIndex是operation 则不导出
      if (item.dataIndex !== 'operation') {
        tempArr.push({
          key: item.dataIndex,
          value: item.title
        })
      }
    })
    return tempArr
  }



  /* 重置选择数据 */
  function restSelectData (){
    // 返回清空选择数据
    gridData.selectedData = [];
    gridData.selectedRowKeys = [];
    editConfig.editData = {}
  }



  /* =================================== 双击进入表格 ================================== */
  // 自定义行属性
  const customRow = (record) => {
    return {
      onDblclick: () => {
        handleRowDblclick(record);
      },
      style: {
        cursor: 'pointer'
      }
    }
  }


  const handleRowDblclick = (record) => {
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData = record

    show.value = !show.value;
  };



  // 返回状态 和 方法
  return {
    show,
    editConfig,
    page,
    showSearch,
    headSearch,
    handleEditByRow,
    handleViewByRow,
    operationEdit,
    onPageChange,
    handleShowSearch,
    handlerSearch,
    doExport,
    ajaxUrl,
    dataSourceList,
    getList,
    getTableScroll,
    tableLoading,
    exportLoading,
    handlerRefresh,
    gridData,
    onSelectChange,
    getSearchParams,
    doSearch,
    customRow
  }

}
