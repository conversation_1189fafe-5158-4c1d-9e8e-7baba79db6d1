// import request from '@/utils/request'
import {ref} from "vue";
import xdoCache from "@/utils/XdoCache";

/**
 * 自定义Hook函数
 */
export function usePCode() {


    // 定义变量
    const pCode = ref();

    /* 获取PCode信息 */
    const getPCode = async () => {
      let param = 'CONTAINER_MODEL,AREA,POST_AREA,TRADE,UNIT,COUNTRY,COUNTRY_OUTDATED,PORT_LIN,LEVYMODE,LC_TYPE,TRANSF,USE_TO,CURR,CURR_OUTDATED,CUSTOMS_REL,TRANSAC,WRAP,LEVYTYPE,ELEMENT,LICENSEDOCU,CO_TYPE,DIST_TYPE,COMPLEX,PRODUCT_TYPE_MNL,COMPANY,KEY_PRODUCT_MARK,TRADE_TERMS'
      let list = param.split(',')
      for (let i in list) {
        pCode[list[i]] = await xdoCache.get(list[i])
      }
      pCode.value = {...pCode}
      return {...pCode};
    }

    // 返回状态 和 方法
    return{
      pCode,
      getPCode
    }

}
