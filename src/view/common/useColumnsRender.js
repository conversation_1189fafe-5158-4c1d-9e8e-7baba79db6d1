import {isNullOrEmpty} from "@/view/utils/common";
import xdoCache from "@/utils/XdoCache";


export function useColumnsRender() {





    /**
     * 自定义截取两位小数方法，如果小数位数超过两位，则截取两位小数，如果不足两位小数，则在后面补零，否则返回原值
     * @param value
     * @returns {*}
     */
    function toFixedTwo(value) {
      if (isNullOrEmpty(value)) {
        return value;
      }else {
          //  如果是数字类型，则转换为字符串
          if (typeof value === 'number') {
            value = value.toString()
          }
          if (value.indexOf('.') !== -1) {
            let index = value.indexOf('.')
            let val = value.substring(index + 1, index + 3)
            if (val.length === 1) {
              return value + '0'
            } else if (val.length === 2) {
              return value
            } else {
              return value.substring(0, index + 3)
            }
          } else {
            return value + '.00'
          }
      }
    }


  /**
   * 自定义渲染千分位分隔符，如果是数字类型，则转换为字符串，注意小数位数不渲染千分位分隔符
   * @param value 传入的值
   * @param isFixed 是否需要保留指定小数位数，true 保留两位小数，false 不保留小数位数
   * @param number 保留小数位数
   * @returns {*}
   */
  function formatNumber(value,isFixed,number) {
      if (isNullOrEmpty(value)) {
        return value;
      } else {
        // 数字类型转字符串处理
        if (typeof value === 'number') {
          value = value.toString();
        }

        // 分割整数和小数部分
        let parts = value.split('.');
        let integerPart = parts[0];
        let decimalPart = parts.length > 1 ? parts[1] : '';

        // 处理负号标识
        let sign = '';
        if (integerPart.startsWith('-')) {
          sign = '-';
          integerPart = integerPart.substring(1);
        }

        // 添加千分位分隔符
        let formattedInteger = '';
        let count = 0;
        for (let i = integerPart.length - 1; i >= 0; i--) {
          formattedInteger = integerPart[i] + formattedInteger;
          count++;
          // 每隔三位添加逗号（排除首位）
          if (count % 3 === 0 && i !== 0) {
            formattedInteger = ',' + formattedInteger;
          }
        }

        // 还原负号
        formattedInteger = sign + formattedInteger;

        // 处理小数部分

        if (!isNullOrEmpty(isFixed) && isFixed === true) {
          // 如果指定了小数位数，则截取指定位数
          if (decimalPart.length >= number) {
             decimalPart = decimalPart.substring(0, number);
          } else {
            // 如果小数位数不足，则在后面补零
             decimalPart += '0'.repeat(number - decimalPart.length);
          }
        }

        // 如果是整数 不需要添加.
        if (parts.length === 1) {
          return formattedInteger;
        }
        return formattedInteger + '.' + decimalPart;
      }
  }

  // 格式化数字为千分位分隔的工具函数
  function formatNumberNew (value) {
    if (value === undefined || value === null || value === '') {
      return '';
    }
    // 将数字转换为字符串并添加千分位分隔符
    // 检查是否有小数部分
    const numValue = Number(value);
    if (Number.isNaN(numValue)) {
      return '';
    }

    const hasDecimal = numValue % 1 !== 0;
    if (hasDecimal) {
      // 如果有小数，保留原有小数位数
      return new Intl.NumberFormat('zh-CN').format(numValue);
    } else {
      // 如果没有小数，补充.00
      return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
    }
  }


  /**
   * 渲染指定位数 数值
   * 自定义渲染千分位分隔符，如果是数字类型，则转换为字符串，注意小数位数不渲染千分位分隔符
   * @param value 传入的值
   * @param isFixed 是否需要保留指定小数位数，true 保留两位小数，false 不保留小数位数
   * @param number 保留小数位数
   * @returns {*}
   */
  function formatSpecifiedNumber(value,isFixed,number) {
    if (isNullOrEmpty(value)) {
      return value;
    }else if (value === 0) {
      // 传几位小数，就保留几位小数
      return '0.' + '0'.repeat(number);
    } else {
      // 数字类型转字符串处理
      if (typeof value === 'number') {
        value = value.toString();
      }

      // 分割整数和小数部分
      let parts = value.split('.');
      let integerPart = parts[0];
      let decimalPart = parts.length > 1 ? parts[1] : '';

      // 处理负号标识
      let sign = '';
      if (integerPart.startsWith('-')) {
        sign = '-';
        integerPart = integerPart.substring(1);
      }

      // 添加千分位分隔符
      let formattedInteger = '';
      let count = 0;
      for (let i = integerPart.length - 1; i >= 0; i--) {
        formattedInteger = integerPart[i] + formattedInteger;
        count++;
        // 每隔三位添加逗号（排除首位）
        if (count % 3 === 0 && i !== 0) {
          formattedInteger = ',' + formattedInteger;
        }
      }

      // 还原负号
      formattedInteger = sign + formattedInteger;

      // 处理小数部分

      if (!isNullOrEmpty(isFixed) && isFixed === true) {
        // 如果指定了小数位数，则截取指定位数
        if (decimalPart.length >= number) {
          // decimalPart = decimalPart.substring(0, number);
        } else {
          // 如果小数位数不足，则在后面补零
          decimalPart += '0'.repeat(number - decimalPart.length);
        }
      }

      // 如果是整数 不需要添加.
      // if (parts.length === 1) {
      //   return formattedInteger;
      // }
      return formattedInteger + '.' + decimalPart;
    }
  }


  /**
   * 数字格式化函数（空间输入格式化）
   * @param {number|string} value - 需要格式化的值
   * @returns {string} 格式化后的带千位分隔符的字符串
   */
  const inputFormatter = (value) => {
    // 处理空值情况（包括 null、undefined、空字符串）
    if (value === null || value === undefined || value === '') return '0'

    const numStr = value.toString().trim()

    // 处理负号
    const hasNegative = numStr.startsWith('-')
    const cleanStr = hasNegative ? numStr.slice(1) : numStr

    // 分割整数和小数部分
    const [integer, decimal] = cleanStr.split('.')

    // 添加千位分隔符
    const formattedInteger = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

    // 重组带小数部分
    const result = decimal
      ? `${formattedInteger}.${decimal}`
      : formattedInteger

    return hasNegative ? `-${result}` : result
  }



  /**
   * 解析格式化后的字符串为纯数字字符串
   * @param {string} value - 格式化后的字符串
   * @returns {string} 原始数字字符串
   */
  const inputParser = (value) => {
    // 移除所有非数字字符（保留负号、小数点）
    return value.replace(/[^0-9.-]/g, '')
      // 处理多个小数点
      .replace(/(\..*)\./g, '$1')
      // 处理开头的多余负号
      .replace(/(?!^-)-/g, '')
  }






  /**
     * 自定义单元格下拉框渲染方法
     * @param params
     * @param cmbSource
     * @param pCodeKey
     * @param toUpperCase
     * @returns {*}
     */
    function cmbShowRender(sKey, cmbSource, pCodeKey, toUpperCase) {
        let cmbVal = sKey
        if (isNullOrEmpty(cmbVal)) {
            cmbVal = ''
        }else if (!isNullOrEmpty(pCodeKey)) {
          if (toUpperCase === true) {
            let dict =  xdoCache.getSync(pCodeKey).get(cmbVal.toUpperCase())
            if (!isNullOrEmpty(dict) && dict.hasOwnProperty(sKey)) {
              cmbVal = sKey + ' ' + val
            }
          } else {
            let dict = xdoCache.getSync(pCodeKey)
            if (!isNullOrEmpty(dict) && dict.hasOwnProperty(sKey)) {
              cmbVal =  sKey + ' ' + dict[sKey]
            }

          }
        }else {
            cmbVal = getKeyValue(cmbSource, cmbVal)
        }
        return cmbVal
    }


    /**
     * 自定义key-value列展示规则
     * @param h
     * @param params
     * @param key
     * @param value
     * @returns {*}
     */
    function keyValueRender(h, params, key, value) {
        let keyVal = params.row[key]
        let valueVal = params.row[value]
        let showVal = ''
        if (!isNullOrEmpty(keyVal)) {
            showVal = keyVal
        }
        if (!isNullOrEmpty(valueVal)) {
            showVal += ' ' + valueVal
        }
        return h('span', showVal.trim())
    }


    /**
     * 根据数据源及key值获取【key value】
     * @param source
     * @param key
     * @param full
     * @returns {*}
     */
    function getKeyValue(source, key, full) {
        if (full !== false) {
            full = true
        }
        if (!isNullOrEmpty(key)) {
            if (Array.isArray(source) && source.length > 0) {
                let theArr = source.filter(item => {
                    return item.value === key
                })
                if (Array.isArray(theArr) && theArr.length > 0) {
                    if (full) {
                        return key + ' ' + theArr[0].label
                    } else {
                        return theArr[0].label
                    }
                } else {
                    return key
                }
            } else if (typeof source === 'object') {
                if (source.hasOwnProperty(key)) {
                    if (full) {
                        return key + ' ' + source[key]
                    } else {
                        return source[key]
                    }
                } else {
                    return key
                }
            } else if (typeof source === 'string') {
                if (!isNullOrEmpty(source)) {
                    if (full) {
                        return key + ' ' + source
                    } else {
                        return source
                    }
                } else {
                    return key
                }
            } else {
                return key
            }
        }
        return ''
    }

    return {
        cmbShowRender,
        toFixedTwo,
        formatNumber,
        inputFormatter,
        inputParser,
        formatSpecifiedNumber,
        formatNumberNew
    }
}
