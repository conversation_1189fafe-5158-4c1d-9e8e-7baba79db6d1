import { ref } from 'vue'
import ycCs<PERSON>pi from "@/api/ycCsApi"
import { message } from 'ant-design-vue'

export function useMerchant() {
  const merchantOptions = ref([])

  const getMerchantOptions = async () => {
    try {
      const params = {}
      const res = await window.majesty.httpUtil.postAction(
        `${ycCsApi.bizMerchant.list}`,
        params
      );
      if (res.code === 200) {
        merchantOptions.value = res.data.map(item => ({
          value: item.merchantCode,
          label: item.merchantNameCn
        }));
      } else {
        message.error(res.message || '获取客商数据失败');
      }
    } catch (error) {
      message.error('获取客商数据失败');
    }
  }

  const productTypeOptions = ref([])

  const getProductTypeOptions = async () => {
    try {
      const params = {}
      const res = await window.majesty.httpUtil.postAction(
        `${ycCsApi.params.productType.list}`,
        params
      );
      if (res.code === 200) {
        productTypeOptions.value = res.data.map(item => ({
          value: item.categoryCode,
          label: item.categoryName
        }));
      } else {
        message.error(res.message || '获取商品类别失败');
      }
    } catch (error) {
      message.error('获取商品类别失败');
    }
  }

  //海关参数币制
  const currencyOptions = ref([]);

  const getCurrency = async (type) => {
    const params = {
      paramsType: 'CURR',
    }
    try {
      const res = await window.majesty.httpUtil.postAction(
        `${ycCsApi.importedCigarettes.contract.customsList}/${type}`,
        params
      );
      if (res.code === 200) {
        // 将接口返回的数据添加到数组中
        currencyOptions.value = res.data.map(item => ({
          value: item.key,
          label: item.value
        }));
      } else {
        message.error(res.message || '获取币制数据失败');
      }
    } catch (error) {
      message.error('获取币制数据失败');
    }
  }

  // 获取单位键值对列表
  const unitList = ref([])

  const getUnitList = async () => {
    let params = {}
    const res = await window.majesty.httpUtil.postAction( ycCsApi.bizSmokeMachineInComingList.getCommonKeyValueList,params);
    // console.log('res', res);
    if (res.code === 200) {
      unitList.value =  res.data.unitList
    }else {
      message.error(res.message)
    }
  }

  const packageInfoMap = ref([]);
  const getPackageInfoMap = async () => {
    try {
      const params = {}
      const res = await window.majesty.httpUtil.postAction(
        `${ycCsApi.params.packageInfo.listAll}`, params
      );
      if (res.code === 200) {
        // 将接口返回的数据添加到数组中
        res.data.forEach(item => {
          packageInfoMap.value.push({
            value: item.paramCode,
            label: item.packUnitCnName
          });
        });
      } else {
        message.error(res.message || '获取包装信息数据失败');
      }
    } catch (error) {
      message.error('获取包装信息数据失败');
    }
  }

  return {
    merchantOptions,
    getMerchantOptions,
    productTypeOptions,
    getProductTypeOptions,
    currencyOptions,
    getCurrency,
    unitList,
    getUnitList,
    packageInfoMap,
    getPackageInfoMap,
  }
}
