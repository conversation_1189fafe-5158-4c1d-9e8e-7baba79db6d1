<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <BizDeliveryOrderHeadListSearch ref="headSearch"></BizDeliveryOrderHeadListSearch>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:add']">
          <a-button size="small" @click="handlerAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{localeContent('m.common.button.add')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:update']">
          <a-button size="small" @click="handlerEdit">
            <template #icon>
              <GlobalIcon type="form" style="color:orange"/>
            </template>
            {{localeContent('m.common.button.update')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:delete']">
          <a-button size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{localeContent('m.common.button.delete')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:export']">
          <a-button size="small" :loading="exportLoading" @click="handlerExport">
            <template #icon>
              <GlobalIcon type="folder-open" style="color:orange"/>
            </template>
            {{localeContent('m.common.button.export')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:confirm']">
          <a-button size="small" :loading="confirmLoading" @click="handlerConfirm">
            <template #icon>
              <GlobalIcon type="check" style="color:green"/>
            </template>
            确认
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:back']">
          <a-button size="small" :loading="backLoading" @click="handlerBack">
            <template #icon>
              <GlobalIcon type="swap-left" style="color:red"/>
            </template>
            退单
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:invalidate']">
          <a-button size="small" :loading="invalidLoading" @click="handlerInvalid">
            <template #icon>
              <GlobalIcon type="close-square" style="color:red"/>
            </template>
            作废
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:sendEntry']">
          <a-button size="small" :loading="sendEntryLoading" @click="handlerSendEntry">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue"/>
            </template>
            发送报关
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:printLink']">
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handlePrintLink">
                <a-menu-item key=".pdf">打印PDF</a-menu-item>
                <a-menu-item key=".xlsx">打印EXCEL</a-menu-item>
              </a-menu>
            </template>
            <a-button size="small" type="ghost" :loading="printReceiptLoading">
              <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
              <DownOutlined />
              打印出货单
            </a-button>
          </a-dropdown>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:printIns']">
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handlePrintIns">
                <a-menu-item key=".pdf">打印PDF</a-menu-item>
                <a-menu-item key=".xlsx">打印EXCEL</a-menu-item>
              </a-menu>
            </template>
            <a-button size="small" type="ghost" :loading="printReceiptLoading">
              <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
              <DownOutlined />
              打印投保单
            </a-button>
          </a-dropdown>
        </div>
        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
              :resId="tableKey"
              :tableKey="tableKey+'-contract'"
              :initSettingColumns="originalColumns"
              :showColumnSettings="true"
              @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
            :animate-rows="false"
            ref="tableRef"
            class="cs-action-item remove-table-border-add-bg"
            size="small"
            :scroll="{ y: tableHeight,x:400 }"
            column-drag
            bordered
            :pagination="false"
            :columns="showColumns.length > 0 ?showColumns:totalColumns"
            :data-source="dataSourceList"
            :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
            :loading="tableLoading"
            row-key="sid"
            :custom-row="customRow"
            :row-height="30"
            :range-selection="false"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                    size="small"
                    type="link"
                    @click="handleEditByRow(record)"
                    :style="operationEdit('edit')"
                >
                  <template #icon>
                    <GlobalIcon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                    size="small"
                    type="link"
                    @click="handleViewByRow(record)"
                    :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>
              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>
    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <BizDeliveryOrderHeadTabs  @onEditBack="handlerOnBack" :editConfig="editConfig"></BizDeliveryOrderHeadTabs>
    </div>

    <!-- 新增弹框 -->
    <cs-modal :visible="showAddModal" :title="'新增'" :width="1200" :footer="true" @cancel="handleCloseModal">
      <template #customContent>
        <DeliveryOrderExtractHead ref="extractRef"></DeliveryOrderExtractHead>
      </template>
      <template #footer>
        <div style="display: flex;justify-content: right;align-items: center">
          <a-button @click="handleCloseModal" size="small">返回</a-button>
          <a-button
            style="margin-left: 8px"
            size="small"
            type="primary"
            @click="extractData"
            :loading="saveLoading"
          >保存</a-button>
        </div>
      </template>
    </cs-modal>

  </section>
</template>
<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon';
import {createVNode, onMounted, reactive, ref, watch} from "vue";
import {getColumns} from './bizDeliveryOrderHeadColumns';
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
const { totalColumns } = getColumns();
import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute, useRouter} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
import BizDeliveryOrderHeadListSearch from  './BizDeliveryOrderHeadListSearch';
import BizDeliveryOrderHeadTabs from  './BizDeliveryOrderHeadTabs'
import { useFieldMarking } from '@/utils/useFieldMarking';
import {backDeliveryOrderHead, confirmDeliveryOrderHead, generateDeliveryOrderHead, invalidateDeliveryOrderHead} from "@/api/cs_api_constant";
import DeliveryOrderExtractHead from './DeliveryOrderExtractHead';
import CsModal from "@/components/modal/cs-modal.vue";
/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData,
  onSelectChange
} = useCommon()
/* 引入字段标记功能 */
const { getBySidAndFormType } = useFieldMarking()
defineOptions({
  name: 'BizDeliveryOrderHeadList',
});
const router = useRouter();
const planSelectVisible = ref(false);
onMounted(fn => {
  getPortList()
  ajaxUrl.selectAllPage = ycCsApi.deliveryOrder.bizDeliveryOrderHead.list
  ajaxUrl.exportUrl = ycCsApi.deliveryOrder.bizDeliveryOrderHead.export
  tableHeight.value = getTableScroll(100,'');
  getList()
  initCustomColumn()
})
const handleCloseModal = () => {
  showAddModal.value = false
  confirmLoading.value = false
}
const tableHeight = ref('')
/* 按钮loading */
const deleteLoading = ref(false)
const sendEntryLoading = ref(false)
const saveLoading = ref(false)
const confirmLoading = ref(false)
const auditLoading = ref(false)
const printReceiptLoading = ref(false)
const backLoading = ref(false)
const invalidLoading = ref(false)
const showAddModal = ref(false)
const portList = ref([])
const extractRef = ref(null)
const extractData = async ()=> {
  console.log('extractContractData',extractRef.value.gridData.selectedRowKeys)
  if (extractRef.value.gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }

  const ids = [...new Set(extractRef.value.gridData.selectedData.map(item => item.tid))];
  console.log('ids',ids)
  if(ids.length > 1){
    message.warning('请选择同一合同号的数据')
    return
  }
  // if (extractRef.value.gridData.selectedRowKeys.length > 1){
  //   message.warning('只能选择一条数据')
  //   return
  // }
  saveLoading.value = true
  // 提取数据
  const res =  await generateDeliveryOrderHead({sid:extractRef.value.gridData.selectedData[0].tid});
  try {
    if (res.code === 200){
      message.success(res.message)
      saveLoading.value = false
      showAddModal.value = false
      getList()
      // console.log('dataSourceList.value',dataSourceList.value)
      editConfig.value.editStatus = editStatus.EDIT
      editConfig.value.editData =  res.data
      show.value =!show.value
    }else{
      message.error(res.message)
      saveLoading.value = false
    }
  }catch (err) {
    saveLoading.value = false
  }
}

const getPortList = async () => {
  const res = await window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.getPortList, {})
  if (res.code === 200) {
    portList.value = res.data
  }
}
/* 返回事件 */
const handlerOnBack = (flag) => {
  console.log('返回',flag)
  show.value = !show.value;
  // 返回清空选择数据
  if (flag){
    getList()
  }
}
/* 发送报关  */
const handlerSendEntry = () => {
  // 1）只可选择一行数据操作，如选择多行，则提示用户只可操作一行数据
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只可操作一行数据')
    return
  }

  // 2）单据状态为"确认"数据可以操作发送，如不符合，则提示用户"数据条件不符，请重新选择"
  if (gridData.selectedData[0].status !== '1') {
    message.warning('数据条件不符，请重新选择')
    return
  }

  // 获取选中数据的发送报关字段值
  const selectedRecord = gridData.selectedData[0]
  const sendEntryValue = selectedRecord.sendDeclare
  let messageContent = ''
  if (sendEntryValue === '1') {
    messageContent = '是否将数据发送关务系统？'
  } else {
    messageContent = '是否将数据发送关务系统？'
  }
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: messageContent,
    onOk() {
      sendEntryLoading.value = true
      let params = {
        sid: selectedRecord.sid
      }
      window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderHead.sendEntry, params).then(res => {
        debugger
        if (res.success === true) {
          message.success("发送成功！")
          // 发送成功后刷新列表数据
          getList()
        } else {
          message.error(res.data)
        }
      }).finally(() => {
        sendEntryLoading.value = false
      })
    },
    onCancel() {
      // 点击取消则关闭系统弹框，不执行发送及不修改相关栏位值
    },
  })
}
/* 作废事件 */
const handlerInvalid = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '作废',
    cancelText: '取消',
    content: '确认作废所选项吗？',
    onOk() {
      invalidLoading.value = true
      // 这里需要调用作废API
      const params = {
        sid : gridData.selectedRowKeys[0]
      }
      invalidateDeliveryOrderHead(params.sid).then(res => {
        if (res.success) {
          message.success("作废成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        invalidLoading.value = false
      })

    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 退单事件 */
const handlerBack = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '确认退单所选项吗？',
    onOk() {
      backLoading.value = true
      // 这里需要调用确认API
      const params = {
        sid : gridData.selectedRowKeys[0]
      }
      backDeliveryOrderHead(params.sid).then(res => {
        if (res.success) {
          message.success("退单成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        backLoading.value = false
      })
    },
    onCancel() {
      // 取消操作
    },
  });
}
const handlePrintIns = (e) => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  const printType = e.key; // 'pdf' or 'xlsx'
  handleSaveAndPrintIns(printType);
}
const handleSaveAndPrintIns = async (fileType) => {
  printReceiptLoading.value = true;
  const params = {sid:gridData.selectedRowKeys[0],fileType:fileType}
  const url = `${ycCsApi.deliveryOrder.bizDeliveryOrderHead.printIns}`;
  window.majesty.httpUtil.downloadFile(
    url, null, params, 'post', null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
    message.error(`打印${fileType === 'xlsx' ? 'XLSX' : 'PDF'}失败`);
  }).finally(() => {
    printReceiptLoading.value = false;
  })
};
const handlePrintLink = (e) => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  const printType = e.key; // 'pdf' or 'xlsx'
  handleSaveAndPrintLink(printType);
}
const handleSaveAndPrintLink = async (fileType) => {
  printReceiptLoading.value = true;
  const params = {sid:gridData.selectedRowKeys[0],fileType:fileType}
  const url = `${ycCsApi.deliveryOrder.bizDeliveryOrderHead.printLink}`;
  window.majesty.httpUtil.downloadFile(
    url, null, params, 'post', null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
    message.error(`打印${fileType === 'xlsx' ? 'XLSX' : 'PDF'}失败`);
  }).finally(() => {
    printReceiptLoading.value = false;
  })
};

const handlerConfirm = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '确认执行此操作吗？',
    onOk() {
      confirmLoading.value = true
      // 这里需要调用确认API
      const params = {
        sid : gridData.selectedRowKeys[0]
      }
      confirmDeliveryOrderHead(params).then(res => {
        if (res.success) {
          message.success("确认成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        confirmLoading.value = false
      })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 新增数据 */
const handlerAdd = () => {
  showAddModal.value = true;
}
const handleEditByRow = (row) => {
  // 在这里添加处理编辑行的逻辑
  // if (row.dataStatus !== '0'){
  //   message.warning('仅0编制的数据可以操作编辑')
  //   return
  // }
  show.value = !show.value
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = row
}
/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // if (gridData.selectedData[0].dataStatus !== '0'){
  //   message.warning('仅0编制的数据可以操作编辑')
  //   return
  // }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]
  console.log('editConfig.value.editData', gridData)
  show.value =!show.value;
}
/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('请选择一条数据')
    return
  }
  if(gridData.selectedData[0].status !== '0'){
    message.warning('仅编制状态数据允许删除!');
    return;
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      window.majesty.httpUtil.deleteAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderHead.delete}/${gridData.selectedRowKeys}`).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {
    },
  });
}
/* 导出事件 */
const handlerExport = () =>{
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = ``
  doExport(`国营贸易进口卷烟-进口合同.xlsx`, totalColumns)
}
/* 自定义设置 */
const showColumns =  ref([])
/* 唯一键 */
const tableKey = ref('')
console.log('window.majesty.router',window.majesty.router.patch)
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()
/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}
/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
}
/* 双击行进入编辑页面 */
const handleRowDblclick = (record) => {
  handleViewByRow(record)
};
// 自定义行属性
const customRow = (record) => {
  return {
    onDblclick: () => {
      handleRowDblclick(record);
    }, style: {cursor: 'pointer'}
  };
};
/* 监控 dataSourceList */
// watch(dataSourceList, (newValue, oldValue) => {
//   showColumns.value = [...totalColumns.value];
//   // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
// },{deep:true})
watch(totalColumns.value, (newValue, oldValue) => {
  if(!window.$vueApp){
    showColumns.value = [...totalColumns.value];
  }else {
    if (newValue.length === 0) {
      showColumns.value = [...totalColumns.value];
    }else {
      showColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
      totalColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
    }
  }
},{immediate:true,deep:true})
/* 处理计划选择 */
const handlePlanSelect = (plan) => {
  editConfig.value.editStatus = editStatus.ADD;
  editConfig.value.editData = plan;
  //标识新增
  // editConfig.value.flag = editStatus.ADD;
  show.value = !show.value;
};
</script>
<style lang="less" scoped>
</style>
