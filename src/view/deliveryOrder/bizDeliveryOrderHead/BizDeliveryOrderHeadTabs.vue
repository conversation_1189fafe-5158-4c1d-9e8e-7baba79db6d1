<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="表头表体" >
          <BizDeliveryOrderHeadEdit  ref="headTab" :head-id="headId" :edit-config="editConfig"  @onEditBack="editBack"></BizDeliveryOrderHeadEdit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="ship" tab="装运信息" @onEditBack="editBack" >
          <BizDeliveryOrderShipmentsEdit  ref="shipBody" :head-id="headId"  :edit-config="editConfig"  @onEditBack="editBack"></BizDeliveryOrderShipmentsEdit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="cert" tab="证件信息" @onEditBack="editBack" >
          <BizDeliveryOrderCertEdit  ref="headTab" :head-id="headId" :edit-config="editConfig"  @onEditBack="editBack"></BizDeliveryOrderCertEdit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="info" tab="投保信息" @onEditBack="editBack" >
          <BizDeliveryOrderInsuranceInfoEdit  ref="InsuranceTab" :head-id="headId" :edit-config="editConfig"  @onEditBack="editBack"></BizDeliveryOrderInsuranceInfoEdit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="attach" tab="归档附件" @onEditBack="editBack" >
          <biz-i-attach :head-id="headId"  :operation-status="editConfig.editStatus"></biz-i-attach>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="aeo" tab="审批记录" @onEditBack="editBack" >
          <!-- 确保子组件重新挂载 -->
          <cs-aeo-info-list :sid="headId"></cs-aeo-info-list>
        </a-tab-pane>
        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000"/>
          </div>
        </template>
      </a-tabs>
    </div>
  </section>
</template>
<script setup>
import {onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import BizDeliveryOrderHeadEdit from "./BizDeliveryOrderHeadEdit.vue";
import BizIAttach from "./DeliveryOrderAttach";
import BizDeliveryOrderShipmentsEdit from "../bizDeliveryOrderShipments/BizDeliveryOrderShipmentsEdit";
import BizDeliveryOrderInsuranceInfoEdit from "../bizDeliveryOrderInsuranceInfo/BizDeliveryOrderInsuranceInfoEdit";
import BizDeliveryOrderCertEdit from "../bizDeliveryOrderCert/BizDeliveryOrderCertEdit";
import CsAeoInfoList from '@/components/aeo/CsAeoInfoList.vue'
defineOptions({
  name:'BizDeliveryOrderHeadTabs'
})
const emit = defineEmits(['onEditBack'])
/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}
/* 激活Tab key */
const tabName = ref('headTab');
/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  shipFrom:true,
})
/* 表头headId */
const headId = ref('')
/* 是否显示子模块 tab */
const showBody = ref(false)
/* 返回tab界面 */
const editBack = (val) => {
  console.log('val', val)
  console.log('val', val.editStatus)
  if (val.editStatus === editStatus.EDIT){
    // showBody.value = val.showBody
    headId.value = val.editData.sid
    props.editConfig.editStatus = val.editStatus
    props.editConfig.editData = val.editData
    console.log('val.editData', val.editData)
    if(val.editData != null && val.editData.isSave === '1'){
      showBody.value = true
    }
  }else {
    if (val) {
      emit('onEditBack', val)
    }
  }
}
/* 初始化操作 */
onMounted(()=>{
  console.log('props.editConfig', props.editConfig)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    headId.value = props.editConfig.editData.sid
    // showBody.value = true;
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    headId.value = props.editConfig.editData.sid
    console.log('headId.value', headId.value)
    if(props.editConfig.editData.isSave === '1'){
      showBody.value = true
    }
  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    headId.value = props.editConfig.editData.sid
    if(props.editConfig.editData.isSave === '1'){
      showBody.value = true
    }
  }
})
/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true
})
</script>
<style lang="less" scoped>
</style>
