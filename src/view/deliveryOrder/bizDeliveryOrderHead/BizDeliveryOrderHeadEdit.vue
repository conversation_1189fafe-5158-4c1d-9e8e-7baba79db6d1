<template>
  <section>
    <a-card size="small" title="出货单表头" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType2"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="purchaseOrderNo" :label="'出货单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.purchaseOrderNo"></a-input>
          </a-form-item>
          <a-form-item name="supplier" :label="'供应商'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.supplier" id="supplier">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.customer" id="customer">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="customerAddress" :label="'客户地址'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.customerAddress"></a-input>
          </a-form-item>
<!--          <a-form-item name="tradeCountry" :label="'贸易国别'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.tradeCountry"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="tradeCountry" :label="'贸易国别'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' " optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.tradeCountry" id="tradeCountry">
              <a-select-option v-for="item in countryOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          <a-form-item name="businessEnterprise" :label="'经营单位'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.businessEnterprise"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="businessEnterprise" :label="'经营单位'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' "  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessEnterprise" id="businessEnterprise">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          <a-form-item name="portOfDestination" :label="'目的地/港'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.portOfDestination"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="portOfDestination" :label="'目的地/港'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' " optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.portOfDestination" id="portOfDestination">
              <a-select-option v-for="item in portList" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          <a-form-item name="paymentMethod" :label="'付款方式'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.paymentMethod"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="paymentMethod" :label="'付款方式'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' " optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.paymentMethod" id="paymentMethod">
              <a-select-option v-for="item in productClassify.paymentMethodMap" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.curr"
              :disabled="showDisable || formData.status  !== '0' "
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
            ></a-select>
          </a-form-item>
<!--          <a-form-item name="totalAmount" :label="'总金额'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="true" size="small" v-model:value="formData.totalAmount" style="width: 100%"/>-->
<!--          </a-form-item>-->
<!--          <a-form-item name="transportMode" :label="'运输方式'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.transportMode"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="transportMode" :label="'运输方式'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' " optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.transportMode" id="transportMode">
              <a-select-option v-for="item in productClassify.transportMode" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="priceTerm" :label="'价格条款'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' "  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.priceTerm" id="priceTerm">
              <a-select-option class="cs-select-dropdown" v-for="item in priceTermList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="priceTermPort" :label="'价格条款对应港口'" class="grid-item" :colon="false">
            <cs-select :disabled="true" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.priceTermPort" id="priceTermPort">
              <a-select-option v-for="item in priceTermPortOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="deliveryEnterprise" :label="'发货单位'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' "  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.deliveryEnterprise" id="deliveryEnterprise">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          <a-form-item name="deliveryEnterprise" :label="'发货单位'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.deliveryEnterprise"></a-input>-->
<!--          </a-form-item>-->
<!--          <a-form-item name="wrapType" :label="'包装种类'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.wrapType"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="wrapType" :label="'包装种类'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' " optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.wrapType" id="wrapType">
              <a-select-option v-for="item in wrapMap" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="packNum" :label="'包装数量'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable || formData.status  !== '0' " size="small"
                            :formatter="value => {
                              if (!value) return '';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.packNum" notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
<!--          <a-form-item name="packNum" :label="'包装数量'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.packNum" style="width: 100%"/>-->
<!--          </a-form-item>-->
          <a-form-item name="deliveryEnterpriseAddress" :label="'发货单位所在地'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.deliveryEnterpriseAddress"></a-input>
          </a-form-item>
          <a-form-item name="totalGrossWt" :label="'总毛重'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.totalGrossWt" notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
          <a-form-item name="totalNetWt" :label="'总净重'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.totalNetWt" notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
          <a-form-item name="totalTare" :label="'总皮重'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.totalTare" notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>

<!--          <a-form-item name="totalNetWt" :label="'总毛重'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="true" size="small" v-model:value="formData.totalNetWt" style="width: 100%"/>-->
<!--          </a-form-item>-->
<!--          <a-form-item name="totalGrossWt" :label="'总净重'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="true" size="small" v-model:value="formData.totalGrossWt" style="width: 100%"/>-->
<!--          </a-form-item>-->
<!--          <a-form-item name="totalTare" :label="'总皮重'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="true" size="small" v-model:value="formData.totalTare" style="width: 100%"/>-->
<!--          </a-form-item>-->
          <a-form-item name="sendDeclare" :label="'发送财务系统'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendDeclare" id="sendDeclare">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="createrUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createrUserName"/>
          </a-form-item>
          <!--          制单时间-->
          <a-form-item name="createrTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createrTime"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="status" :label="'单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="apprStatus" :label="'审核状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.apprStatus" id="apprStatus">
              <a-select-option v-for="item in productClassify.approval_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW && formData.status === '0'">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>

    <a-tabs v-model:activeKey="headInnerTabKey" v-if="formData.sid" >
      <a-tab-pane key="1" tab="表体子表">
<!--        <a-card size="small" title="表体列表" class="cs-card-form">-->
          <BizDeliveryOrderListList  ref="listRef" :head-id="formData.sid" :operation-status="props.editConfig.editStatus" :is-confirm="formData.isConfirm" :totalAmount="formData.totalAmount" @listBack="listBack"></BizDeliveryOrderListList>
<!--        </a-card>-->
      </a-tab-pane>
      <a-tab-pane key="2" tab="装箱子表">
<!--        <a-card size="small" title="装箱表体列表" class="cs-card-form">-->
          <BizDeliveryOrderContainerListList ref="clistRef" :head-id="formData.sid"  :operation-status="props.editConfig.editStatus" :is-confirm="formData.isConfirm" @listBack="listBack"></BizDeliveryOrderContainerListList>
<!--        </a-card>-->
      </a-tab-pane>
    </a-tabs>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import BizDeliveryOrderListList from '../bizDeliveryOrderList/BizDeliveryOrderListList';
import BizDeliveryOrderContainerListList from '../bizDeliveryOrderContainerList/BizDeliveryOrderContainerListList';
import {editStatus, productClassify} from '@/view/common/constant';
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import {useIncomingCommon} from "@/view/dec/incoming/common/IncomingCommon";
import {getDeliveryOrderSid, getOrderSupplierList} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import useEventBus from "@/view/common/eventBus";
const { getPCode } = usePCode()
const { inputFormatter,inputParser,formatNumber,formatSpecifiedNumber}  = useColumnsRender()
const {  portList,priceTermList } = useIncomingCommon({ immediate: true });
const priceTermPortOptions = reactive([
  {
    label:'起运港',
    value:'0'
  },
  {
    label:'目的港',
    value:'1'
  },
])
const headInnerTabKey = ref('1')
const currMap = ref([])
const wrapMap = ref([])
const supplierList = ref([])
const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
const countryOptions = reactive([]);
const listBack = (val) => {
  console.log('ediTval', val)
  if (val){
    initEdit();
  }
}
const initEdit = async () => {
  const res = await getDeliveryOrderSid({'sid':props.headId})
  if (res.code === 200){
    if(null !== res.data){
      Object.assign(formData, res.data);
    }else {
      Object.assign(formData, {});
    }
  }else {
    message.error(res.message)
  }
}
const getCountryOptions = async () => {
  const params = {
    paramsType: 'COUNTRY',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/1`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        countryOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取国家数据失败');
    }
  } catch (error) {
    message.error('获取国家数据失败');
  }
}
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'sid':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'contractNo':'合同号'
, 'purchaseOrderNo':'出货单号'
, 'supplier':'供应商'
, 'customer':'客户'
, 'customerAddress':'客户地址'
, 'tradeCountry':'贸易国别'
, 'businessEnterprise':'经营单位'
, 'portOfDestination':'目的地/港'
, 'paymentMethod':'付款方式'
, 'curr':'币种'
, 'totalAmount':'总金额'
, 'transportMode':'运输方式'
, 'priceTerm':'价格条款'
, 'priceTermPort':'价格条款对应港口'
, 'deliveryEnterprise':'发货单位'
, 'wrapType':'包装种类'
, 'packNum':'包装数量'
, 'deliveryEnterpriseAddress':'发货单位所在地'
, 'totalNetWt':'总毛重'
, 'totalGrossWt':'总净重'
, 'totalTare':'总皮重'
, 'sendDeclare':'发送报关'
, 'businessDate':'业务日期'
, 'confirmTime':'确认时间'
, 'isConfirm':'是否确认'
, 'isSave':'是否保存'
, 'note':'备注'
, 'status':'单据状态'
, 'apprStatus':'审核状态'
, 'sendFinance':'发送财务系统'
, 'redFlush':'是否红冲'
, 'purchaseMark':'外商合同、进货明细数据标记'
, 'purchaseNoMark':'外商合同、进货明细数据标记'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId:{
    type:String,
    default:()=>''
  },
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid: ''
,businessType: ''
,contractNo: ''
,purchaseOrderNo: ''
,supplier: ''
,customer: ''
,customerAddress: ''
,tradeCountry: ''
,businessEnterprise: ''
,portOfDestination: ''
,paymentMethod: ''
,curr: ''
,totalAmount: ''
,transportMode: ''
,priceTerm: ''
,priceTermPort: ''
,deliveryEnterprise: ''
,wrapType: ''
,packNum: ''
,deliveryEnterpriseAddress: ''
,totalNetWt: ''
,totalGrossWt: ''
,totalTare: ''
,sendDeclare: ''
,confirmTime: ''
,status: ''
})
// 表单校验规则
const rules = {
businessType: [
  { required: true, message: '业务类型不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
contractNo: [
  { required: true, message: '合同号不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
purchaseOrderNo: [
  { required: true, message: '出货单号不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
supplier: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
customer: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
customerAddress: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
tradeCountry: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
businessEnterprise: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
portOfDestination: [
{ max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
],
paymentMethod: [
{ max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
],
curr: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
totalAmount: [
],
transportMode: [
{ max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
],
priceTerm: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
priceTermPort: [
{ max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
],
deliveryEnterprise: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
wrapType: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
packNum: [
],
deliveryEnterpriseAddress: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
totalNetWt: [
],
totalGrossWt: [
],
totalTare: [
],
sendDeclare: [
  { required: true, message: '合发送报关不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
confirmTime: [
],
status: [
  { required: true, message: '单据状态不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
  createrUserName: [
    {required: true, message: '制单人不能为空！', trigger: 'blur'}
  ],
  createrTime: [
    {required: true, message: '制单时间不能为空！', trigger: 'blur'}
  ]
}
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      window.majesty.httpUtil.putAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderHead.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      window.majesty.httpUtil.putAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderHead.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
          Object.assign(formData, res.data)
          formData.createrUserName = res.data.updateUserName
          formData.createrTime = res.data.updateTime
          onBack({
            editData: res.data,
            showBody: true,
            editStatus: editStatus.EDIT
          })
          emitEvent('refreshOrderList')
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
const {emitEvent} = useEventBus()
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getCountryOptions();
  getSupplierList();
  getPackageInfoMap();
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      value
    }));
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})
const getPackageInfoMap = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.packageInfo.listAll}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        wrapMap.value.push({
          value: item.paramCode,
          label: item.packUnitCnName
        });
      });
    } else {
      message.error(res.message || '获取包装信息数据失败');
    }
  } catch (error) {
    message.error('获取包装信息数据失败');
  }
}
</script>
<style lang="less" scoped>
</style>
