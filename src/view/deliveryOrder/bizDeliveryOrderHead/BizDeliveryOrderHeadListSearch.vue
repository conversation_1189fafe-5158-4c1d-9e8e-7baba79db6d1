<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <a-form-item name="status"   :label="'单据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.status" id="status">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.contractNo" xid="s_contractNo"></a-input>
    </a-form-item>
    <a-form-item name="purchaseOrderNo"   :label="'出货单号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.purchaseOrderNo" xid="s_purchaseOrderNo"></a-input>
    </a-form-item>
    <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
      <cs-select  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.customer" id="customer">
        <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
          {{item.value}} {{ item.label  }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <a-form-item name="portOfDestination" :label="'目的地/港'" class="grid-item" :colon="false">
      <cs-select  optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="searchParam.portOfDestination" id="portOfDestination">
        <a-select-option v-for="item in portList" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + item.label">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <a-form-item name="destinationDate" label="装运期限起止" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.destinationDateFrom"
              id="destinationDateFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.destinationDateTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>

    </a-form-item>
    <a-form-item name="createBy" :label="'制单人'" class="grid-item" :colon="false">
      <cs-select  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.createBy" id="createBy">
        <a-select-option class="cs-select-dropdown" v-for="item in createByList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
          {{item.value}} {{ item.label  }}
        </a-select-option>
      </cs-select>
    </a-form-item>
<!--    <a-form-item name="status"   :label="'单据状态'" class="grid-item"  :colon="false">-->
<!--      <a-input size="small" v-model:value="searchParam.status" xid="s_status"></a-input>-->
<!--    </a-form-item>-->
    <a-form-item name="insertTime" label="制单日期起止" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeFrom"
              id="insertTimeFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>

    </a-form-item>
  </a-form>
</template>
<script setup>
import {inject, onMounted, reactive, ref} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import ycCsApi from "@/api/ycCsApi";
import {message} from "ant-design-vue";
import {getCreateUserListS, getDeliveryOrderHeadCreateUserList, getOrderSupplierList} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
defineOptions({
  name: 'BizDeliveryOrderHeadListSearch'
})
const searchParam = reactive({
    contractNo: '',
    purchaseOrderNo: '',
    portOfDestination: '',
    status: '',
  destinationDateFrom:'',
  destinationDateTo:'',
  insertTimeFrom:'',
  insertTimeTo:'',
  createBy:'',
})
const supplierList = ref([])
const createByList = ref([])
const portList = ref([])
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
  onMounted(() => {
    getPortList();
    getCreateByList();
    getSupplierList();
  })
  defineExpose({
    searchParam,
    resetSearch
  })
const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
const getPortList = async () => {
  const res = await window.majesty.httpUtil.postAction(ycCsApi.bizSmokeMachineInComingHead.getPortList, {})
  if (res.code === 200) {
    portList.value = res.data
  }
}
const getCreateByList  = () =>{
  getDeliveryOrderHeadCreateUserList({}).then(res=>{
    if (!isNullOrEmpty(res.data)){
      createByList.value = res.data
    }
  })
}
</script>
<style scoped>
</style>
