import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }
  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};
const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()
// 初始化时获取数据
await getMerchantOptions()
function getColumns() {
  const commColumns = reactive([
    'businessType'
    , 'sid'
    , 'createBy'
    , 'createTime'
    , 'createUserName'
    , 'updateBy'
    , 'updateTime'
    , 'updateUserName'
    , 'tradeCode'
    , 'sysOrgCode'
    , 'extend1'
    , 'extend2'
    , 'extend3'
    , 'extend4'
    , 'extend5'
    , 'extend6'
    , 'extend7'
    , 'extend8'
    , 'extend9'
    , 'extend10'
    , 'businessType'
    , 'purchaseOrderNo'
    , 'insuranceCompany'
    , 'insurant'
    , 'invoiceTitle'
    , 'trafName'
    , 'startShipmentDate'
    , 'trafRouteFrom'
    , 'trafRoutePass'
    , 'trafRouteTo'
    , 'insuranceType'
    , 'curr'
    , 'insuranceMarkup'
    , 'insuranceAmount'
    , 'insuranceRate'
    , 'insuranceFee'
    , 'insuranceDate'
    , 'freightFee'
    , 'freightCurr'
    , 'note'
    , 'headId'
    , 'analysisId'
  ])
  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])
  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])
  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '编号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'purchaseOrderNo',
      resizable: true,
      key: 'purchaseOrderNo',
    },
    {
      title: '保险公司',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insuranceCompany',
      resizable: true,
      key: 'insuranceCompany',
    },
    {
      title: '被保险人',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insurant',
      resizable: true,
      key: 'insurant',
    },
    {
      title: '发票抬头',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'invoiceTitle',
      resizable: true,
      key: 'invoiceTitle',
    },
    {
      title: '运输工具名称',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'trafName',
      resizable: true,
      key: 'trafName',
    },
    {
      title: '开航日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'startShipmentDate',
      resizable: true,
      key: 'startShipmentDate',
    },
    {
      title: '运输路线自',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'trafRouteFrom',
      resizable: true,
      key: 'trafRouteFrom',
    },
    {
      title: '经',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'trafRoutePass',
      resizable: true,
      key: 'trafRoutePass',
    },
    {
      title: '至',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'trafRouteTo',
      resizable: true,
      key: 'trafRouteTo',
    },
    {
      title: '投保险别',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insuranceType',
      resizable: true,
      key: 'insuranceType',
    },
    {
      title: '币种',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'curr',
      resizable: true,
      key: 'curr',
    },
    {
      title: '投保加成%',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insuranceMarkup',
      resizable: true,
      key: 'insuranceMarkup',
    },
    {
      title: '保险金额',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insuranceAmount',
      resizable: true,
      key: 'insuranceAmount',
    },
    {
      title: '保险费率',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insuranceRate',
      resizable: true,
      key: 'insuranceRate',
    },
    {
      title: '保费',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insuranceFee',
      resizable: true,
      key: 'insuranceFee',
    },
    {
      title: '投保日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'insuranceDate',
      resizable: true,
      key: 'insuranceDate',
    },
    {
      title: '投保日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'freightFee',
      resizable: true,
      key: 'freightFee',
    },
    {
      title: '投保日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'freightCurr',
      resizable: true,
      key: 'freightCurr',
    },
    {
      title: '备注',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },
  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
export { getColumns }
