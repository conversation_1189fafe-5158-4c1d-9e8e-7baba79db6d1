<template>
  <section>
    <a-card size="small" title="投保信息" class="cs-card-form">
      <a-spin :spinning="spinning"  >
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="purchaseOrderNo" :label="'编号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.purchaseOrderNo"></a-input>
          </a-form-item>
          <a-form-item name="insuranceCompany" :label="'保险公司'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.insuranceCompany" id="insuranceCompany">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="insurant" :label="'被保险人'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.insurant" id="insurant">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="invoiceTitle" :label="'发票抬头'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.invoiceTitle" id="invoiceTitle">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          <a-form-item name="insuranceCompany" :label="'保险公司'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.insuranceCompany"></a-input>-->
<!--          </a-form-item>-->
<!--          <a-form-item name="insurant" :label="'被保险人'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.insurant"></a-input>-->
<!--          </a-form-item>-->
<!--          <a-form-item name="invoiceTitle" :label="'发票抬头'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.invoiceTitle"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="trafName" :label="'运输工具名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.trafName"></a-input>
          </a-form-item>
          <a-form-item name="startShipmentDate" :label="'开航日期'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.startShipmentDate" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="trafRouteFrom" :label="'运输路线自'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.trafRouteFrom"></a-input>
          </a-form-item>
          <a-form-item name="trafRoutePass" :label="'经'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.trafRoutePass"></a-input>
          </a-form-item>
          <a-form-item name="trafRouteTo" :label="'至'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.trafRouteTo"></a-input>
          </a-form-item>
          <a-form-item name="insuranceType" :label="'投保险别'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.insuranceType"></a-input>
          </a-form-item>
<!--          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.curr"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.curr"
              :disabled="showDisable"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
              @change="currChange"
            ></a-select>
          </a-form-item>
<!--          <a-form-item name="insuranceMarkup" :label="'投保加成%'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceMarkup" @change="insuranceMarkupChange" style="width: 100%"/>-->
<!--          </a-form-item>-->
          <a-form-item name="insuranceMarkup" :label="'投保加成%'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.insuranceMarkup"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            @change="insuranceMarkupChange"
                            notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
          <a-form-item name="insuranceAmount" :label="'保险金额'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                              if (!value) return '';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="insuranceFeeChange"
                            v-model:value="formData.insuranceAmount" notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
<!--          <a-form-item name="insuranceAmount" :label="'保险金额'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceAmount" @change="insuranceFeeChange" style="width: 100%"/>-->
<!--          </a-form-item>-->
          <a-form-item name="insuranceRate" :label="'保险费率%'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.insuranceRate"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            @change="insuranceFeeChange"
                            notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
<!--          <a-form-item name="insuranceRate" :label="'保险费率'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceRate" @change="insuranceFeeChange" style="width: 100%"/>-->
<!--          </a-form-item>-->
<!--          <a-form-item name="insuranceFee" :label="'保费'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceFee" style="width: 100%"/>-->
<!--          </a-form-item>-->
          <a-form-item name="insuranceFee" :label="'保费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                              if (!value) return '';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.insuranceFee" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="insuranceDate" :label="'投保日期'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.insuranceDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>

<!--          <a-form-item name="freightCurr" :label="'运费币种'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.freightCurr"></a-input>-->
<!--          </a-form-item>-->
          <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"></a-input>
          </a-form-item>


        </a-form>
      </div>
      </a-spin>
    </a-card>
    <a-card size="small" title="运费信息" class="cs-card-form">
      <a-spin :spinning="spinning"  >
        <div class="cs-form">
          <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                  :model="formData" class="grid-container">
            <a-form-item name="freightFee" :label="'运费'" class="grid-item" :colon="false">
              <a-input-number :disabled="showDisable" size="small" v-model:value="formData.freightFee" style="width: 100%"/>
            </a-form-item>
            <a-form-item name="freightCurr" :label="'运费币种'" class="grid-item" :colon="false">
              <a-select
                v-model:value="formData.freightCurr"
                :disabled="showDisable"
                style="width: 100%"
                size="small"
                placeholder="Please select"
                :options="currMap"
              ></a-select>
            </a-form-item>
          </a-form>
        </div>
      </a-spin>
    </a-card>
    <div class="cs-submit-btn merge-3">
      <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
      </a-button>
      <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
    </div>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {getDeliveryOrderInsuranceInfoByHeadSid, getExchangeRateByCurr,getOrderSupplierList} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
const { getPCode } = usePCode()
const currMap = ref([])
const supplierList = ref([])
const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'sid':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'purchaseOrderNo':'编号'
, 'insuranceCompany':'保险公司'
, 'insurant':'被保险人'
, 'invoiceTitle':'发票抬头'
, 'trafName':'运输工具名称'
, 'startShipmentDate':'开航日期'
, 'trafRouteFrom':'运输路线自'
, 'trafRoutePass':'经'
, 'trafRouteTo':'至'
, 'insuranceType':'投保险别'
, 'curr':'币种'
, 'insuranceMarkup':'投保加成%'
, 'insuranceAmount':'保险金额'
, 'insuranceRate':'保险费率'
, 'insuranceFee':'保费'
, 'insuranceDate':'投保日期'
, 'freightFee':'投保日期'
, 'freightCurr':'投保日期'
, 'note':'备注'
, 'headId':'表头id'
, 'analysisId':'分析单id'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId:{
    type:String,
    default: () => ''
  },
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid: ''
,purchaseOrderNo: ''
,insuranceCompany: ''
,insurant: ''
,invoiceTitle: ''
,trafName: ''
,startShipmentDate: ''
,trafRouteFrom: ''
,trafRoutePass: ''
,trafRouteTo: ''
,insuranceType: ''
,curr: ''
,insuranceMarkup: ''
,insuranceAmount: ''
,insuranceRate: ''
,insuranceFee: ''
,insuranceDate: ''
,freightFee: ''
,freightCurr: ''
,note: ''
,exchangeRate: ''
})
// 表单校验规则
const rules = {
purchaseOrderNo: [
  { required: true, message: '编号不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
insuranceCompany: [
  { required: true, message: '保险公司不能为空！', trigger: 'blur' },
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
insurant: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
invoiceTitle: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
trafName: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
startShipmentDate: [
{ max: null, message: '长度不能超过null位字节(汉字占2位)！', trigger: 'blur' }
],
trafRouteFrom: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
trafRoutePass: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
trafRouteTo: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
insuranceType: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
curr: [
  { required: true, message: '币种不能为空！', trigger: 'blur' },
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
insuranceMarkup: [
  { required: true, message: '投保加成不能为空！', trigger: 'blur' },
],
insuranceAmount: [
  { required: true, message: '投保金额不能为空！', trigger: 'blur' },
],
insuranceRate: [
  { required: true, message: '投保费率不能为空！', trigger: 'blur' },
],
insuranceFee: [
  { required: true, message: '保费不能为空！', trigger: 'blur' },
],
insuranceDate: [
{ max: null, message: '长度不能超过null位字节(汉字占2位)！', trigger: 'blur' }
],
freightFee: [
],
freightCurr: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
note: [
{ max: 1000, message: '长度不能超过1000位字节(汉字占2位)！', trigger: 'blur' }
],
}
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      window.majesty.httpUtil.putAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderInsuranceInfo.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      window.majesty.httpUtil.putAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderInsuranceInfo.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  initEdit();
  getExchangeRate();
  getSupplierList();
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      value
    }));
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    // Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    // Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    // Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
})
const initEdit = async () => {
  const res = await getDeliveryOrderInsuranceInfoByHeadSid({'headId':props.headId})
  if (res.code === 200){
    if(null !== res.data){
      Object.assign(formData, res.data);
    }else {
      Object.assign(formData, {});
    }
    spinning.value = false
  }else {
    message.error(res.message)
  }
}
const spinning = ref(true);
const getExchangeRate = () => {
  if(formData.curr !== null && formData.curr !== ''){
    getExchangeRateByCurr(formData.curr).then(res=>{
      console.log('resRate',res)
      if (res.code === 200 && res.data !== null){
        formData.exchangeRate = res.data.rate
        console.log('formData',formData)
      }
    })
  }
}
const currChange = () =>{
  getExchangeRate();
  insuranceFeeChange();
}

const insuranceMarkupChange = () =>{
  if(formData.insuranceAmount !== null){
    delete formData.insuranceAmount;
  }
  if(formData.insuranceMarkup !== null && props.editConfig.editData.totalAmount !== null){
    formData.insuranceAmount = insuranceAmountCount(formData)
  }
}
const insuranceFeeChange = () =>{
  if(formData.insuranceFee !== null){
    delete formData.insuranceFee;
  }
  if(formData.insuranceAmount !== null && formData.insuranceRate !== null && formData.exchangeRate !== null){
    formData.insuranceFee = insuranceFeeCount(formData)
  }
}
const insuranceFeeCount = (row) => {
  const insuranceAmount = parseFloat(row.insuranceAmount);
  const insuranceRate = parseFloat(row.insuranceRate);
  const exchangeRate = parseFloat(row.exchangeRate);
  const insuranceFee = roundToDecimal(insuranceAmount*insuranceRate*exchangeRate,2)
  return insuranceFee !== null ? insuranceFee : null
};
const insuranceAmountCount = (row) => {
  const insuranceMarkup = parseFloat(row.insuranceMarkup);
  const totalAmount = parseFloat(props.editConfig.editData.totalAmount);
  const insuranceAmount = roundToDecimal(totalAmount*(100+insuranceMarkup)/100,4)
  return insuranceAmount !== null ? insuranceAmount : null
};
function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}
</script>
<style lang="less" scoped>
</style>
