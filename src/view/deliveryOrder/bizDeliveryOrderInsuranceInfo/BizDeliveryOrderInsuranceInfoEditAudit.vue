<template>
  <section>
    <a-card size="small" title="xxx" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="purchaseOrderNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('purchaseOrderNo')"
                  @click="handleLabelClick('purchaseOrderNo')"
              >
                编号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.purchaseOrderNo"></a-input>
          </a-form-item>
          <a-form-item name="insuranceCompany" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insuranceCompany')"
                  @click="handleLabelClick('insuranceCompany')"
              >
                保险公司
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.insuranceCompany"></a-input>
          </a-form-item>
          <a-form-item name="insurant" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insurant')"
                  @click="handleLabelClick('insurant')"
              >
                被保险人
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.insurant"></a-input>
          </a-form-item>
          <a-form-item name="invoiceTitle" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('invoiceTitle')"
                  @click="handleLabelClick('invoiceTitle')"
              >
                发票抬头
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.invoiceTitle"></a-input>
          </a-form-item>
          <a-form-item name="trafName" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('trafName')"
                  @click="handleLabelClick('trafName')"
              >
                运输工具名称
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.trafName"></a-input>
          </a-form-item>
          <a-form-item name="startShipmentDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('startShipmentDate')"
                  @click="handleLabelClick('startShipmentDate')"
              >
                开航日期
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.startShipmentDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="trafRouteFrom" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('trafRouteFrom')"
                  @click="handleLabelClick('trafRouteFrom')"
              >
                运输路线自
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.trafRouteFrom"></a-input>
          </a-form-item>
          <a-form-item name="trafRoutePass" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('trafRoutePass')"
                  @click="handleLabelClick('trafRoutePass')"
              >
                经
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.trafRoutePass"></a-input>
          </a-form-item>
          <a-form-item name="trafRouteTo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('trafRouteTo')"
                  @click="handleLabelClick('trafRouteTo')"
              >
                至
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.trafRouteTo"></a-input>
          </a-form-item>
          <a-form-item name="insuranceType" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insuranceType')"
                  @click="handleLabelClick('insuranceType')"
              >
                投保险别
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.insuranceType"></a-input>
          </a-form-item>
          <a-form-item name="curr" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('curr')"
                  @click="handleLabelClick('curr')"
              >
                币种
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.curr"></a-input>
          </a-form-item>
          <a-form-item name="insuranceMarkup" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insuranceMarkup')"
                  @click="handleLabelClick('insuranceMarkup')"
              >
                投保加成%
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceMarkup" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="insuranceAmount" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insuranceAmount')"
                  @click="handleLabelClick('insuranceAmount')"
              >
                保险金额
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceAmount" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="insuranceRate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insuranceRate')"
                  @click="handleLabelClick('insuranceRate')"
              >
                保险费率
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceRate" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="insuranceFee" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insuranceFee')"
                  @click="handleLabelClick('insuranceFee')"
              >
                保费
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceFee" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="insuranceDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('insuranceDate')"
                  @click="handleLabelClick('insuranceDate')"
              >
                投保日期
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.insuranceDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="freightFee" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('freightFee')"
                  @click="handleLabelClick('freightFee')"
              >
                投保日期
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.freightFee" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="freightCurr" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('freightCurr')"
                  @click="handleLabelClick('freightCurr')"
              >
                投保日期
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.freightCurr"></a-input>
          </a-form-item>
          <a-form-item name="note" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('note')"
                  @click="handleLabelClick('note')"
              >
                备注
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"></a-input>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'sid':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'purchaseOrderNo':'编号'
, 'insuranceCompany':'保险公司'
, 'insurant':'被保险人'
, 'invoiceTitle':'发票抬头'
, 'trafName':'运输工具名称'
, 'startShipmentDate':'开航日期'
, 'trafRouteFrom':'运输路线自'
, 'trafRoutePass':'经'
, 'trafRouteTo':'至'
, 'insuranceType':'投保险别'
, 'curr':'币种'
, 'insuranceMarkup':'投保加成%'
, 'insuranceAmount':'保险金额'
, 'insuranceRate':'保险费率'
, 'insuranceFee':'保费'
, 'insuranceDate':'投保日期'
, 'freightFee':'投保日期'
, 'freightCurr':'投保日期'
, 'note':'备注'
, 'headId':'表头id'
, 'analysisId':'分析单id'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid:''
,purchaseOrderNo: ''
,insuranceCompany: ''
,insurant: ''
,invoiceTitle: ''
,trafName: ''
,startShipmentDate: ''
,trafRouteFrom: ''
,trafRoutePass: ''
,trafRouteTo: ''
,insuranceType: ''
,curr: ''
,insuranceMarkup: ''
,insuranceAmount: ''
,insuranceRate: ''
,insuranceFee: ''
,insuranceDate: ''
,freightFee: ''
,freightCurr: ''
,note: ''
})
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})
/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }
  // 审核意见输入框
  const auditOpinion = ref('同意审批')
  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'xxx',
      }
      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderInsuranceInfo.audit, params)
          .then(res => {
            if (res.code === 200) {
              message.success("审核通过成功！")
              // 返回列表页面
              onBack(true)
            } else {
              message.error(res.message || '审核失败')
            }
          })
          .catch(error => {
            console.error('审核失败:', error)
            message.error('审核失败，请重试')
          })
          .finally(() => {
            auditLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }
  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'contract',
      }
      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderInsuranceInfo.reject, params)
          .then(res => {
            if (res.code === 200) {
              // 审核退回成功后，调用标记保存接口
              return saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
            } else {
              throw new Error(res.message || '审核退回失败')
            }
          })
          .then(res => {
            message.success("审核退回成功！")
            // 返回列表页面
            onBack(true)
          })
          .catch(error => {
            console.error('审核退回失败:', error)
            message.error(error.message || '审核退回失败，请重试')
          })
          .finally(() => {
            invalidLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
</script>
<style lang="less" scoped>
</style>
