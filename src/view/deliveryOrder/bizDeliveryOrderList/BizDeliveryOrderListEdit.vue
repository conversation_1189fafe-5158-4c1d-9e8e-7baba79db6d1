<template>
  <section>
    <a-card size="small" title="xxx" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="productName" :label="'商品名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.productName"></a-input>
          </a-form-item>
          <a-form-item name="productModel" :label="'产品型号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.productModel"></a-input>
          </a-form-item>
          <a-form-item name="unit" :label="'单位'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.unit"></a-input>
          </a-form-item>
          <a-form-item name="qty" :label="'数量'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.qty" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="unitPrice" :label="'单价'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.unitPrice" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="amount" :label="'金额'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.amount" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"></a-input>
          </a-form-item>
          <a-form-item name="containerNum" :label="'箱数'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.containerNum" style="width: 100%"/>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'sid':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'productName':'商品名称'
, 'productModel':'产品型号'
, 'unit':'单位'
, 'qty':'数量'
, 'unitPrice':'单价'
, 'amount':'金额'
, 'note':'备注'
, 'containerNum':'箱数'
, 'headId':'表头id'
, 'contractListId':'外商合同表体id'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid: ''
,productName: ''
,productModel: ''
,unit: ''
,qty: ''
,unitPrice: ''
,amount: ''
,note: ''
,containerNum: ''
})
// 表单校验规则
const rules = {
productName: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
productModel: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
unit: [
{ max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
],
qty: [
],
unitPrice: [
],
amount: [
],
note: [
{ max: 1000, message: '长度不能超过1000位字节(汉字占2位)！', trigger: 'blur' }
],
containerNum: [
],
}
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      window.majesty.httpUtil.putAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderList.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      window.majesty.httpUtil.putAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderList.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})
</script>
<style lang="less" scoped>
</style>
