<template>
  <section>
    <a-card size="small" title="xxx" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="boxNoStart" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('boxNoStart')"
                  @click="handleLabelClick('boxNoStart')"
              >
                起始箱号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.boxNoStart"></a-input>
          </a-form-item>
          <a-form-item name="boxNoEnd" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('boxNoEnd')"
                  @click="handleLabelClick('boxNoEnd')"
              >
                结束箱号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.boxNoEnd"></a-input>
          </a-form-item>
          <a-form-item name="productName" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('productName')"
                  @click="handleLabelClick('productName')"
              >
                商品名称
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.productName"></a-input>
          </a-form-item>
          <a-form-item name="packageStyle" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('packageStyle')"
                  @click="handleLabelClick('packageStyle')"
              >
                包装样式
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.packageStyle"></a-input>
          </a-form-item>
          <a-form-item name="grossWt" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('grossWt')"
                  @click="handleLabelClick('grossWt')"
              >
                毛重(KG)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.grossWt" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="netWt" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('netWt')"
                  @click="handleLabelClick('netWt')"
              >
                净重(KG)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.netWt" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="tareWt" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('tareWt')"
                  @click="handleLabelClick('tareWt')"
              >
                皮重(KG)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.tareWt" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="long" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('long')"
                  @click="handleLabelClick('long')"
              >
                长(M)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.long" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="whith" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('whith')"
                  @click="handleLabelClick('whith')"
              >
                宽(M)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.whith" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="high" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('high')"
                  @click="handleLabelClick('high')"
              >
                高(M)
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.high" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="note" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('note')"
                  @click="handleLabelClick('note')"
              >
                备注
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"></a-input>
          </a-form-item>
          <a-form-item name="containerNum" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('containerNum')"
                  @click="handleLabelClick('containerNum')"
              >
                箱数
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.containerNum" style="width: 100%"/>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'sid':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'boxNoStart':'起始箱号'
, 'boxNoEnd':'结束箱号'
, 'productName':'商品名称'
, 'packageStyle':'包装样式'
, 'grossWt':'毛重(KG)'
, 'netWt':'净重(KG)'
, 'tareWt':'皮重(KG)'
, 'long':'长(M)'
, 'whith':'宽(M)'
, 'high':'高(M)'
, 'note':'备注'
, 'containerNum':'箱数'
, 'headId':'表头id'
, 'contractListId':'外商合同表体id'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid:''
,boxNoStart: ''
,boxNoEnd: ''
,productName: ''
,packageStyle: ''
,grossWt: ''
,netWt: ''
,tareWt: ''
,long: ''
,whith: ''
,high: ''
,note: ''
,containerNum: ''
})
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})
/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }
  // 审核意见输入框
  const auditOpinion = ref('同意审批')
  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'xxx',
      }
      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderContainerList.audit, params)
          .then(res => {
            if (res.code === 200) {
              message.success("审核通过成功！")
              // 返回列表页面
              onBack(true)
            } else {
              message.error(res.message || '审核失败')
            }
          })
          .catch(error => {
            console.error('审核失败:', error)
            message.error('审核失败，请重试')
          })
          .finally(() => {
            auditLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }
  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'contract',
      }
      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderContainerList.reject, params)
          .then(res => {
            if (res.code === 200) {
              // 审核退回成功后，调用标记保存接口
              return saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
            } else {
              throw new Error(res.message || '审核退回失败')
            }
          })
          .then(res => {
            message.success("审核退回成功！")
            // 返回列表页面
            onBack(true)
          })
          .catch(error => {
            console.error('审核退回失败:', error)
            message.error(error.message || '审核退回失败，请重试')
          })
          .finally(() => {
            invalidLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
</script>
<style lang="less" scoped>
</style>
