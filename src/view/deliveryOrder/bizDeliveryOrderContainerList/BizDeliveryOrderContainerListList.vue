<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn" v-if="props.operationStatus !== editStatus.SHOW && props.isConfirm === '0'">
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:add']">
          <a-button size="small" @click="handlerAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{localeContent('m.common.button.add')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:deliveryOrder:delete']">
          <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{localeContent('m.common.button.delete')}}
          </a-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :heigh="530"
          column-drag
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          :custom-row="customRow"
          @keydown.enter="handleEnter"
          @blur="handleBlur"
          @keydown.esc="handleBlur"
          row-key="sid"
          :row-height="30"
          :range-selection="false"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, text, record }">

            <template v-if="['boxNoStart','boxNoEnd'].includes(column.dataIndex) && props.operationStatus !== editStatus.SHOW && props.isConfirm === '0'">
              <div>
                <span v-if="editableData[record.sid]">
                <a-input
                  size="small"
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="margin: -5px 0"
                />
                  </span>
                <span v-else>
                  {{ text }}
                </span>
              </div>
            </template>

            <template v-if="['qty','containerNum','grossWt','netWt','longer','whither','higher'].includes(column.dataIndex) && props.operationStatus !== editStatus.SHOW && props.isConfirm === '0'">
              <div>
                <span v-if="editableData[record.sid]">
                   <a-input-number
                     size="small"
                     v-model:value="editableData[record.sid][column.dataIndex]"
                     style="margin: -5px 0"
                     :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0'"
                     :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                   />
              </span>
                <span v-else>
                    {{ formatNumber(text) }}
                </span>
              </div>
            </template>

            <template v-if="['packageStyle'].includes(column.dataIndex) && props.operationStatus !== editStatus.SHOW && props.isConfirm === '0'">
              <div>
                <span v-if="editableData[record.sid]">
                <a-select
                  size="small"
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  :options="packageInfoMap"
                ></a-select>
              </span>
                <span v-else>
                  {{ cmbShowRender(text,packageInfoMap) }}
                </span>
              </div>
            </template>

            <template v-else-if="column.dataIndex === 'operation'">
              <div class="operation-container">
                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleEditByRow(record)"
                    :style="operationEdit('edit')"
                  >
                    <template #icon>
                      <GlobalIcon type="form" style="color:#e93f41"/>
                    </template>
                  </a-button>
                </div>

                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleViewByRow(record)"
                    :style="operationEdit('view')"
                  >
                    <template #icon>
                      <GlobalIcon type="search" style="color:#1677ff"/>
                    </template>
                  </a-button>
                </div>
              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>
    <!-- 新增 编辑数据 -->
<!--    <div v-if="!show">-->
<!--      <BizDeliveryOrderContainerListTabs  @onEditBack="handlerOnBack" :editConfig="editConfig"></BizDeliveryOrderContainerListTabs>-->
<!--    </div>-->
  </section>
</template>
<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon';
import {createVNode, onMounted, reactive, ref, watch} from "vue";
import {getColumns} from './bizDeliveryOrderContainerListColumns';
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
const { totalColumns } = getColumns();
import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute, useRouter} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
import BizDeliveryOrderContainerListListSearch from  './BizDeliveryOrderContainerListListSearch';
import BizDeliveryOrderContainerListTabs from  './BizDeliveryOrderContainerListTabs'
import { useFieldMarking } from '@/utils/useFieldMarking';
import {updateOrderCListList} from "@/api/cs_api_constant";
import {useColumnsRender} from "@/view/common/useColumnsRender";
/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  // getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData,
  onSelectChange
} = useCommon()
/* 引入字段标记功能 */
const { getBySidAndFormType } = useFieldMarking()
defineOptions({
  name: 'BizDeliveryOrderContainerListList',
});
const router = useRouter();
const planSelectVisible = ref(false);
const packageInfoMap = ref([]);
onMounted(fn => {
  getPackageInfoMap();
  ajaxUrl.selectAllPage = ycCsApi.deliveryOrder.bizDeliveryOrderContainerList.list
  ajaxUrl.exportUrl = ycCsApi.deliveryOrder.bizDeliveryOrderContainerList.export
  tableHeight.value = getTableScroll(700,'');
  getList()
  initCustomColumn()
})

const sourceData = ref([])
const getList = () => {
  tableLoading.value = true
  console.log('props', props)
  window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderContainerList.list}?page=${page.current}&limit=${page.pageSize}`,
    {headId:props.headId}
  ).then(res => {
    dataSourceList.value = res.data
    Object.assign(sourceData.value, res.data)
    page.total = res.total
  }).finally(() => {
    tableLoading.value = false
  })
}
const getPackageInfoMap = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.packageInfo.listAll}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        packageInfoMap.value.push({
          value: item.paramCode,
          label: item.packUnitCnName
        });
      });
    } else {
      message.error(res.message || '获取包装信息数据失败');
    }
  } catch (error) {
    message.error('获取包装信息数据失败');
  }
}
const { cmbShowRender } = useColumnsRender()
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return '0';
  }
  // 将值转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '0';
  }
  // 使用 toLocaleString 添加千位分隔符
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 6
  });
};
const editableData = reactive({});
const editSid = ref();
const tableHeight = ref('')
/* 按钮loading */
const deleteLoading = ref(false)
const confirmLoading = ref(false)
const auditLoading = ref(false)
const invalidLoading = ref(false)
const props =  defineProps({
  /* 进口信息表头sid */
  headId:{
    type:String,
    default:()=>''
  },
  /* 查看状态 */
  operationStatus:{
    type:Boolean,
    default:()=>''
  },
  isConfirm:{
    type:String,
    default:()=>'0'
  },
})
/* 返回事件 */
const handlerOnBack = (flag) => {
  console.log('返回',flag)
  show.value = !show.value;
  // 返回清空选择数据
  if (flag){
    getList()
  }
}
/* 新增数据 */
const handlerAdd = () => {
  planSelectVisible.value = true;
}
const handleEditByRow = (row) => {
  // 在这里添加处理编辑行的逻辑
  // if (row.dataStatus !== '0'){
  //   message.warning('仅0编制的数据可以操作编辑')
  //   return
  // }
  // show.value = !show.value
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = row
  editableData[row.sid] = row
}
/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // if (gridData.selectedData[0].dataStatus !== '0'){
  //   message.warning('仅0编制的数据可以操作编辑')
  //   return
  // }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]
  console.log('editConfig.value.editData', gridData)
  show.value =!show.value;
}
/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      window.majesty.httpUtil.deleteAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderContainerList.delete}/${gridData.selectedRowKeys}`).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {
    },
  });
}
/* 导出事件 */
const handlerExport = () =>{
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = ``
  doExport(`国营贸易进口卷烟-进口合同.xlsx`, totalColumns)
}
/* 自定义设置 */
const showColumns =  ref([])
/* 唯一键 */
const tableKey = ref('')
console.log('window.majesty.router',window.majesty.router.patch)
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()
/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}
/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
}
/* 双击行进入编辑页面 */
// const handleRowDblclick = (record) => {
//   handleViewByRow(record)
// };
// 自定义行属性
const customRow = (record) => {
  return {
    onDblclick: () => {
      handleRowDblclick(record);
    }, style: {cursor: 'pointer'}
  };
};
const selectedRowKeys = ref([]);
const detailEditStatus=ref(editStatus.SHOW);
/* 双击行进入编辑页面 */
const handleRowDblclick = (record) => {
  edit(record.sid);
};
const handleBlur = () => {
  const sid = selectedRowKeys.value[0];
  cancel(sid)
  getList()
};
const handleEnter = () => {
  const sid = selectedRowKeys.value[0];
  save(sid);
};
const cancel = (sid) => {
  // 先删除编辑状态
  delete editableData[sid];

  // 获取该行记录
  const record = dataSourceList.value.find(item => item.sid === sid);

  // 更新表格数据，确保视图更新
  dataSourceList.value = [...dataSourceList.value];

  // 从选中行中移除
  if (selectedRowKeys.value.includes(sid)) {
    selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== sid);
  }

  // 检查是否还有编辑中的行
  const stillEditing = Object.keys(editableData).length > 0;

  // 如果没有编辑中的行，重置编辑状态
  if (!stillEditing) {
    detailEditStatus.value = editStatus.SHOW;
  }

};

const save = (sid) => {
  if (!editableData[sid]) return;

  const record = dataSourceList.value.find(item => item.sid === sid);
  console.log('re',record)
  if (!record) return;
  // 创建更新/新增的数据对象
  const saveData = { ...record };
  // 只更新允许编辑的字段
  const editableFields = ['boxNoStart','boxNoEnd','englishBrand','packageStyle','qty','containerNum','grossWt', 'netWt', 'longer','whither','higher'];
  editableFields.forEach(field => {
    if (editableData[sid] && editableData[sid][field] !== undefined) {
      saveData[field] = editableData[sid][field];
    }
  });
  // 确保 headId 字段存在
  saveData.headId = props.headId;
  updateOrderCListList(sid, saveData).then((res) => {
    if (res.code === 200) {
      message.success('更新成功!');
      const index = dataSourceList.value.findIndex(item => item.sid === sid);
      if (index !== -1) {
        // 更新本地数据中的字段
        editableFields.forEach(field => {
          if (saveData[field] !== undefined) {
            dataSourceList.value[index][field] = saveData[field];
          }
        });
      }
      // 清除此行的编辑状态
      delete editableData[sid];

      // 强制更新表格数据，确保视图重新渲染
      dataSourceList.value = [...dataSourceList.value];

      // 检查是否还有其他行在编辑中
      const stillEditing = Object.keys(editableData).length > 0;

      // 只有当没有任何行在编辑中时，才重置整体编辑状态
      if (!stillEditing) {
        detailEditStatus.value = editStatus.SHOW;
      }
      emit('listBack', true);
    } else {
      message.error(res.message || '保存失败');
    }
  }).finally(() => {
    getList()
  }).catch((error) => {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
  });
};
const emit = defineEmits(['listBack']);
const onBack = (val) => {
  emit('listBack', val);
}
// const edit = (sid) => {
//   editSid.value = sid
//   editableData[sid] = { ...dataSourceList.value.find(item => item.sid === sid) };
// };
// 编辑单行数据
const edit = (sid) => {
  // 找到要编辑的记录
  const targetRecord = dataSourceList.value.find(item => item.sid === sid);
  if (targetRecord) {
    // 将该行设为选中状态
    selectedRowKeys.value = [sid];

    // 深拷贝记录到编辑数据
    editableData[sid] = JSON.parse(JSON.stringify(targetRecord));

    // 设置编辑状态
    detailEditStatus.value = editStatus.EDIT;
  }
};
/* 监控 dataSourceList */
// watch(dataSourceList, (newValue, oldValue) => {
//   showColumns.value = [...totalColumns.value];
//   // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
// },{deep:true})
watch(totalColumns.value, (newValue, oldValue) => {
  if(!window.$vueApp){
    showColumns.value = [...totalColumns.value];
  }else {
    if (newValue.length === 0) {
      showColumns.value = [...totalColumns.value];
    }else {
      showColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
      totalColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
    }
  }
},{immediate:true,deep:true})
/* 处理计划选择 */
const handlePlanSelect = (plan) => {
  editConfig.value.editStatus = editStatus.ADD;
  editConfig.value.editData = plan;
  //标识新增
  // editConfig.value.flag = editStatus.ADD;
  show.value = !show.value;
};
</script>
<style lang="less" scoped>
</style>
