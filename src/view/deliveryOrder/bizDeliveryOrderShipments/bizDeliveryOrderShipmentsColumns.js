import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }
  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};
const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()
// 初始化时获取数据
await getMerchantOptions()
function getColumns() {
  const commColumns = reactive([
    'businessType'
    , 'sid'
    , 'createBy'
    , 'createTime'
    , 'createUserName'
    , 'updateBy'
    , 'updateTime'
    , 'updateUserName'
    , 'tradeCode'
    , 'sysOrgCode'
    , 'extend1'
    , 'extend2'
    , 'extend3'
    , 'extend4'
    , 'extend5'
    , 'extend6'
    , 'extend7'
    , 'extend8'
    , 'extend9'
    , 'extend10'
    , 'businessType'
    , 'shipper'
    , 'consignee'
    , 'notifyParty'
    , 'portOfShipment'
    , 'portOfDestination'
    , 'destinationDate'
    , 'warehouseAddress'
    , 'contactPerson'
    , 'contactPhone'
    , 'note'
    , 'headId'
    , 'analysisId'
  ])
  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])
  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])
  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '装运人SHIPPER',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'shipper',
      resizable: true,
      key: 'shipper',
    },
    {
      title: '收件人CONSIGNEE',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'consignee',
      resizable: true,
      key: 'consignee',
    },
    {
      title: '通知人NOTIFY PARTY',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'notifyParty',
      resizable: true,
      key: 'notifyParty',
    },
    {
      title: '装运港',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'portOfShipment',
      resizable: true,
      key: 'portOfShipment',
    },
    {
      title: '目的地/港',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'portOfDestination',
      resizable: true,
      key: 'portOfDestination',
    },
    {
      title: '装运期限',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'destinationDate',
      resizable: true,
      key: 'destinationDate',
    },
    {
      title: '仓库地址',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'warehouseAddress',
      resizable: true,
      key: 'warehouseAddress',
    },
    {
      title: '联系人',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'contactPerson',
      resizable: true,
      key: 'contactPerson',
    },
    {
      title: '电话',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'contactPhone',
      resizable: true,
      key: 'contactPhone',
    },
    {
      title: '备注',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },
  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
export { getColumns }
