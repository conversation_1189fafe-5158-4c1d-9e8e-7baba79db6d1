import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }
  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};
const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()
// 初始化时获取数据
await getMerchantOptions()
function getColumns() {
  const commColumns = reactive([
    'businessType'
    , 'sid'
    , 'createBy'
    , 'createTime'
    , 'createUserName'
    , 'updateBy'
    , 'updateTime'
    , 'updateUserName'
    , 'tradeCode'
    , 'sysOrgCode'
    , 'extend1'
    , 'extend2'
    , 'extend3'
    , 'extend4'
    , 'extend5'
    , 'extend6'
    , 'extend7'
    , 'extend8'
    , 'extend9'
    , 'extend10'
    , 'businessType'
    , 'transportPermit'
    , 'shipmentConfirmDate'
    , 'entryNo'
    , 'declarationDate'
    , 'releaseDate'
    , 'shippingMark'
    , 'note'
    , 'headId'
    , 'analysisId'
  ])
  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])
  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])
  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '准运证编号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'transportPermit',
      resizable: true,
      key: 'transportPermit',
    },
    {
      title: '出货确认日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'shipmentConfirmDate',
      resizable: true,
      key: 'shipmentConfirmDate',
    },
    {
      title: '报关单号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'entryNo',
      resizable: true,
      key: 'entryNo',
    },
    {
      title: '申报日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'declarationDate',
      resizable: true,
      key: 'declarationDate',
    },
    {
      title: '放行日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'releaseDate',
      resizable: true,
      key: 'releaseDate',
    },
    {
      title: '唛头',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'shippingMark',
      resizable: true,
      key: 'shippingMark',
    },
    {
      title: '备注',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },
  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
export { getColumns }
