<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <BizDeliveryOrderCertListSearch ref="listSearch"></BizDeliveryOrderCertListSearch>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:xxx:audit']">
          <a-button size="small" :loading="auditLoading" @click="handlerAudit">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue"/>
            </template>
            审核通过
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:xxx:refuse']">
          <a-button size="small" :loading="invalidLoading" @click="handlerInvalid">
            <template #icon>
              <GlobalIcon type="close-square" style="color:red"/>
            </template>
            审核退回
          </a-button>
        </div>
        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
              :resId="tableKey"
              :tableKey="tableKey+'-contract'"
              :initSettingColumns="originalColumns"
              :showColumnSettings="true"
              @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
            :animate-rows="false"
            ref="tableRef"
            class="cs-action-item remove-table-border-add-bg"
            size="small"
            :scroll="{ y: tableHeight,x:400 }"
            column-drag
            bordered
            :pagination="false"
            :columns="showColumns.length > 0 ?showColumns:totalColumns"
            :data-source="dataSourceList"
            :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
            :loading="tableLoading"
            row-key="sid"
            :custom-row="customRow"
            :row-height="30"
            :range-selection="false"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                    size="small"
                    type="link"
                    @click="handleViewByRow(record)"
                    :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>
              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>
    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <BizDeliveryOrderCertTabs  @onEditBack="handlerOnBack" :editConfig="editConfig"></BizDeliveryOrderCertTabs>
    </div>
  </section>
</template>
<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon';
import {createVNode, onMounted, reactive, ref, watch} from "vue";
import {getColumns} from './bizDeliveryOrderCertColumns';
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
const { totalColumns } = getColumns();
import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute, useRouter} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
import BizDeliveryOrderCertListSearch from  './BizDeliveryOrderCertListSearch';
import BizDeliveryOrderCertTabs from  './BizDeliveryOrderCertTabs'
import { useFieldMarking } from '@/utils/useFieldMarking';
/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData,
  onSelectChange
} = useCommon()
/* 引入字段标记功能 */
const { getBySidAndFormType } = useFieldMarking()
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
, 'sid':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'transportPermit':'准运证编号'
, 'shipmentConfirmDate':'出货确认日期'
, 'entryNo':'报关单号'
, 'declarationDate':'申报日期'
, 'releaseDate':'放行日期'
, 'shippingMark':'唛头'
, 'note':'备注'
, 'headId':'表头id'
, 'analysisId':'分析单id'
}
defineOptions({
  name: 'BizDeliveryOrderCertList',
});
const router = useRouter();
const planSelectVisible = ref(false);
onMounted(fn => {
  ajaxUrl.selectAllPage = ycCsApi.deliveryOrder.bizDeliveryOrderCert.list
  ajaxUrl.exportUrl = ycCsApi.deliveryOrder.bizDeliveryOrderCert.export
  tableHeight.value = getTableScroll(100,'');
  getList()
  initCustomColumn()
})
const tableHeight = ref('')
/* 按钮loading */
const deleteLoading = ref(false)
const confirmLoading = ref(false)
const auditLoading = ref(false)
const invalidLoading = ref(false)
/* 返回事件 */
const handlerOnBack = (flag) => {
  console.log('返回',flag)
  show.value = !show.value;
  // 返回清空选择数据
  if (flag){
    getList()
  }
}
/* 自定义设置 */
const showColumns =  ref([])
/* 唯一键 */
const tableKey = ref('')
console.log('window.majesty.router',window.majesty.router.patch)
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()
/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}
/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
}
/* 双击行进入编辑页面 */
const handleRowDblclick = (record) => {
  handleViewByRow(record)
};
// 自定义行属性
const customRow = (record) => {
  return {
    onDblclick: () => {
      handleRowDblclick(record);
    }, style: {cursor: 'pointer'}
  };
};
/* 监控 dataSourceList */
// watch(dataSourceList, (newValue, oldValue) => {
//   showColumns.value = [...totalColumns.value];
//   // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
// },{deep:true})
watch(totalColumns.value, (newValue, oldValue) => {
  if(!window.$vueApp){
    showColumns.value = [...totalColumns.value];
  }else {
    if (newValue.length === 0) {
      showColumns.value = [...totalColumns.value];
    }else {
      showColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
      totalColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
    }
  }
},{immediate:true,deep:true})
/* 处理计划选择 */
const handlePlanSelect = (plan) => {
  editConfig.value.editStatus = editStatus.ADD;
  editConfig.value.editData = plan;
  //标识新增
  // editConfig.value.flag = editStatus.ADD;
  show.value = !show.value;
};
/* 审核通过事件 */
const handlerAudit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择数据')
    return
  }
  // 审核意见输入框
  const auditOpinion = ref('同意审批')
  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: gridData.selectedRowKeys,
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'contract',   // 需要修改
      }
      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderCert.audit, params)
          .then(res => {
            if (res.code === 200) {
              message.success("审核通过成功！")
              getList()
              // 清空选择
              gridData.selectedRowKeys = []
              gridData.selectedData = []
            } else {
              message.error(res.message || '审核失败')
            }
          })
          .catch(error => {
            console.error('审核失败:', error)
            message.error('审核失败，请重试')
          })
          .finally(() => {
            auditLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 审核退回事件 */
const handlerInvalid = async () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 获取标红字段信息
  let markedFields = ''
  try {
    const fieldMarkings = await getBySidAndFormType(gridData.selectedRowKeys[0], 'default')
    console.log(fieldMarkings)
    if (fieldMarkings && typeof fieldMarkings === 'object') {
      // 从fieldMarkings对象中筛选出值为'red'的字段名
      const redFieldNames = Object.keys(fieldMarkings).filter(key => fieldMarkings[key] === 'red')
      if (redFieldNames.length > 0) {
        // 将英文字段名转换为中文显示
        const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
        markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
      }
    }
  } catch (error) {
    console.warn('获取标红字段失败:', error)
  }
  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: gridData.selectedRowKeys,
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'contract',  // 需要修改
      }
      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderCert.reject, params)
          .then(res => {
            if (res.code === 200) {
              message.success("审核退回成功！")
              getList()
              // 清空选择
              gridData.selectedRowKeys = []
              gridData.selectedData = []
            } else {
              message.error(res.message || '审核退回失败')
            }
          })
          .catch(error => {
            console.error('审核退回失败:', error)
            message.error('审核退回失败，请重试')
          })
          .finally(() => {
            invalidLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
</script>
<style lang="less" scoped>
</style>
