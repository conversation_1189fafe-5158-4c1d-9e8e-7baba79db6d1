<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '200px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 参数代码 -->
    <a-form-item name="paramCode" :label="'参数代码'" class="grid-item" :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramCode" />
    </a-form-item>

    <!-- 保险类别中文名称 -->
    <a-form-item name="insuranceTypeCn"   :label="'保险类别中文名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.insuranceTypeCn" />
    </a-form-item>



  </a-form>
</template>

<script setup>
import { onMounted, reactive, ref} from 'vue'

import {usePCode} from "@/view/common/usePCode";
const { getPCode } = usePCode();
defineOptions({
  name: 'PackageInfoSearch'
})
const searchParam = reactive({

  paramCode:'',
  insuranceTypeCn:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}


const pCode = ref('');
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
