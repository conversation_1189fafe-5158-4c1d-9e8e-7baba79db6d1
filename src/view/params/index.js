import { defineAsyncComponent } from "vue"

export default  [
  {
    path: '/tobacco/params/storehouse',
    name: 'storehouse',
    meta: {
      title: '仓库'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./storehouse/StorehouseList.vue"))
  },
  {
    path: '/tobacco/params/productType',
    name: 'productType',
    meta: {
      title: '商品类别'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./productType/ProductTypeList.vue"))
  },
  {
    path: '/tobacco/params/packageInfo',
    name: 'packageInfo',
    meta: {
      title: '包装信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./packageInfo/PackageInfoList.vue"))
  },
  {
    path: '/tobacco/params/costType',
    name: 'costType',
    meta: {
      title: '费用类型'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./costType/CostTypeList.vue"))
  },
  {
    path: '/tobacco/params/rateTable',
    name: 'rateTable',
    meta: {
      title: '汇率上浮表'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./rateTable/RateTableList.vue"))
  },
  {
    path: '/tobacco/params/enterpriseRate',
    name: 'enterpriseRate',
    meta: {
      title: '企业汇率'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./enterpriseRate/RateTableList.vue"))
  },
  {
    path: '/tobacco/params/priceTerms',
    name: 'PriceTermsList',
    meta: {
      title: '价格条款'
    },
    component: defineAsyncComponent(() => import(/* webpackChunkName: "my-chunk-name" */ './priceTerms/PriceTermsList.vue'))
  },
  {
    path: '/tobacco/params/city',
    name: 'CityList',
    meta: {
      title: '城市'
    },
    component: defineAsyncComponent(() => import(/* webpackChunkName: "my-chunk-name" */ './city/CityList.vue'))
  },
  {
    path: '/tobacco/params/transCode',
    name: 'transCode',
    meta: {
      title: '划款参数'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./transCode/TransCodeHeadList.vue"))
  },
  {
    path: '/tobacco/params/boxType',
    name: 'boxType',
    meta: {
      title: '箱型'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./boxType/BoxTypeList.vue"))
  },
  {
    path: '/tobacco/params/insuranceType',
    name: 'insuranceType',
    meta: {
      title: '保险类别'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./insuranceType/InsuranceTypeList.vue"))
  },



]
