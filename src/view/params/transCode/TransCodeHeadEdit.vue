<template>
  <section  >

    <a-card size="small" title="划款参数" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <a-form-item name="bizType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.bizType" id="bizType" @change="handleBizTypeChange">
              <a-select-option v-for="item in productClassify.bizType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="tariffRate" :label="'关税率% '" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.tariffRate" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="consumptionTaxRate" :label="'消费税率%'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.consumptionTaxRate" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="vatRate" :label="'增值税率%'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.vatRate" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="ieAgentFeeRate" :label="'进出口公司代理费率%'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.ieAgentFeeRate" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="hqAgentFeeRate" :label="'总公司代理费率%'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.hqAgentFeeRate" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="intlTransType" :label="'国际运输类型'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.intlTransType" id="intlTransType">
              <a-select-option v-for="item in productClassify.intlTransType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="isContainerShip" :label="'是否集装箱装运'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.isContainerShip" id="isContainerShip" @change="handleContainerShipChange">
              <a-select-option v-for="item in productClassify.isFlag"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="containerCap" :label="'集装箱容量'" class="grid-item" :colon="false">
            <cs-select :key="containerCapKey" :disabled="containerCapDisabled"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.containerCap" id="containerCap">
              <a-select-option v-for="item in containerCapOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="containerType" :label="'集装箱型'" class="grid-item" :colon="false">
            <cs-select :key="containerTypeKey" :disabled="containerTypeDisabled"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.containerType" id="containerType">
              <a-select-option v-for="item in boxTypeMap"  :key="item.value" :value="item.value" :label=" item.value">
                {{item.value}}
              </a-select-option>
            </cs-select>
          </a-form-item>


<!--          <a-form-item name="intlFreightAmt" :label="'国际运费'" class="grid-item" :colon="false">-->
<!--            <a-input-number :disabled="showDisable"  size="small" v-model:value="formattedValues.intlFreightAmt.value" style="width: 100%"/>-->
<!--          </a-form-item>-->

          <a-form-item name="intlFreightAmt" :label="'国际运费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.intlFreightAmt" notConvertNumber decimal int-length="19" precision="4"/>
          </a-form-item>
          <a-form-item name="portChargesAmt" :label="'港杂费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.portChargesAmt" notConvertNumber decimal int-length="19" precision="4"/>
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formattedValues.portChargesAmt.value" style="width: 100%"/>-->
          </a-form-item>
          <a-form-item name="landFreightAmt" :label="'陆运费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.landFreightAmt" notConvertNumber decimal int-length="19" precision="4"/>
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formattedValues.landFreightAmt.value" style="width: 100%"/>-->
          </a-form-item>
          <a-form-item name="customsFeeAmt" :label="'通关费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.customsFeeAmt" notConvertNumber decimal int-length="19" precision="4"/>
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formattedValues.customsFeeAmt.value" style="width: 100%"/>-->
          </a-form-item>
          <a-form-item name="cntrInspFeeAmt" :label="'验柜服务费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.cntrInspFeeAmt" notConvertNumber decimal int-length="19" precision="4"/>
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formattedValues.cntrInspFeeAmt.value" style="width: 100%"/>-->
          </a-form-item>
          <a-form-item name="insuranceRate" :label="'保险费率'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.insuranceRate" style="width: 100%"/>
          </a-form-item>

          <a-form-item name="otherChargesAmt" :label="'其他费用'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.otherChargesAmt" notConvertNumber decimal int-length="19" precision="5"/>
<!--            <a-input-number :disabled="showDisable" size="small" v-model:value="formattedValues.otherChargesAmt.value" style="width: 100%"/>-->
          </a-form-item>


          <a-form-item name="remark" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.remark"/>
          </a-form-item>


          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回
            </a-button>
          </div>
        </a-form>
      </div>
    </a-card>

  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {computed, onMounted, reactive, ref, watch} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {
  insertTransCode,
  updateTransCode,
  getBoxTypeMap,
} from "@/api/params/params_info.js";
const { getPCode } = usePCode()
import {useCommon} from "@/view/common/useCommon";

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

const {
  editConfig,
  // show,
  page,
  showSearch,
  getList,
  headSearch,
  // handleEditByRow,
  // handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
const onBack = (val) => {
  emit('onEditBack', val);
};
// 是否禁用
const showDisable = ref(false)

// 集装箱容量是否禁用
const containerCapDisabled = ref(false)
// 集装箱型是否禁用
const containerTypeDisabled = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  bizType:'',
  tariffRate:'',
  consumptionTaxRate:'',
  vatRate:'',
  ieAgentFeeRate:'',
  hqAgentFeeRate:'',
  intlTransType:'',
  isContainerShip:'',
  containerCap:'',
  containerType:'',
  intlFreightAmt:'',
  portChargesAmt:'',
  landFreightAmt:'',
  customsFeeAmt:'',
  cntrInspFeeAmt:'',
  insuranceRate:'',
  otherChargesAmt:'',
  remark:'',
  insertUserName:'',
  createTime:'',
})


// 校验规则
const rules = {
  bizType: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  tariffRate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  vatRate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  ieAgentFeeRate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  hqAgentFeeRate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  isContainerShip: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  containerCap: [
    {
      validator: (rule, value) => {
        if (formData.isContainerShip === '1') { // 选择了"是"
          const bizType = formData.bizType
          if (bizType === '2' || bizType === '6') { // 进口辅料
            if (!value) {
              return Promise.reject('当业务类型为进口辅料时，集装箱容量必选')
            }
          } else if (bizType === '3') { // 国营贸易进口烟机设备
            // 集装箱容量不可输入，无需校验
          } else {
            // 其他业务类型，集装箱容量或集装箱型至少一个必填
            if (!value && !formData.containerType) {
              return Promise.reject('集装箱容量或集装箱型至少选择一个')
            }
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  containerType: [
    {
      validator: (rule, value) => {
        if (formData.isContainerShip === '1') { // 选择了"是"
          const bizType = formData.bizType
          if (bizType === '3') { // 国营贸易进口烟机设备
            if (!value) {
              return Promise.reject('当业务类型为国营贸易进口烟机设备时，集装箱箱型必选')
            }
          } else if (bizType === '2' || bizType === '6') { // 进口辅料
            // 集装箱箱型不可输入，无需校验
          } else {
            // 其他业务类型，集装箱容量或集装箱型至少一个必填
            if (!value && !formData.containerCap) {
              return Promise.reject('集装箱容量或集装箱型至少选择一个')
            }
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  landFreightAmt: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  customsFeeAmt: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
}

const boxTypeMap = ref([])
// 集装箱容量选项（用于强制重新渲染）
const containerCapOptions = ref([])
// 组件key，用于强制重新渲染
const containerCapKey = ref(0)
const containerTypeKey = ref(0)

// 重新加载集装箱相关数据
const reloadContainerData = async () => {
  try {
    // 重新加载集装箱型数据
    const boxTypeRes = await getBoxTypeMap()
    if (boxTypeRes.data != null) {
      boxTypeMap.value = boxTypeRes.data
    }

    // 重新设置集装箱容量选项（从常量中获取）
    containerCapOptions.value = [...productClassify.containerCap]

    // 强制重新渲染组件
    containerCapKey.value++
    containerTypeKey.value++
  } catch (error) {
    console.error('重新加载集装箱数据失败:', error)
  }
}

// 处理业务类型变化
const handleBizTypeChange = (value) => {
  updateContainerFieldsStatus()
  // 重新加载下拉框数据
  reloadContainerData()
}

// 处理集装箱装运变化
const handleContainerShipChange = (value) => {
  updateContainerFieldsStatus()
  // 重新加载下拉框数据
  reloadContainerData()
}

// 更新集装箱字段状态
const updateContainerFieldsStatus = () => {
  const isContainerShip = formData.isContainerShip
  const bizType = formData.bizType

  if (isContainerShip === '1') { // 是
    // 根据业务类型决定字段状态
    if (bizType === '2' || bizType === '6') { // 进口辅料
      containerCapDisabled.value = showDisable.value // 集装箱容量必选
      containerTypeDisabled.value = true // 集装箱箱型不可输入
      formData.containerType = '' // 清空集装箱型
    } else if (bizType === '3') { // 国营贸易进口烟机设备
      containerCapDisabled.value = true // 集装箱容量不可输入
      containerTypeDisabled.value = showDisable.value // 集装箱箱型必选
      formData.containerCap = '' // 清空集装箱容量
    } else {
      // 其他业务类型，集装箱容量或集装箱型不可为空（至少一个必填）
      containerCapDisabled.value = showDisable.value
      containerTypeDisabled.value = showDisable.value
    }
  } else if (isContainerShip === '2') { // 否
    containerCapDisabled.value = true // 集装箱容量不可输入
    containerTypeDisabled.value = true // 集装箱型不可输入
    formData.containerCap = '' // 清空集装箱容量
    formData.containerType = '' // 清空集装箱型
  } else {
    // 未选择时，保持可输入状态
    containerCapDisabled.value = showDisable.value
    containerTypeDisabled.value = showDisable.value
  }
}

// 监听业务类型变化
watch(() => formData.bizType, () => {
  updateContainerFieldsStatus()
})

// 监听是否集装箱装运变化
watch(() => formData.isContainerShip, () => {
  updateContainerFieldsStatus()
})

// 初始化操作
onMounted(() => {

  // 初始化集装箱容量选项
  containerCapOptions.value = [...productClassify.containerCap]

  getBoxTypeMap().then((res)=>{
    if (res.data !=null){
      boxTypeMap.value =  res.data
    }

  })


  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false

    Object.assign(formData, {});



  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }

  // 初始化集装箱字段状态
  updateContainerFieldsStatus()


});




// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);


function formatter(value) {
  if (value === '' || value === null || value === undefined) return '';
  const number = Number(value);
  if (isNaN(number)) return '';
  // 使用 Intl.NumberFormat 格式化，强制至少两位小数

  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 10
  }).format(number);
}



function parser(value) {
  // 去除所有千分位逗号，保留原始小数位
  return value.replace(/\$\s?|(,*)/g, '');
}


// 保存
const handlerSave = async () => {

  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertTransCode(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            formData.id = res.data.id
            props.editConfig.editData.id = res.data.id
            props.editConfig.editStatus = editStatus.EDIT
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        updateTransCode(formData.id,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')

          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
    })
};

const handlerSaveClose = async () => {
  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertTransCode(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        updateTransCode(formData.id,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
    })
}




</script>

<style lang="less" scoped>
/* 统一禁用状态下的输入框字体颜色 */
:deep(.ant-input[disabled]),
:deep(.ant-input-number-disabled),
:deep(.ant-select-disabled .ant-select-selection-item),
:deep(.ant-picker-input > input[disabled]) {
  color: rgba(0, 0, 0, 0.85) !important; /* 使用较深的黑色 */
}

/* 确保数字输入框和普通输入框在禁用状态下颜色一致 */
:deep(.ant-input-number-disabled .ant-input-number-input) {
  color: rgba(0, 0, 0, 0.85) !important;
}

</style>



