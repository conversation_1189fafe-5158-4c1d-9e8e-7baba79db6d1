<template>
  <section class="dc-section" ref="dcSectionRef">
    <div v-show="show" class="cs-action">
      <!-- 查询区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button v-show="showSearch" class="cs-margin-right cs-refresh"
                          size="small" type="primary" @click="handlerRefresh">
                  <template #icon>
                    <global-icon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{ localeContent('m.common.button.query') }}
                  <template #icon>
                    <global-icon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning"
                          @click="handleShowSearch">
                  <template #icon>
                    <global-icon v-show="!showSearch" type="down" style="color:#fff"/>
                    <global-icon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"/>
          <div ref="area_search">
            <div v-show="showSearch">
              <order-notice-search :customer-page-options="customerPageOptions" ref="headSearch"/>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:add']">
          <a-button size="small" @click="handleAdd">
            <template #icon>
              <global-icon type="plus" style="color:green"/>
            </template>
            {{ localeContent('m.common.button.add') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:update']">
          <a-button size="small" @click="handleEdit">
            <template #icon>
              <global-icon type="form" style="color:orange"/>
            </template>
            {{ localeContent('m.common.button.update') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:delete']">
          <a-button size="small" :loading="deleteLoading" @click="handleDelete">
            <template #icon>
              <global-icon type="delete" style="color:red"/>
            </template>
            {{ localeContent('m.common.button.delete') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:export']">
          <a-button size="small" :loading="exportLoading" @click="handleExport">
            <template #icon>
              <global-icon type="folder-open" style="color:orange"/>
            </template>
            {{ localeContent('m.common.button.export') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:confirm']">
          <a-button size="small" :loading="confirmLoading" @click="handleConfirm">
            <template #icon>
              <global-icon type="check" style="color:green"/>
            </template>
            确认
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:invalidate']">
          <a-button size="small" :loading="invalidateLoading" @click="handleInvalidate">
            <template #icon>
              <global-icon type="close-square" style="color:red"/>
            </template>
            作废
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:sendAudit']">
          <a-button size="small" :loading="sendAuditLoading" @click="handleSendAudit">
            <template #icon>
              <global-icon type="cloud" style="color:deepskyblue"/>
            </template>
            发送审核
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:copyVersion']">
          <a-button size="small" :loading="versionCopyLoading" @click="handleVersionCopy">
            <template #icon>
              <global-icon type="snippets" style="color:deepskyblue"/>
            </template>
            版本复制
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice:printOrderTable']">
          <a-dropdown :disabled="printOrderFormLoading">
            <template #overlay>
              <a-menu @click="handlePrintOrderForm">
                <a-menu-item key="PDF">打印采购订单表(PDF)</a-menu-item>
                <a-menu-item key="XLSX">打印采购订单表(XLSX)</a-menu-item>
              </a-menu>
            </template>
            <a-button class="button" size="small" :loading="printOrderFormLoading">
              <template #icon>
                <GlobalIcon type="cloud-download" style="color:blue"/>
              </template>
              打印采购订单表
            </a-button>
          </a-dropdown>
        </div>
        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <cs-table-col-settings
            :resId="tableKey"
            :tableKey="tableKey + '-orderNotice'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </cs-table-col-settings>
        </div>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item remove-table-border-add-bg"
          size="small"
          bordered
          column-drag
          :custom-row="customRow"
          :pagination="false"
          :columns="showColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          :style="listTableStyle"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, record, text}">
            <template v-if="column.dataIndex === 'operation'">
              <div class="operation-container">
                <a-button
                  v-if="record.dataStatus === '0'"
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  <template #icon>
                    <global-icon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="record.dataStatus === '0' ? operationEdit('view') : undefined"
                >
                  <template #icon>
                    <global-icon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>
              </div>
            </template>

            <template v-else-if="column.dataIndex === 'customer'">
              <span>{{ cmbShowRender(text, customerOptions) }}</span>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页区域 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>

    <!-- tabs -->
    <order-notice-tabs v-if="!show"/>

    <!-- 新增模态框 -->
    <order-notice-add-modal ref="addModalRef" @select-contract-no="handleSelectContractNo"/>
  </section>
</template>

<script setup>
import { GlobalIcon } from '@/components/icon'
import CsTableColSettings from '@/components/settings/CsTableColSettings.vue'
import BreadCrumb from '@/components/breadcrumb/BreadCrumb.vue'
import OrderNoticeSearch from '@/view/auxiliaryMaterials/orderNotice/OrderNoticeSearch.vue'
import OrderNoticeTabs from '@/view/auxiliaryMaterials/orderNotice/OrderNoticeTabs.vue'
import OrderNoticeAddModal from '@/view/auxiliaryMaterials/orderNotice/OrderNoticeAddModal.vue'
import { onBeforeMount, ref, provide, createVNode, watch, onBeforeUnmount, computed, watchEffect, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import { message, Modal } from 'ant-design-vue'
import ycCsApi from '@/api/ycCsApi'
import { useCommon } from '@/view/common/useCommon'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import { localeContent } from '@/view/utils/commonUtil'
import { getColumns } from '@/view/auxiliaryMaterials/orderNotice/OrderNoticeColumns.jsx'
import { editStatus, DATA_STATUS } from '@/view/common/constant'
import {
  getAllCustomers, getAddData, deleteHead, confirmHead, invalidateHead, checkVersionCopy
  , versionCopy, printOrderForm, getPortOptions
} from '@/api/auxiliaryMaterials/orderNotice'
import { observe, unobserve } from '@/utils/observe'

const {
  ajaxUrl,
  showSearch,
  headSearch,
  handleShowSearch,
  handlerSearch,
  handlerRefresh,
  getList,
  show,
  tableLoading,
  dataSourceList,
  gridData,
  editConfig,
  onSelectChange,
  operationEdit,
  page,
  onPageChange,
  exportLoading,
  doExport
} = useCommon()

const {cmbShowRender} = useColumnsRender()

/**
 * 检查选择行
 * @param operation 操作
 * @param multi 是否多选
 * @returns {Promise<unknown>} promise
 */
function checkSelectedRows(operation, multi = true) {
  const keys = gridData.selectedRowKeys
  return new Promise((resolve, reject) => {
    if (keys.length <= 0) {
      reject(`请选择一条要${operation}的数据`)
      return
    }
    if (keys.length > 1 && !multi) {
      reject(`只能选择一条数据${operation}`)
      return
    }
    resolve(multi ? {keys, rows: gridData.selectedData} : {
      key: keys[0],
      row: gridData.selectedData.filter(item => item['id'] === keys[0])[0]
    })
  })
}

// 配置表格列、导出列
const {tableColumns, excelColumns} = getColumns()

// 原始显示列
const originalColumns = ref((() => {
  for (let columns of tableColumns) {
    columns.visible = true
  }
  return tableColumns
})())

// 当前显示列
const showColumns = ref([...originalColumns.value])

// 自定义显示列更改回调
function customColumnChange(settingColumns) {
  showColumns.value = settingColumns.filter(item => item.visible === true)
}

// 表格唯一key
const tableKey = ref(window['$vueApp'] ? window.majesty.router.currentRoute.value.path : useRoute().path)

// 请求url
ajaxUrl.selectAllPage = ycCsApi.auxiliaryMaterials.orderNotice.head.list
ajaxUrl.exportUrl = ycCsApi.auxiliaryMaterials.orderNotice.head.export

// 客户选项列表
const customerOptions = ref([])

// 当前页面客户选项列表
const customerPageOptions = ref([])

const stopWatchDataSourceList = watch(dataSourceList, () => {
  getAllCustomers(headSearch.value.searchParam).then(res => {
    customerPageOptions.value = customerOptions.value.filter(item => (res.data || []).includes(item['value']))
  })
})

/**
 * 初始化客户选项列表
 */
async function initCustomerOptions() {
  const {data} = await window.majesty.httpUtil.postAction(ycCsApi.bizMerchant.list, {})
  if (!data || !data.length || data.length <= 0) {
    return
  }
  customerOptions.value = data.map(item => {
    return {
      value: item.merchantCode,
      label: item.merchantNameCn
    }
  })
}

// 港口选项
const portOptions = ref([])

/**
 * 初始化港口选项
 */
async function initPortOptions() {
  const res = await getPortOptions()
  if (!res.success) {
    message.warn('初始化港口选项失败')
    return
  }
  if (res.data) {
    portOptions.value = res.data
  }
}

/**
 * 自定义行
 * @param record 数据记录
 * @returns {{onDblclick: *, style: {cursor: string}}} 行配置
 */
function customRow(record) {
  return {
    onDblclick: () => {
      handleRowDblclick(record)
    },
    style: {cursor: 'pointer'}
  }
}

/**
 * 双击行
 * @param record 数据记录
 */
function handleRowDblclick(record) {
  if (DATA_STATUS.DRAFT !== record.dataStatus) {
    message.warn('仅编制状态数据支持编辑操作')
    return
  }
  handleEditByRow(record)
}

// 新增模态框ref
const addModalRef = ref(null)

/**
 * 导出
 */
function handleExport() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`国营贸易进口辅料-订货通知${timestamp}.xlsx`, ref(excelColumns))
}

/**
 * 新增
 */
function handleAdd() {
  addModalRef.value.openModal()
}

/**
 * 编辑
 */
function handleEdit() {
  checkSelectedRows('编辑', false).then(selected => {
    const {row} = selected
    if (DATA_STATUS.DRAFT !== row.dataStatus) {
      message.warn('仅编制状态数据支持编辑操作')
      return
    }
    handleEditByRow(selected.row)
  }).catch(error => {
    message.warn(error)
  })
}

// 删除
const deleteLoading = ref(false)

async function handleDelete() {
  const doDelete = async (id) => {
    deleteLoading.value = true
    try {
      const res = await deleteHead(id)
      if (res.success) {
        message.success('删除成功')
        getList()
      } else {
        message.error(res.message)
      }
    } finally {
      deleteLoading.value = false
    }
  }
  try {
    const {key, row} = await checkSelectedRows('删除', false)
    if (row.dataStatus !== DATA_STATUS.DRAFT) {
      message.error('仅编制状态数据允许删除')
      return
    }
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk: async () => {
        await doDelete(key)
      }
    })
  } catch (reason) {
    message.warn(reason)
  }
}

// 确认
const confirmLoading = ref(false)

async function handleConfirm() {
  const doConfirm = async (id) => {
    confirmLoading.value = true
    try {
      const res = await confirmHead(id)
      if (res.success) {
        message.success('确认成功')
        getList()
      } else {
        message.error(res.message)
      }
    } finally {
      confirmLoading.value = false
    }
  }
  try {
    const {key, row} = await checkSelectedRows('确认', false)
    if (row.dataStatus === DATA_STATUS.CONFIRMED) {
      message.warn('该数据已确认，无需重复操作')
      return
    }
    if (row.dataStatus === DATA_STATUS.INVALID) {
      message.error('该数据已作废，不允许确认')
      return
    }
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: '是否确认所选项？',
      onOk: async () => {
        await doConfirm(key)
      }
    })
  } catch (reason) {
    message.warn(reason)
  }
}

// 作废
const invalidateLoading = ref(false)

async function handleInvalidate() {
  const doInvalidate = async (id) => {
    invalidateLoading.value = true
    try {
      const res = await invalidateHead(id)
      if (res.success) {
        message.success('作废成功')
        getList()
      } else {
        message.error(res.message)
      }
    } finally {
      invalidateLoading.value = false
    }
  }
  try {
    const {key, row} = await checkSelectedRows('作废', false)
    if (row.dataStatus === DATA_STATUS.INVALID) {
      message.warn('该数据已作废，无需重复操作')
      return
    }
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '作废',
      cancelText: '取消',
      content: '是否作废所选项？',
      onOk: async () => {
        await doInvalidate(key)
      }
    })
  } catch (reason) {
    message.warn(reason)
  }
}

// 版本复制
const versionCopyLoading = ref(false)

function handleVersionCopy() {
  const doVersionCopy = async (row) => {
    versionCopyLoading.value = true
    try {
      const {data} = await checkVersionCopy(row.orderNo)
      if ('1' === data) {
        Modal.confirm({
          title: '提醒',
          icon: createVNode(ExclamationCircleOutlined),
          okText: '确认',
          cancelText: '取消',
          content: '当前单据存在有效数据，是否将其作废并重新生成一份新数据？',
          onOk: async () => {
            await actionVersionCopy(row)
          }
        })
      } else {
        await actionVersionCopy(row)
      }
    } finally {
      versionCopyLoading.value = false
    }
  }
  const actionVersionCopy = async (row) => {
    const res = await versionCopy(row)
    if (res.success) {
      message.success('版本复制成功')
      getList()
    } else {
      message.error(res.message)
    }
  }
  checkSelectedRows('版本复制', false).then(selected => {
    const {row} = selected
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '版本复制',
      cancelText: '取消',
      content: '是否版本复制所选项？',
      onOk: async () => {
        await doVersionCopy(row)
      }
    })
  }).catch(error => {
    message.warn(error)
  })
}

// 发送审核
const sendAuditLoading = ref(false)

function handleSendAudit() {
  message.success('发送审核敬请期待')
}

// 打印采购订单表
const printOrderFormLoading = ref(false)

function handlePrintOrderForm(value) {
  checkSelectedRows('打印采购订单表', true).then(async selected => {
    printOrderFormLoading.value = true
    try {
      const {keys} = selected
      await printOrderForm(value.key, keys)
      message.success('打印采购订单表成功')
      gridData.selectedData = []
      gridData.selectedRowKeys = []
    } finally {
      printOrderFormLoading.value = false
    }
  }).catch(error => {
    message.warn(error)
  })
}

/**
 * 行内编辑
 * @param record 数据记录
 */
function handleEditByRow(record) {
  show.value = false
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = record
  editConfig.value.headId = record.id
  editConfig.value.contractIds = (record['buyHeadIds'] || '').split(',')
}

/**
 * 行内查看
 * @param record 数据记录
 */
function handleViewByRow(record) {
  show.value = false
  editConfig.value.editStatus = editStatus.SHOW
  editConfig.value.editData = record
  editConfig.value.headId = record.id
  editConfig.value.contractIds = (record['buyHeadIds'] || '').split(',')
}

/**
 * 返回
 * @param requireFlush 是否需要刷新
 * @param reselect 重新选择购销合同
 */
function handleBack(requireFlush, reselect = false) {
  show.value = true
  editConfig.value.editStatus = editStatus.SHOW
  editConfig.value.headId = ''
  editConfig.value.editData = {}
  if (requireFlush) {
    getList()
  }
  if (reselect) {
    addModalRef.value.openModal()
  }
}

/**
 * 选择购销合同号
 */
async function handleSelectContractNo(contractIds) {
  try {
    const res = await getAddData(contractIds)
    if (!res.success) {
      message.error(res.msg)
      return
    }
    addModalRef.value.closeModal()
    editConfig.value.editStatus = editStatus.ADD
    editConfig.value.editData = res.data
    editConfig.value.contractIds = contractIds
    show.value = false
  } finally {
    addModalRef.value.selectLoading = false
  }
}

// 列表表格样式
const listTableStyle = ref({
  minHeight: '300px'
})

// section高度
const dcSectionHeight = ref(0)
const dcSectionRef = ref(null)

watchEffect(() => {
  const currentHeight = dcSectionHeight.value - 120 - (showSearch.value ? 54 : 0)
  listTableStyle.value.minHeight = Math.max(currentHeight, 300) + 'px'
})


// 编辑数据
provide('edit', {
  config: editConfig,
  back: handleBack,
  toEdit: (data) => {
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData = data
    editConfig.value.headId = data.id
    editConfig.value.contractIds = (data['buyHeadIds'] || '').split(',') || []
  },
  toShow: () => {
    if (editConfig.value.editStatus === editStatus.EDIT) {
      editConfig.value.editStatus = editStatus.SHOW
    }
  },
  commonAddFlag: computed(() => editConfig.value.editStatus === editStatus.ADD),
  commonShowFlag: computed(() => editConfig.value.editStatus === editStatus.SHOW)
})

// 客户选项
provide('customerOptions', {
  page: customerPageOptions,
  all: customerOptions,
})

// 港口选项
provide('portOptions', portOptions)

onBeforeMount(() => {
  getList()
  initCustomerOptions()
  initPortOptions()
})

onMounted(() => {
  observe(dcSectionRef.value, config => {
    dcSectionHeight.value = config.height
  })
})

onBeforeUnmount(() => {
  stopWatchDataSourceList()
  unobserve(dcSectionRef.value)
})

defineOptions({
  name: 'OrderNoticeList'
})
</script>
