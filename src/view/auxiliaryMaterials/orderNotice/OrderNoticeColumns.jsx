import { Tag } from 'ant-design-vue'
import { baseColumns } from '@/view/common/baseColumns'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import { productClassify, DATA_STATUS } from '@/view/common/constant'

const {baseColumnsExport, baseColumnsShow} = baseColumns()

const {cmbShowRender} = useColumnsRender()

export function getColumns() {
  const commColumns = [
    'id',
    'businessType',
    'orderNo',
    'orderDate',
    'customer',
    'versionNo',
    'createByName',
    'documentMakeTime',
    'dataStatus'
  ]

  // 导出字段设置
  const excelColumnsConfig = [
    ...commColumns,
    ...baseColumnsExport
  ]

  // 表格字段设置
  const columnsConfig = [
    ...commColumns,
    ...baseColumnsShow
  ]

  // table表格字段设置
  const totalColumns = [
    {
      width: 100,
      minWidth: 70,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left',
    },
    {
      minWidth: 120,
      title: '业务类型',
      align: 'center',
      dataIndex: 'businessType',
      resizable: true,
      key: 'businessType',
      customRender: ({text}) => {
        return (<span>{cmbShowRender(text, productClassify.businessType)}</span>)
      }
    },
    {
      minWidth: 140,
      title: '订货编号',
      align: 'center',
      dataIndex: 'orderNo',
      resizable: true,
      key: 'orderNo'
    },
    {
      minWidth: 100,
      title: '订货日期',
      align: 'center',
      dataIndex: 'orderDate',
      resizable: true,
      key: 'orderDate'
    },
    {
      minWidth: 160,
      title: '客户',
      align: 'center',
      dataIndex: 'customer',
      resizable: true,
      key: 'customer',
      customRender: ({record}) => {
        return (<span>{`${record['customer']} ${record['customerName'] || ''}`}</span>)
      }
    },
    {
      title: '版本号',
      minWidth: 80,
      align: 'center',
      dataIndex: 'versionNo',
      resizable: true,
      key: 'versionNo'
    },
    {
      title: '制单人',
      minWidth: 100,
      align: 'center',
      dataIndex: 'createByName',
      resizable: true,
      key: 'createByName'
    },
    {
      minWidth: 120,
      title: '制单时间',
      align: 'center',
      dataIndex: 'documentMakeTime',
      resizable: true,
      key: 'documentMakeTime'
    },
    {
      title: '单据状态',
      minWidth: 110,
      align: 'center',
      dataIndex: 'dataStatus',
      resizable: true,
      key: 'dataStatus',
      customRender: ({text}) => {
        const color = text === DATA_STATUS.DRAFT ? 'success' : (text === DATA_STATUS.CONFIRMED ? 'processing' : 'error')
        return (<Tag color={color}>{cmbShowRender(text, productClassify.data_status)}</Tag>)
      }
    },
  ]

  return {
    tableColumns: totalColumns.filter(item => columnsConfig.includes(item.key)),
    excelColumns: totalColumns.filter(item => excelColumnsConfig.includes(item.key))
  }
}
