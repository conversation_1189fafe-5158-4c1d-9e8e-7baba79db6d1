<template>
  <a-modal v-model:open="open" title="新增明细" :keyboard="false" :width="1200"
           :mask-closable="false" :destroy-on-close="false">
    <div class="cs-action">
      <!-- 表格区域 -->
      <a-table
        ref="tableRef"
        :loading="tableLoading"
        :columns="columns"
        :custom-row="customRow"
        :data-source="datasourceList"
        :row-selection="{ selectedRowKeys: selectedKeys , onChange: onSelectChange }"
        row-key="id"
        :scroll="{ y: 400}"
        :pagination="false"
        size="small"
        style="font-size: 12px"
      >
        <!-- 操作 -->
        <template #bodyCell="{ column, record, text}">
          <template v-if="column.dataIndex === 'supplier'">
            <span>{{ cmbShowRender(text, customerOptions.all.value) }}</span>
          </template>
        </template>
      </a-table>
    </div>

    <template #footer>
      <a-button type="primary" @click="handleSave" :loading="saveLoading">保存</a-button>
      <a-button @click="closeModal">返回</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { onBeforeUnmount, ref, shallowRef, watch, inject, toRaw } from 'vue'
import { message } from 'ant-design-vue'
import columns from '@/view/auxiliaryMaterials/orderNotice/body/OrderNoticeBodyAddDetailColumns'
import { getAddListDetailData } from '@/api/auxiliaryMaterials/orderNotice'
import { useColumnsRender } from '@/view/common/useColumnsRender'

const edit = inject('edit')
const customerOptions = inject('customerOptions')
const getCurrentAddBodyList = inject('getCurrentAddBodyList')

const { cmbShowRender } = useColumnsRender()

// 通用新增标识
const commonAddFlag = edit.commonAddFlag

const emit = defineEmits(['addListDetail'])

// 表格loading
const tableLoading = ref(false)

// 保存按钮loading
const saveLoading = ref(false)

// 打开状态
const open = ref(false)

/**
 * 打开模态框
 */
function openModal() {
  open.value = true
}

/**
 * 关闭模态框
 */
function closeModal() {
  open.value = false
}

// open侦听器
const stopWatchOpen = watch(open, (openStatus) => {
  if (openStatus) {
    saveLoading.value = false
    selectedKeys.value = []
    getList()
  }
})

// 选中键列表
const selectedKeys = ref([])

/**
 * 选中行变化回调函数
 * @param selectedRowKeys 行keys
 */
function onSelectChange(selectedRowKeys) {
  selectedKeys.value = selectedRowKeys
}

/**
 * 自定义行
 * @param record 数据记录
 * @returns {{onDblclick: *, style: {cursor: string}}} 行配置
 */
function customRow(record) {
  return {
    onDblclick: () => {
      const currentKeys = selectedKeys.value
      if (currentKeys['includes'](record.id)) {
        selectedKeys.value = currentKeys.filter(key => key !== record.id)
      } else {
        currentKeys.push(record.id)
      }
    },
    style: {cursor: 'pointer'}
  }
}

// 数据源列表
const datasourceList = shallowRef([])

/**
 * 获取数据列表
 */
async function getList() {
  tableLoading.value = true
  try {
    const res = await getAddListDetailData(edit.config.value['contractIds'])
    const bodyList = getCurrentAddBodyList() || []
    if (res.success) {
      if (commonAddFlag.value) {
        const targetArr = res.data || []
        datasourceList.value = targetArr.filter(item => !(bodyList.map(item => item.id).includes(item.id)))
        return
      }
      datasourceList.value = (res.data || []).filter(item => !(bodyList.map(item => item['buyListId']).includes(item.id)))
    } else {
      message.error(res.message)
    }
  } finally {
    tableLoading.value = false
  }
}

/**
 * 保存
 */
async function handleSave() {
  if (selectedKeys.value.length < 1) {
    message.warn('请选择表体数据')
    return
  }
  const ids = toRaw(selectedKeys.value) || []
  saveLoading.value = true
  emit('addListDetail', commonAddFlag.value ? datasourceList.value.filter(item => ids.includes(item.id)) : ids)
}

onBeforeUnmount(() => {
  stopWatchOpen()
})

defineExpose({openModal, closeModal, saveLoading})

defineOptions({
  name: 'OrderNoticeBodyAddDetailModal'
})
</script>
