<template>
  <section class="dc-section">
    <!-- 操作按钮区域 -->
    <div v-if="commonShowFlag" style="margin-bottom: 20px"/>
    <div v-else class="cs-action-btn">
      <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice-list:addDetail']">
        <a-button size="small" @click="handleAddDetail">
          <template #icon>
            <global-icon type="plus" style="color:green"/>
          </template>
          {{ '新增明细' }}
        </a-button>
      </div>
      <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-orderNotice-list:delete']">
        <a-button size="small" :loading="deleteLoading" @click="handleDelete">
          <template #icon>
            <global-icon type="delete" style="color:red"/>
          </template>
          {{ localeContent('m.common.button.delete') }}
        </a-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <s-table
      :animate-rows="false"
      ref="tableRef"
      column-drag
      class="remove-table-border-add-bg"
      size="small"
      bordered
      :pagination="false"
      :columns="showColumns"
      :data-source="datasourceList"
      :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
      :loading="tableLoading"
      row-key="id"
      :style="tableStyle"
    >
      <template #bodyCell="{ column, text, record, index}">
        <div v-if="['qty'].includes(column.dataIndex)">
          <a-input-number
            v-if="editableData[record.id]"
            v-model:value="editableData[record.id][column.dataIndex]"
            size="small"
            :controls="false"
            :formatter="formatter"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
            :precision="column.precision"
            style="width: 100%"
            @change="qtyChange"
          />
          <span v-else>{{ formatSpecifiedNumber(datasourceList[index].qty, true, 2) }}</span>
        </div>

        <div v-else-if="['transportMode', 'port'].includes(column.dataIndex)">
          <cs-select v-if="editableData[record.id]" optionFilterProp="label" option-label-prop="key" allow-clear
                     show-search v-model:value="editableData[record.id][column.dataIndex]" style="width: 100%">
            <a-select-option class="cs-select-dropdown" v-for="item in selectOptions[column.dataIndex]"
                             :key="`${item.value} ${item.label}`" :value="item.value"
                             :label="`${item.value}${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
          <span v-else>
             {{ cmbShowRender(datasourceList[index][column.dataIndex], selectOptions[column.dataIndex]) }}
          </span>
        </div>

        <div v-else-if="column.dataIndex === 'requestDeliveryDate'">
          <a-date-picker
            v-if="editableData[record.id]"
            v-model:value="editableData[record.id][column.dataIndex]"
            :valueFormat="DATE_FORMAT.DATE"
            :format="DATE_FORMAT.DATE"
            :locale="zhCN"
            placeholder=""
            size="small"
            style="width: 100%"
          />
          <span v-else>{{ datasourceList[index].requestDeliveryDate }}</span>
        </div>

        <div v-else-if="column.dataIndex === 'supplier'">
          <span>{{ cmbShowRender(text, selectOptions.supplier) }}</span>
        </div>

        <div v-else-if="column.dataIndex === 'unit'">
          <span>{{ cmbShowRender(text, selectOptions.unit) }}</span>
        </div>
      </template>
    </s-table>

    <!-- 分页区域 -->
    <div class="cs-pagination">
      <div class="cs-margin-right cs-list-total-data">
        总数量：{{ showTotalQty }}
      </div>
      <div class="count-number">
        <span>共 {{ page.total }} 条</span>
      </div>
      <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                    :total="page.total" @change="onPageChange">
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
  </section>
  <order-notice-body-add-detail-modal ref="addDetailModalRef" @add-list-detail="handleAddListDetail"/>
</template>

<script setup>
import OrderNoticeBodyAddDetailModal from '@/view/auxiliaryMaterials/orderNotice/body/OrderNoticeBodyAddDetailModal.vue'
import CsSelect from '@/components/select/CsSelect.vue'
import { GlobalIcon } from '@/components/icon'
import { computed, createVNode, inject, onBeforeUnmount, onMounted, provide, ref, toRaw, watch, watchEffect } from 'vue'
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN'
import { message, Modal } from 'ant-design-vue'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import { localeContent } from '@/view/utils/commonUtil'
import { getColumns } from '@/view/auxiliaryMaterials/orderNotice/body/OrderNoticeBodyColumns'
import { deepClone } from '@/view/utils/common'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import { usePCode } from '@/view/common/usePCode'
import { DATE_FORMAT, productClassify } from '@/view/common/constant'
import ycCsApi from '@/api/ycCsApi'
import { debounced, removeDebounced } from '@/view/auxiliaryMaterials/orderNotice/body/debounced'
import { addListDetail, deleteList, getSummaryList, updateList } from '@/api/auxiliaryMaterials/orderNotice'

const tabHeight = inject('tabHeight')
const currentEdit = inject('edit')
const customerOptions = inject('customerOptions')
const portOptions = inject('portOptions')
const {pCode, getPCode} = usePCode()

const {cmbShowRender, formatSpecifiedNumber} = useColumnsRender()

// 通用显示标识
const commonShowFlag = currentEdit.commonShowFlag
// 通用新增标识
const commonAddFlag = currentEdit.commonAddFlag

// 显示列
const showColumns = ref(getColumns().tableColumns.map(item => {
  item.visible = true
  return item
}))

// 表格样式
const tableStyle = ref({
  minHeight: '180px'
})

// 表格高度副作用函数
const stopWatchHeight = watchEffect(() => {
  const curHeight = tabHeight.value - 380 + (commonShowFlag.value ? 20 : 0)
  tableStyle.value.minHeight = (curHeight < 180 ? 180 : curHeight) + 'px'
})

// 显示列副作用函数
const stopWatchColumns = watchEffect(() => {
  if (commonShowFlag.value) {
    showColumns.value = showColumns.value.filter(item => item.dataIndex !== 'operation')
  }
})

// 表格loading
const tableLoading = ref(false)

// 表格数据源
const datasourceList = ref([])

// 网格数据
const gridData = ref({
  selectedRowKeys: [],
  selectedData: []
})

/**
 * 重置网格数据
 */
function resetGridData() {
  gridData.value = {
    selectedRowKeys: [],
    selectedData: []
  }
}

// 选择选项
const selectOptions = computed(() => {
  return {
    transportMode: productClassify.transportMode,
    supplier: customerOptions.all.value,
    port: portOptions['value'],
    unit: (() => {
      if (!pCode.value || !pCode.value['UNIT']) {
        return []
      }
      const units = toRaw(pCode.value['UNIT'])
      return Object.keys(units).map(key => {
        return {
          value: key,
          label: units[key]
        }
      })
    })()
  }
})

// 格式化数字（整数千分位分隔）
function formatter(value) {
  if (value === 0) {
    return '0'
  }
  if (!value) {
    return ''
  }
  const parts = value.toString().split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return parts.join('.')
}

// 分页信息
const page = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

/**
 * 分页变化函数
 * @param pageNumber 页号
 * @param pageSize 页大小
 */
function onPageChange(pageNumber, pageSize) {
  page.value.current = pageNumber
  page.value.pageSize = pageSize
  if (commonAddFlag.value) {
    updateAddList()
  } else {
    getList()
  }
}

/**
 * 获取列表
 * @returns {Promise<void>} get req promise
 */
async function getList() {
  const listUrl = ycCsApi.auxiliaryMaterials.orderNotice.body.list
  const completeUrl = `${listUrl}?page=${page.value.current}&limit=${page.value.pageSize}`
  const params = {headId: currentEdit.config.value['headId']}
  tableLoading.value = true
  try {
    const {data, total, pageIndex} = await window.majesty.httpUtil.postAction(completeUrl, params)
    page.value.total = total
    page.value.current = pageIndex
    datasourceList.value = data
    await updateTotalQty()
    syncEditData()
  } finally {
    tableLoading.value = false
    resetGridData()
  }
}

/**
 * 选中行变化回调函数
 * @param selectedRowKeys 行keys
 * @param rowSelectData 选中数据
 */
function onSelectChange(selectedRowKeys, rowSelectData) {
  gridData.value.selectedData = rowSelectData
  gridData.value.selectedRowKeys = selectedRowKeys
}

// 编辑数据
const editableData = ref({})

// 新增明细模态框ref
const addDetailModalRef = ref(null)

/**
 * 新增明细
 */
function handleAddDetail() {
  addDetailModalRef.value.openModal()
}

/**
 * 删除数据
 */
const deleteLoading = ref(false)

function handleDelete() {
  if (gridData.value.selectedRowKeys.length <= 0) {
    message.warn('请选择一条或多条要删除的数据')
    return
  }
  const doDelete = async () => {
    const ids = gridData.value.selectedRowKeys
    if (commonAddFlag.value) {
      ids.forEach(id => {
        if (editableData.value[id]) {
          delete editableData.value[id]
        }
        addOriginDatasourceList = addOriginDatasourceList.filter(item => item.id !== id)
        updateAddList()
      })
      return
    }
    deleteLoading.value = true
    try {
      const res = await deleteList(ids)
      if (res.success) {
        message.success('删除成功')
      }
      await getList()
    } finally {
      deleteLoading.value = false
    }
  }
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk: async () => {
      await doDelete()
    }
  })
}

let addOriginDatasourceList = []

/**
 * 获取新增列表
 * @returns {*[]} 新增数据列表
 */
function getAddList() {
  addOriginDatasourceList.forEach(item => {
    // 只更新可编辑栏位
    if (editableData.value.hasOwnProperty(item.id)) {
      const editData = editableData.value[item.id]
      item.transportMode = editData['transportMode']
      item.port = editData['port']
      item.qty = editData['qty']
      item.requestDeliveryDate = editData['requestDeliveryDate']
    }
  })
  return addOriginDatasourceList
}

/**
 * 更新新增列表
 */
function updateAddList() {
  page.value.total = addOriginDatasourceList.length
  const startIndex = (page.value.current - 1) * page.value.pageSize
  const endIndex = Math.min(startIndex + page.value.pageSize, addOriginDatasourceList.length)
  datasourceList.value = addOriginDatasourceList.slice(startIndex, endIndex)
  updateTotalQty()
}

/**
 * 新增明细处理
 */
async function handleAddListDetail(data) {
  const validateGNameDuplicates = () => {
    return new Promise((resolve, reject) => {
      const countTable = new Map(), productNames = addOriginDatasourceList
        .map(item => item.productName).filter(item => Boolean(item))
      for (let item of data) {
        const productName = item.productName
        if (!Boolean(productName)) {
          continue
        }
        countTable.set(productName, (countTable.get(productName) || 0) + 1)
      }
      for (let productName of countTable.keys()) {
        const count = countTable.get(productName)
        if (count > 1) {
          reject(`商品名称为 ${productName} 的明细重复选择`)
          return
        }
      }
      for (let item of data) {
        if (productNames.includes(item.productName)) {
          reject(`商品名称为 ${item.productName} 的表体已存在`)
          return
        }
      }
      resolve()
    })
  }
  try {
    if (commonAddFlag.value) {
      validateGNameDuplicates().then(() => {
        addDetailModalRef.value.closeModal()
        resetGridData()
        addOriginDatasourceList = data.concat(addOriginDatasourceList)
        data.forEach(item => editableData.value[item.id] = deepClone(item))
        updateAddList()
      }).catch(error => {
        message.error(error)
      })
      return
    }
    const res = await addListDetail({
      id: currentEdit.config.value['headId'],
      contractIds: currentEdit.config.value['contractIds'],
      contractListIds: data || []
    })
    if (res.success) {
      message.success('新增明细成功')
      addDetailModalRef.value.closeModal()
      await getList()
    } else {
      message.error(res.message)
    }
  } finally {
    addDetailModalRef.value.saveLoading = false
  }
}

// 新增总数量
const addTotalQty = ref(0)
// 总数量
const totalQty = ref(0)

// 显示总数量
const showTotalQty = computed(() => {
  const qty = commonAddFlag.value ? Number(addTotalQty.value) : Number(totalQty.value)
  return formatSpecifiedNumber(String(qty || 0), true, 2)
})

/**
 * 数量更改
 */
function qtyChange() {
  updateTotalQty(true)
}

/**
 * 更新总数量
 * @param manual 是否手工录入
 * @param data 数量数据，包含新旧值
 */
async function updateTotalQty(manual = false, data = undefined) {
  if (commonAddFlag.value) {
    addTotalQty.value = addOriginDatasourceList.map(item => (editableData.value.hasOwnProperty(item.id) ?
      Number(editableData.value[item.id].qty) : Number(item.qty)) || 0).reduce((acc, n) => acc + n, 0)
    return
  }
  if (!manual) {
    await getTotalQty()
  } else {
    if (data) {
      markUpDiffTotalQty(data.newQty, data.oldQty)
    }
  }
}

/**
 * 获取总数量
 */
async function getTotalQty() {
  const params = {headId: currentEdit.config.value['headId']}
  const {data: summaryData} = await getSummaryList(params)
  totalQty.value = Number(summaryData['totalQty']) || 0
}

/**
 * 总数量补足差异化
 * @param newQty 新数量
 * @param oldQty 旧数量
 */
function markUpDiffTotalQty(newQty, oldQty) {
  totalQty.value = totalQty.value - (oldQty || 0) + (newQty || 0)
}

// 表格监听计数器
const tableWatcherCounter = new Map()

/**
 * 清理编辑数据
 */
function clearEditData() {
  if (commonAddFlag.value) {
    return
  }
  // 清理
  if (editableData.value) {
    const keys = Object.keys(editableData.value)
    keys.forEach(key => {
      if (tableWatcherCounter.has(key)) {
        tableWatcherCounter.delete(key)
      }
      removeDebounced(key)
      if (editableData.value.hasOwnProperty(key)) {
        const editData = editableData.value[key]
        if (editData['stopWatch'] && typeof editData['stopWatch'] === 'function') {
          editData['stopWatch']()
        }
      }
    })
  }
}

/**
 * 同步编辑数据
 */
function syncEditData() {
  if (commonShowFlag.value) {
    return
  }
  // 清理编辑数据
  clearEditData()
  // 重新同步编辑数据
  const unprocessed = {}
  const ids = []
  datasourceList.value.forEach(item => {
    const cloneObj = deepClone(toRaw(item))
    cloneObj['debounced'] = debounced(item.id)
    tableWatcherCounter.set(item.id, 0)
    unprocessed[item.id] = cloneObj
    ids.push(item.id)
  })
  editableData.value = unprocessed
  // 监听更改
  ids.forEach(id => {
    editableData.value[id]['stopWatch'] = watch(() => editableData.value[id], editData => {
      // 增加计数
      tableWatcherCounter.set(id, (tableWatcherCounter.get(id) || 0) + 1)
      // 如果是第一次effect执行，则跳过
      if (tableWatcherCounter.get(id) === 1) {
        return
      }
      editData['debounced'](async () => {
        // 请求更改
        const res = await updateList(id, editData)
        if (res && !res.success) {
          message.error('修改失败')
          return
        }
        // 更新源数据源
        const index = datasourceList.value.findIndex(item => item.id === id)
        const oldQty = datasourceList.value[index].qty, newQty = editData.qty
        datasourceList.value[index] = res.data
        // 更改总数量
        await updateTotalQty(true, {newQty, oldQty})
      }, 1000)()
    }, {deep: true, immediate: false})
  })
}

/**
 * 变更为只读状态
 */
function toReadonly() {
  currentEdit.toShow()
  clearEditData()
  editableData.value = {}
  getList()
}

provide('getCurrentAddBodyList', () => addOriginDatasourceList)

onMounted(() => {
  const editConfig = currentEdit.config.value
  // 新增状态，则初始化表体明细
  if (commonAddFlag.value) {
    addOriginDatasourceList = editConfig['editData']['bodyList'] || []
    addOriginDatasourceList.forEach(item => editableData.value[item.id] = deepClone(toRaw(item)))
    updateAddList()
  } else {
    getList()
  }
  getPCode()
})

onBeforeUnmount(() => {
  stopWatchHeight()
  stopWatchColumns()
  clearEditData()
})

defineExpose({getAddList, getList, toReadonly})

defineOptions({
  name: 'OrderNoticeBodyList'
})
</script>
