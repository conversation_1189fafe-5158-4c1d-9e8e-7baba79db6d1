<template>
  <section>
    <a-card size="small" title="进货单表头" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType2"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="purchaseOrderNo" :label="'进货单号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.purchaseOrderNo"></a-input>
          </a-form-item>

          <a-form-item name="supplier" :label="'供应商'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.supplier" id="supplier">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="invoiceNo" :label="'发票号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.invoiceNo"></a-input>
          </a-form-item>

          <a-form-item name="portOfDeparture"   :label="'启运港'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable || (isEdit  === false)"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.portOfDeparture" id="portOfDeparture">
              <a-select-option class="cs-select-dropdown" v-for="item in portList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="portOfDestination" :label="'目的地/港'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' " optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.portOfDestination" id="portOfDestination">
              <a-select-option v-for="item in portList" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="vesselVoyage" :label="'船名航次'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.vesselVoyage"></a-input>
          </a-form-item>
          <a-form-item name="sailingDate" :label="'开航日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.sailingDate"
              id="sailingDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="salesDate" :label="'作销日期'" class="grid-item" :colon="false">
            <a-date-picker :disabled="showDisable" v-model:value="formData.salesDate" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="priceTerm" :label="'价格条款'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.priceTerm" id="priceTerm">
              <a-select-option class="cs-select-dropdown" v-for="item in priceTermList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="priceTermPort" :label="'价格条款对应港口'" class="grid-item" :colon="false">
            <cs-select :disabled="true" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.priceTermPort" id="priceTermPort">
              <a-select-option v-for="item in priceTermPortOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.curr"
              :disabled="showDisable || formData.status  !== '0' "
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
            ></a-select>
          </a-form-item>
          <a-form-item name="totalAmount" :label="'金额'" class="grid-item" :colon="false">
            <a-input-number :disabled="true" size="small" v-model:value="formData.totalAmount" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="totalQuantity" :label="'总数量'" class="grid-item" :colon="false">
            <a-input-number :disabled="true" size="small" v-model:value="formData.totalQuantity" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="sendDeclare" :label="'发送报关'" class="grid-item" :colon="false">
            <cs-select :disabled="true" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.sendDeclare" id="sendDeclare">
              <a-select-option v-for="item in sendDeclareOptions" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          <a-form-item name="sendDeclare"   :label="'发送报关'" class="grid-item"  :colon="false">-->
<!--            <a-input disabled   size="small" v-model:value="formData.sendDeclare"  />-->
<!--          </a-form-item>-->
          <a-form-item name="createUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createUserName"></a-input>
          </a-form-item>

          <a-form-item name="createTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createTime"
              id="createTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="status" :label="'单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="apprStatus" :label="'审核状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.apprStatus" id="apprStatus">
              <a-select-option v-for="item in productClassify.approval_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
    <a-tabs v-model:activeKey="tabName" v-if="formData.sid" >
      <a-tab-pane key="listRef" tab="表体">
        <BizPurchaseOrderListList  ref="listRef" :head-id="formData.sid" :operation-status="props.editConfig.editStatus" :is-confirm="formData.status" :totalAmount="formData.totalAmount" :contractNo="formData.contractNo"  @listBack="listBack"></BizPurchaseOrderListList>
      </a-tab-pane>
    </a-tabs>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import {getPurchaseOrderSid,getOrderSupplierList} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
import {useIncomingCommon} from "@/view/dec/incoming/common/IncomingCommon";
import BizPurchaseOrderListList from "../../purchaseOrder/bizPurchaseOrderList/BizPurchaseOrderListList.vue";
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const tabName = ref('listRef');
const invalidLoading = ref(false)
const supplierList = ref([])
const currMap = ref([])
const sendDeclareOptions = reactive([
  {
    label:'否',
    value:'0'
  },
  {
    label:'是',
    value:'1'
  },
])
const priceTermPortOptions = reactive([
  {
    label:'起运港',
    value:'0'
  },
  {
    label:'目的港',
    value:'1'
  },
])
const {  portList,priceTermList } = useIncomingCommon({ immediate: true });
const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'contractNo':'合同号'
, 'purchaseOrderNo':'进货单号'
, 'supplier':'供应商'
, 'customer':'客户'
, 'customerAddress':'客户地址'
, 'tradeCountry':'贸易国别'
, 'businessEnterprise':'经营单位'
, 'portOfDestination':'目的地/港'
, 'paymentMethod':'付款方式'
, 'curr':'币种'
, 'totalAmount':'总金额'
, 'transportMode':'运输方式'
, 'priceTerm':'价格条款'
, 'priceTermPort':'价格条款对应港口'
, 'deliveryEnterprise':'发货单位'
, 'wrapType':'包装种类'
, 'packNum':'包装数量'
, 'deliveryEnterpriseAddress':'发货单位所在地'
, 'totalNetWt':'总毛重'
, 'totalGrossWt':'总净重'
, 'totalTare':'总皮重'
, 'sendDeclare':'发送报关'
, 'businessDate':'业务日期'
, 'confirmTime':'确认时间'
, 'isConfirm':'是否确认'
, 'isSave':'是否保存'
, 'note':'备注'
, 'status':'单据状态'
, 'apprStatus':'审核状态'
, 'sendFinance':'发送财务系统'
, 'redFlush':'是否红冲'
, 'purchaseMark':'外商合同、进货明细数据标记'
, 'purchaseNoMark':'外商合同、进货明细数据标记'
, 'invoiceNo':'发票号'
, 'portOfDeparture':'启运港'
, 'vesselVoyage':'船名航次'
, 'sailingDate':'开航日期'
, 'salesDate':'作销日期'
, 'totalQuantity':'总数量'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId:{
    type:String,
    default:()=>''
  },
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid: ''
,createBy: ''
,createTime: ''
,createUserName: ''
,updateBy: ''
,updateUserName: ''
,tradeCode: ''
,sysOrgCode: ''
,extend1: ''
,extend2: ''
,extend3: ''
,extend4: ''
,extend5: ''
,extend6: ''
,extend7: ''
,extend8: ''
,extend9: ''
,extend10: ''
,businessType: ''
,contractNo: ''
,purchaseOrderNo: ''
,supplier: ''
,customer: ''
,customerAddress: ''
,tradeCountry: ''
,businessEnterprise: ''
,portOfDestination: ''
,paymentMethod: ''
,curr: ''
,totalAmount: ''
,transportMode: ''
,priceTerm: ''
,priceTermPort: ''
,deliveryEnterprise: ''
,wrapType: ''
,packNum: ''
,deliveryEnterpriseAddress: ''
,totalNetWt: ''
,totalGrossWt: ''
,totalTare: ''
,sendDeclare: ''
,businessDate: ''
,confirmTime: ''
,isConfirm: ''
,isSave: ''
,note: ''
,status: ''
,apprStatus: ''
,sendFinance: ''
,redFlush: ''
,purchaseMark: ''
,purchaseNoMark: ''
,invoiceNo: ''
,portOfDeparture: ''
,vesselVoyage: ''
,sailingDate: ''
,salesDate: ''
,totalQuantity: ''
})
// 表单校验规则
const rules = {
createBy: [
  { required: true, message: '创建人不能为空！', trigger: 'blur' },
{ max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
],
createTime: [
  { required: true, message: '创建时间不能为空！', trigger: 'blur' },
],
createUserName: [
{ max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
],
updateBy: [
{ max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
],
updateUserName: [
{ max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
],
tradeCode: [
{ max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
],
sysOrgCode: [
{ max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
],
extend1: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend2: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend3: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend4: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend5: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend6: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend7: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend8: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend9: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
extend10: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
businessType: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
contractNo: [
  { required: true, message: '合同号不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
purchaseOrderNo: [
  { required: true, message: '进货单号不能为空！', trigger: 'blur' },
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
supplier: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
customer: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
customerAddress: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
tradeCountry: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
businessEnterprise: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
portOfDestination: [
{ max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
],
paymentMethod: [
{ max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
],
curr: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
totalAmount: [
],
transportMode: [
{ max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
],
priceTerm: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
priceTermPort: [
{ max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
],
deliveryEnterprise: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
wrapType: [
{ max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
],
packNum: [
],
deliveryEnterpriseAddress: [
{ max: 400, message: '长度不能超过400位字节(汉字占2位)！', trigger: 'blur' }
],
totalNetWt: [
],
totalGrossWt: [
],
totalTare: [
],
sendDeclare: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
businessDate: [
{ max: 6, message: '长度不能超过6位字节(汉字占2位)！', trigger: 'blur' }
],

isConfirm: [
{ max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
],
isSave: [
{ max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
],
note: [
{ max: 1000, message: '长度不能超过1000位字节(汉字占2位)！', trigger: 'blur' }
],
status: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
apprStatus: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
sendFinance: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
redFlush: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
purchaseMark: [
{ max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
],
purchaseNoMark: [
{ max: 600, message: '长度不能超过600位字节(汉字占2位)！', trigger: 'blur' }
],
invoiceNo: [
{ max: 120, message: '长度不能超过120位字节(汉字占2位)！', trigger: 'blur' }
],
portOfDeparture: [
{ max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
],
vesselVoyage: [
{ max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
],

totalQuantity: [
],
}
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      window.majesty.httpUtil.putAction(`${ycCsApi.purchaseOrder.bizPurchaseOrderHead.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      window.majesty.httpUtil.putAction(`${ycCsApi.purchaseOrder.bizPurchaseOrderHead.update}/${formData.sid}`, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
const listBack = (val) => {
  console.log('ediTval', val)
  if (val){
    initEdit();
  }
}
const initEdit = async () => {
  const res = await getPurchaseOrderSid({'sid':props.headId})
  if (res.code === 200){
    if(null !== res.data){
      Object.assign(formData, res.data);
    }else {
      Object.assign(formData, {});
    }
  }else {
    message.error(res.message)
  }
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getSupplierList();
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      value
    }));
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})

</script>
<style lang="less" scoped>
</style>
