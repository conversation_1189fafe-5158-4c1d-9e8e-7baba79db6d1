import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
import {useIncomingCommon} from "@/view/dec/incoming/common/IncomingCommon";
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }
  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};
const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()
const {  portList,priceTermList } = useIncomingCommon({ immediate: true });
// 初始化时获取数据
await getMerchantOptions()
function getColumns() {
  const commColumns = reactive([
    'businessType'
    , 'sid'
    , 'createBy'
    , 'createTime'
    , 'createUserName'
    , 'updateBy'
    , 'updateTime'
    , 'updateUserName'
    , 'tradeCode'
    , 'sysOrgCode'
    , 'extend1'
    , 'extend2'
    , 'extend3'
    , 'extend4'
    , 'extend5'
    , 'extend6'
    , 'extend7'
    , 'extend8'
    , 'extend9'
    , 'extend10'
    , 'businessType'
    , 'contractNo'
    , 'purchaseOrderNo'
    , 'supplier'
    , 'customer'
    , 'customerAddress'
    , 'tradeCountry'
    , 'businessEnterprise'
    , 'portOfDestination'
    , 'paymentMethod'
    , 'curr'
    , 'totalAmount'
    , 'transportMode'
    , 'priceTerm'
    , 'priceTermPort'
    , 'deliveryEnterprise'
    , 'wrapType'
    , 'packNum'
    , 'deliveryEnterpriseAddress'
    , 'totalNetWt'
    , 'totalGrossWt'
    , 'totalTare'
    , 'sendDeclare'
    , 'businessDate'
    , 'confirmTime'
    , 'isConfirm'
    , 'isSave'
    , 'note'
    , 'status'
    , 'apprStatus'
    , 'sendFinance'
    , 'redFlush'
    , 'purchaseMark'
    , 'purchaseNoMark'
    , 'invoiceNo'
    , 'portOfDeparture'
    , 'vesselVoyage'
    , 'sailingDate'
    , 'salesDate'
    , 'totalQuantity'
    , 'declarationDate'
    , 'entryNo'
  ])
  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])
  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])
  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    // {
    //   title: '业务类型',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'businessType',
    //   resizable: true,
    //   key: 'businessType',
    //   customRender: ({ text }) => {
    //     return h(<div></div>, cmbShowRender(text,productClassify.businessType2))
    //   }
    // },
    {
      title: '合同号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo',
    },
    {
      title: '进货单号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'purchaseOrderNo',
      resizable: true,
      key: 'purchaseOrderNo',
    },

    // {
    //   title: '发票号',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'invoiceNo',
    //   resizable: true,
    //   key: 'invoiceNo',
    // },
    {
      title: '供应商',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'supplier',
      resizable: true,
      key: 'supplier',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '金额',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'totalAmount',
      resizable: true,
      key: 'totalAmount',
    },
    {
      title: '报关单号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'entryNo',
      resizable: true,
      key: 'entryNo',
    },
    {
      title: '报关日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'declarationDate',
      resizable: true,
      key: 'declarationDate',
      // customRender: ({ text }) => {
      //   return h('span', text ? text.slice(0, 10) : text)
      // }
    },
    // {
    //   title: '启运港',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'portOfDeparture',
    //   resizable: true,
    //   key: 'portOfDeparture',
    //   customRender: ({text}) => {
    //     return cmbShowRender(text, portList.value)
    //   }
    // },
    {
      title: '目的地/港',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'portOfDestination',
      resizable: true,
      key: 'portOfDestination',
      customRender: ({text}) => {
        return cmbShowRender(text, portList.value)
      }
    },

    // {
    //   title: '船名航次',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'vesselVoyage',
    //   resizable: true,
    //   key: 'vesselVoyage',
    // },
    // {
    //   title: '开航日期',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'sailingDate',
    //   resizable: true,
    //   key: 'sailingDate',
    // },
    // {
    //   title: '作销日期',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'salesDate',
    //   resizable: true,
    //   key: 'salesDate',
    // },
    // {
    //   title: '价格条款',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'priceTerm',
    //   resizable: true,
    //   key: 'priceTerm',
    //   customRender: ({text}) => {
    //     return cmbShowRender(text, priceTermList.value)
    //   }
    // },
    // {
    //   title: '价格条款对应港口',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'priceTermPort',
    //   resizable: true,
    //   key: 'priceTermPort',
    //   customRender: ({ text }) => {
    //     return h(<div></div>, cmbShowRender(text,productClassify.priceTermPort))
    //   }
    // },
    {
      title: '币种',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'curr',
      resizable: true,
      key: 'curr',
    },

    // {
    //   title: '总数量',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'totalQuantity',
    //   resizable: true,
    //   key: 'totalQuantity',
    // },
    // {
    //   title: '发送报关',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'sendDeclare',
    //   resizable: true,
    //   key: 'sendDeclare',
    //   customRender: ({ text }) => {
    //     return h(<div></div>, cmbShowRender(text,productClassify.isNot))
    //   }
    // },
    {
      title: '制单人',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'createrUserName',
      resizable: true,
      key: 'createrUserName',
    },
    {
      title: '制单时间',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'createrTime',
      resizable: true,
      key: 'createrTime',
      // customRender: ({ text }) => {
      //   return h('span', text ? text.slice(0, 10) : text)
      // }
    },
    {
      title: '单据状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'status',
      resizable: true,
      key: 'status',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.state))
      }
    },
    {
      title: '确认时间',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'confirmTime',
      resizable: true,
      key: 'confirmTime',
    },
    // {
    //   title: '审核状态',
    //   width: 180,
    //   minWidth: 180,
    //   align: 'center',
    //   dataIndex: 'apprStatus',
    //   resizable: true,
    //   key: 'apprStatus',
    //   customRender: ({ text }) => {
    //     return h(<div></div>, cmbShowRender(text,productClassify.approval_status))
    //   }
    // },
  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
export { getColumns }
