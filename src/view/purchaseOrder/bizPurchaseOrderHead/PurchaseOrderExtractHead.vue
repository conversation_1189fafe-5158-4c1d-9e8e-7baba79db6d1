<template>
  <section class="cs-action">
    <div class="header-search">
      <a-form layout="inline"  label-align="right"  :label-col="{ style: { width: '62px' } }" :model="searchParam"   class="pw-form grid-container" >
        <a-form-item name="contractNo"  label="合同号" class="grid-item merge-3" style="width: 100%" :colon="false">
          <div style="display: flex;">
            <a-input  size="small" v-model:value="searchParam.purchaseNo" allow-clear />
            <a-button class="cs-margin-left" type="primary" size="small" @click="getExtractList">查询</a-button>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <div>
      <s-table
        style="width: 100%;height: 100%;min-height: 100%;overflow-y: auto;overflow-x:auto"
        ref="tableRef"
        size="small"
        class="cs-action-item-modal-table"
        bordered
        height="50vh"
        :scroll="{ y: 400,x:400 }"
        :pagination="false"
        :columns="showColumns"
        :data-source="dataSourceList"
        :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
        :loading="tableLoading"
        row-key="tid"
      >
        <!-- 空数据 -->
        <template #emptyText>
          <a-empty description="暂无数据" />
        </template>
      </s-table>

      <!-- 分页 -->
      <div class=cs-pagination>

        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>
    </div>
    </div>

  </section>
</template>

<script setup>
import {getOrderSupplierList, listInPurchaseOrderHead} from "@/api/cs_api_constant";
import {useCommon} from '@/view/common/useCommon'
import { onMounted, reactive, ref} from "vue";
import {isNullOrEmpty} from "@/view/utils/common";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {useIncomingCommon} from "@/view/dec/incoming/common/IncomingCommon";
const { page } = useCommon()
defineOptions({
  name: 'DeliveryOrderExtractHead'
})

const dataSourceList = ref([]);
/* 表格加载状态 */
const tableLoading = ref(false);

/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});

/* 查询参数 */
const searchParam = reactive({
  contractNo:''
})

/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData,id) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
  gridData.sid = id;
};

/* 获取进口合同列表  */
const getExtractList = () => {
  listInPurchaseOrderHead(searchParam,page).then(res=>{
    if (res.code === 200 && !isNullOrEmpty(res.data)) {
      // getSupplierList()
      dataSourceList.value = res.data
      page.total = res.total
      page.current = res.pageIndex
    }else {
      dataSourceList.value = []
      page.total = 0
      page.current = 1
    }
  })
}
//
// const getSupplierList  = () =>{
//   getOrderSupplierList({}).then(res=>{
//     // console.log('获取供应商信息未',res)
//     if (!isNullOrEmpty(res.data)){
//       supplierList.value = res.data
//     }
//   })
// }
// const supplierList = ref([])
//

// const {  portList } = useIncomingCommon({ immediate: true });
/**
 * 页码 或者 PageSize发生变化时触发
 * @param pageNumber 当前页
 * @param pageSize 每页显示条数
 */
const onPageChange = (pageNumber, pageSize)=>{
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  getExtractList()
}
const { cmbShowRender } = useColumnsRender()

// 表格显示列信息 选择框+合同编号+供应商
const showColumns = [
  {
    title: '合同号',
    dataIndex: 'contractNo',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  // {
  //   title: '买方',
  //   dataIndex: 'buyer',
  //   width: 150,
  //   align: 'center',
  //   ellipsis: true,
  // },
  {
    title: '商品名称',
    dataIndex: 'gname',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '剩余数量',
    dataIndex: 'qtya',
    width: 150,
    align: 'center',
    ellipsis: true,
  }
]


onMounted(() => {
  getExtractList();
  // getSupplierList();
})

defineExpose({
  gridData
})

</script>

<style lang="less" scoped>

.header-search{
  margin: 10px 0;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: calc(100vh);
  height: auto;
  .surely-table-body{
    min-height: calc(100vh);
  }
}
.cs-action-item-modal-table-empty{

  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: 500px;
  line-height: 500px;
}

</style>
