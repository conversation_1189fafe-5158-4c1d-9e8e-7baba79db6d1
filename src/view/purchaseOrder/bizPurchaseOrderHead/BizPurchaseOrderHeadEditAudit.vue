<template>
  <section>
    <a-card size="small" title="xxx" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="createBy" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('createBy')"
                  @click="handleLabelClick('createBy')"
              >
                创建人
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.createBy" id="createBy"></cs-select>
          </a-form-item>
          <a-form-item name="createTime" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('createTime')"
                  @click="handleLabelClick('createTime')"
              >
                创建时间
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.createTime" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="createUserName" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('createUserName')"
                  @click="handleLabelClick('createUserName')"
              >
                创建人名称
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.createUserName"></a-input>
          </a-form-item>
          <a-form-item name="updateBy" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('updateBy')"
                  @click="handleLabelClick('updateBy')"
              >
                更新人
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.updateBy"></a-input>
          </a-form-item>
          <a-form-item name="updateUserName" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('updateUserName')"
                  @click="handleLabelClick('updateUserName')"
              >
                最后修改人名称
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.updateUserName"></a-input>
          </a-form-item>
          <a-form-item name="tradeCode" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('tradeCode')"
                  @click="handleLabelClick('tradeCode')"
              >
                企业编码
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.tradeCode"></a-input>
          </a-form-item>
          <a-form-item name="sysOrgCode" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('sysOrgCode')"
                  @click="handleLabelClick('sysOrgCode')"
              >
                创建人部门编码
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.sysOrgCode"></a-input>
          </a-form-item>
          <a-form-item name="extend1" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend1')"
                  @click="handleLabelClick('extend1')"
              >
                拓展字段1
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend1"></a-input>
          </a-form-item>
          <a-form-item name="extend2" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend2')"
                  @click="handleLabelClick('extend2')"
              >
                拓展字段2
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend2"></a-input>
          </a-form-item>
          <a-form-item name="extend3" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend3')"
                  @click="handleLabelClick('extend3')"
              >
                拓展字段3
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend3"></a-input>
          </a-form-item>
          <a-form-item name="extend4" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend4')"
                  @click="handleLabelClick('extend4')"
              >
                拓展字段4
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend4"></a-input>
          </a-form-item>
          <a-form-item name="extend5" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend5')"
                  @click="handleLabelClick('extend5')"
              >
                拓展字段5
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend5"></a-input>
          </a-form-item>
          <a-form-item name="extend6" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend6')"
                  @click="handleLabelClick('extend6')"
              >
                拓展字段6
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend6"></a-input>
          </a-form-item>
          <a-form-item name="extend7" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend7')"
                  @click="handleLabelClick('extend7')"
              >
                拓展字段7
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend7"></a-input>
          </a-form-item>
          <a-form-item name="extend8" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend8')"
                  @click="handleLabelClick('extend8')"
              >
                拓展字段8
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend8"></a-input>
          </a-form-item>
          <a-form-item name="extend9" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend9')"
                  @click="handleLabelClick('extend9')"
              >
                拓展字段9
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend9"></a-input>
          </a-form-item>
          <a-form-item name="extend10" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('extend10')"
                  @click="handleLabelClick('extend10')"
              >
                拓展字段10
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.extend10"></a-input>
          </a-form-item>
          <a-form-item name="businessType" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('businessType')"
                  @click="handleLabelClick('businessType')"
              >
                业务类型
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.businessType"></a-input>
          </a-form-item>
          <a-form-item name="contractNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('contractNo')"
                  @click="handleLabelClick('contractNo')"
              >
                合同号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.contractNo"></a-input>
          </a-form-item>
          <a-form-item name="purchaseOrderNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('purchaseOrderNo')"
                  @click="handleLabelClick('purchaseOrderNo')"
              >
                进货单号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.purchaseOrderNo"></a-input>
          </a-form-item>
          <a-form-item name="supplier" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('supplier')"
                  @click="handleLabelClick('supplier')"
              >
                供应商
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.supplier" id="supplier"></cs-select>
          </a-form-item>
          <a-form-item name="customer" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('customer')"
                  @click="handleLabelClick('customer')"
              >
                客户
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.customer"></a-input>
          </a-form-item>
          <a-form-item name="customerAddress" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('customerAddress')"
                  @click="handleLabelClick('customerAddress')"
              >
                客户地址
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.customerAddress"></a-input>
          </a-form-item>
          <a-form-item name="tradeCountry" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('tradeCountry')"
                  @click="handleLabelClick('tradeCountry')"
              >
                贸易国别
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.tradeCountry"></a-input>
          </a-form-item>
          <a-form-item name="businessEnterprise" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('businessEnterprise')"
                  @click="handleLabelClick('businessEnterprise')"
              >
                经营单位
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.businessEnterprise"></a-input>
          </a-form-item>
          <a-form-item name="portOfDestination" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('portOfDestination')"
                  @click="handleLabelClick('portOfDestination')"
              >
                目的地/港
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.portOfDestination"></a-input>
          </a-form-item>
          <a-form-item name="paymentMethod" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('paymentMethod')"
                  @click="handleLabelClick('paymentMethod')"
              >
                付款方式
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.paymentMethod"></a-input>
          </a-form-item>
          <a-form-item name="curr" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('curr')"
                  @click="handleLabelClick('curr')"
              >
                币种
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.curr"></a-input>
          </a-form-item>
          <a-form-item name="totalAmount" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('totalAmount')"
                  @click="handleLabelClick('totalAmount')"
              >
                总金额
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.totalAmount" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="transportMode" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('transportMode')"
                  @click="handleLabelClick('transportMode')"
              >
                运输方式
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.transportMode"></a-input>
          </a-form-item>
          <a-form-item name="priceTerm" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('priceTerm')"
                  @click="handleLabelClick('priceTerm')"
              >
                价格条款
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.priceTerm"></a-input>
          </a-form-item>
          <a-form-item name="priceTermPort" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('priceTermPort')"
                  @click="handleLabelClick('priceTermPort')"
              >
                价格条款对应港口
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.priceTermPort"></a-input>
          </a-form-item>
          <a-form-item name="deliveryEnterprise" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('deliveryEnterprise')"
                  @click="handleLabelClick('deliveryEnterprise')"
              >
                发货单位
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.deliveryEnterprise"></a-input>
          </a-form-item>
          <a-form-item name="wrapType" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('wrapType')"
                  @click="handleLabelClick('wrapType')"
              >
                包装种类
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.wrapType"></a-input>
          </a-form-item>
          <a-form-item name="packNum" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('packNum')"
                  @click="handleLabelClick('packNum')"
              >
                包装数量
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.packNum" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="deliveryEnterpriseAddress" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('deliveryEnterpriseAddress')"
                  @click="handleLabelClick('deliveryEnterpriseAddress')"
              >
                发货单位所在地
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.deliveryEnterpriseAddress"></a-input>
          </a-form-item>
          <a-form-item name="totalNetWt" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('totalNetWt')"
                  @click="handleLabelClick('totalNetWt')"
              >
                总毛重
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.totalNetWt" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="totalGrossWt" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('totalGrossWt')"
                  @click="handleLabelClick('totalGrossWt')"
              >
                总净重
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.totalGrossWt" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="totalTare" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('totalTare')"
                  @click="handleLabelClick('totalTare')"
              >
                总皮重
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.totalTare" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="sendDeclare" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('sendDeclare')"
                  @click="handleLabelClick('sendDeclare')"
              >
                发送报关
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.sendDeclare" id="sendDeclare"></cs-select>
          </a-form-item>
          <a-form-item name="businessDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('businessDate')"
                  @click="handleLabelClick('businessDate')"
              >
                业务日期
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.businessDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="confirmTime" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('confirmTime')"
                  @click="handleLabelClick('confirmTime')"
              >
                确认时间
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.confirmTime" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="isConfirm" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('isConfirm')"
                  @click="handleLabelClick('isConfirm')"
              >
                是否确认
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.isConfirm"></a-input>
          </a-form-item>
          <a-form-item name="isSave" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('isSave')"
                  @click="handleLabelClick('isSave')"
              >
                是否保存
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.isSave"></a-input>
          </a-form-item>
          <a-form-item name="note" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('note')"
                  @click="handleLabelClick('note')"
              >
                备注
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"></a-input>
          </a-form-item>
          <a-form-item name="status" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('status')"
                  @click="handleLabelClick('status')"
              >
                单据状态
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="formData.status" id="status"></cs-select>
          </a-form-item>
          <a-form-item name="apprStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('apprStatus')"
                  @click="handleLabelClick('apprStatus')"
              >
                审核状态
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.apprStatus"></a-input>
          </a-form-item>
          <a-form-item name="sendFinance" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('sendFinance')"
                  @click="handleLabelClick('sendFinance')"
              >
                发送财务系统
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.sendFinance"></a-input>
          </a-form-item>
          <a-form-item name="redFlush" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('redFlush')"
                  @click="handleLabelClick('redFlush')"
              >
                是否红冲
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.redFlush"></a-input>
          </a-form-item>
          <a-form-item name="purchaseMark" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('purchaseMark')"
                  @click="handleLabelClick('purchaseMark')"
              >
                外商合同、进货明细数据标记
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.purchaseMark"></a-input>
          </a-form-item>
          <a-form-item name="purchaseNoMark" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('purchaseNoMark')"
                  @click="handleLabelClick('purchaseNoMark')"
              >
                外商合同、进货明细数据标记
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.purchaseNoMark"></a-input>
          </a-form-item>
          <a-form-item name="invoiceNo" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('invoiceNo')"
                  @click="handleLabelClick('invoiceNo')"
              >
                发票号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.invoiceNo"></a-input>
          </a-form-item>
          <a-form-item name="portOfDeparture" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('portOfDeparture')"
                  @click="handleLabelClick('portOfDeparture')"
              >
                启运港
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.portOfDeparture"></a-input>
          </a-form-item>
          <a-form-item name="vesselVoyage" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('vesselVoyage')"
                  @click="handleLabelClick('vesselVoyage')"
              >
                船名航次
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.vesselVoyage"></a-input>
          </a-form-item>
          <a-form-item name="sailingDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('sailingDate')"
                  @click="handleLabelClick('sailingDate')"
              >
                开航日期
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.sailingDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="salesDate" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('salesDate')"
                  @click="handleLabelClick('salesDate')"
              >
                作销日期
              </span>
            </template>
            <a-date-picker :disabled="showDisable" v-model:value="formData.salesDate" valueFormat="YYYY-MM-DD" format="yyyy-MM-DD" :locale="locale" placeholder="" size ="small" style="width: 100%;" />
          </a-form-item>
          <a-form-item name="totalQuantity" class="grid-item" :colon="false">
            <template #label>
              <span
                  class="form-label"
                  :class="getLabelClass('totalQuantity')"
                  @click="handleLabelClick('totalQuantity')"
              >
                总数量
              </span>
            </template>
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.totalQuantity" style="width: 100%"/>
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
//import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
const { getPCode } = usePCode()
// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)
// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'SID':'主键'
, 'createBy':'创建人'
, 'createTime':'创建时间'
, 'createUserName':'创建人名称'
, 'updateBy':'更新人'
, 'updateTime':'最后修改时间'
, 'updateUserName':'最后修改人名称'
, 'tradeCode':'企业编码'
, 'sysOrgCode':'创建人部门编码'
, 'extend1':'拓展字段1'
, 'extend2':'拓展字段2'
, 'extend3':'拓展字段3'
, 'extend4':'拓展字段4'
, 'extend5':'拓展字段5'
, 'extend6':'拓展字段6'
, 'extend7':'拓展字段7'
, 'extend8':'拓展字段8'
, 'extend9':'拓展字段9'
, 'extend10':'拓展字段10'
, 'businessType':'业务类型'
, 'contractNo':'合同号'
, 'purchaseOrderNo':'进货单号'
, 'supplier':'供应商'
, 'customer':'客户'
, 'customerAddress':'客户地址'
, 'tradeCountry':'贸易国别'
, 'businessEnterprise':'经营单位'
, 'portOfDestination':'目的地/港'
, 'paymentMethod':'付款方式'
, 'curr':'币种'
, 'totalAmount':'总金额'
, 'transportMode':'运输方式'
, 'priceTerm':'价格条款'
, 'priceTermPort':'价格条款对应港口'
, 'deliveryEnterprise':'发货单位'
, 'wrapType':'包装种类'
, 'packNum':'包装数量'
, 'deliveryEnterpriseAddress':'发货单位所在地'
, 'totalNetWt':'总毛重'
, 'totalGrossWt':'总净重'
, 'totalTare':'总皮重'
, 'sendDeclare':'发送报关'
, 'businessDate':'业务日期'
, 'confirmTime':'确认时间'
, 'isConfirm':'是否确认'
, 'isSave':'是否保存'
, 'note':'备注'
, 'status':'单据状态'
, 'apprStatus':'审核状态'
, 'sendFinance':'发送财务系统'
, 'redFlush':'是否红冲'
, 'purchaseMark':'外商合同、进货明细数据标记'
, 'purchaseNoMark':'外商合同、进货明细数据标记'
, 'invoiceNo':'发票号'
, 'portOfDeparture':'启运港'
, 'vesselVoyage':'船名航次'
, 'sailingDate':'开航日期'
, 'salesDate':'作销日期'
, 'totalQuantity':'总数量'
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 是否禁用
const showDisable = ref(false)
const formData = reactive({
  sid:''
,createBy: ''
,createTime: ''
,createUserName: ''
,updateBy: ''
,updateUserName: ''
,tradeCode: ''
,sysOrgCode: ''
,extend1: ''
,extend2: ''
,extend3: ''
,extend4: ''
,extend5: ''
,extend6: ''
,extend7: ''
,extend8: ''
,extend9: ''
,extend10: ''
,businessType: ''
,contractNo: ''
,purchaseOrderNo: ''
,supplier: ''
,customer: ''
,customerAddress: ''
,tradeCountry: ''
,businessEnterprise: ''
,portOfDestination: ''
,paymentMethod: ''
,curr: ''
,totalAmount: ''
,transportMode: ''
,priceTerm: ''
,priceTermPort: ''
,deliveryEnterprise: ''
,wrapType: ''
,packNum: ''
,deliveryEnterpriseAddress: ''
,totalNetWt: ''
,totalGrossWt: ''
,totalTare: ''
,sendDeclare: ''
,businessDate: ''
,confirmTime: ''
,isConfirm: ''
,isSave: ''
,note: ''
,status: ''
,apprStatus: ''
,sendFinance: ''
,redFlush: ''
,purchaseMark: ''
,purchaseNoMark: ''
,invoiceNo: ''
,portOfDeparture: ''
,vesselVoyage: ''
,sailingDate: ''
,salesDate: ''
,totalQuantity: ''
})
// 表单引用
const formRef = ref()
// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()
const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    // deleteContract(formData.sid).then(res => {
    //   emit('onEditBack', val);
    // })
  } else {
    emit('onEditBack', val);
    //saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
}
const pCode = ref('')
// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})
/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }
  // 审核意见输入框
  const auditOpinion = ref('同意审批')
  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'xxx',
      }
      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.purchaseOrder.bizPurchaseOrderHead.audit, params)
          .then(res => {
            if (res.code === 200) {
              message.success("审核通过成功！")
              // 返回列表页面
              onBack(true)
            } else {
              message.error(res.message || '审核失败')
            }
          })
          .catch(error => {
            console.error('审核失败:', error)
            message.error('审核失败，请重试')
          })
          .finally(() => {
            auditLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }
  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'contract',
      }
      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.purchaseOrder.bizPurchaseOrderHead.reject, params)
          .then(res => {
            if (res.code === 200) {
              // 审核退回成功后，调用标记保存接口
              return saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
            } else {
              throw new Error(res.message || '审核退回失败')
            }
          })
          .then(res => {
            message.success("审核退回成功！")
            // 返回列表页面
            onBack(true)
          })
          .catch(error => {
            console.error('审核退回失败:', error)
            message.error(error.message || '审核退回失败，请重试')
          })
          .finally(() => {
            invalidLoading.value = false
          })
    },
    onCancel() {
      // 取消操作
    },
  });
}
</script>
<style lang="less" scoped>
</style>
