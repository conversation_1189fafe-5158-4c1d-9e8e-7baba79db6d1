import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {useMerchant} from "@/view/common/useMerchant";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender,formatNumber  } = useColumnsRender()
const { merchantOptions,getMerchantOptions,unitList,getUnitList } = useMerchant()

await getMerchantOptions()
export function getColumns() {

  const commColumns = reactive([
    'gNameNo'
    , 'qtyDeli'
    , 'qtyIss'
    , 'unit'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns,
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    // {
    //   width: 80,
    //   minWidth:80,
    //   title: '操作',
    //   dataIndex: 'operation',
    //   key: 'operation',
    //   align: 'center',
    //   fixed: 'left',
    //   resizable:"true",
    // },
    {
      title: '商品牌号',
      minWidth: 120,
      align: 'center',
      key: 'gNameNo',
      dataIndex: 'gNameNo',
      resizable:"true",
    },
    {
      title: '出货数量',
      minWidth: 120,
      align: 'center',
      key: 'qtyDeli',
      dataIndex: 'qtyDeli',
      resizable:"true",
      customRender({text}){
        return formatNumber(text);
      }
    },
    {
      title: '实发数量',
      minWidth: 120,
      align: 'center',
      key: 'qtyIss',
      dataIndex: 'qtyIss',
      resizable:"true",
      customRender({text}){
        return formatNumber(text);
      }
    },
    {
      title: '单位',
      minWidth: 120,
      align: 'center',
      key: 'unit',
      dataIndex: 'unit',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, unitList.value))
      }
    },
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns,
    commColumns
  }
}


