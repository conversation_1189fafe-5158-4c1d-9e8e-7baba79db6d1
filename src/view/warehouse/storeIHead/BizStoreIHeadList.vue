<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <BizStoreISearch ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:add']">
          <a-button size="small" @click="handlerOpenAddModal">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{localeContent('m.common.button.add')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:edit']">
          <a-button size="small" @click="handlerEdit">
            <template #icon>
              <GlobalIcon type="form" style="color:orange"/>
            </template>
            {{localeContent('m.common.button.update')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:delete']">
          <a-button size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{localeContent('m.common.button.delete')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:export']">
          <a-button size="small" :loading="exportLoading" @click="handlerExport">
            <template #icon>
              <GlobalIcon type="folder-open" style="color:orange"/>
            </template>
            {{localeContent('m.common.button.export')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:printReceiptContainer']">
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handlePrintReceiptContainer">
                <a-menu-item key=".pdf">打印PDF</a-menu-item>
                <a-menu-item key=".xlsx">打印EXCEL</a-menu-item>
              </a-menu>
            </template>
            <a-button size="small" type="ghost" :loading="printReceiptLoading">
              <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
              <DownOutlined />
              打印入库回单
            </a-button>
          </a-dropdown>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse-plan:printWarehouse']">
          <a-dropdown>
            <template #overlay>
              <a-menu >
                <a-menu-item key="1" @click="handlerWarehouseExportPDF">打印PDF</a-menu-item>
                <a-menu-item key="2" @click="handlerWarehouseExportEXCEL">打印EXCEL</a-menu-item>
              </a-menu>
            </template>
            <a-button size="small" type="ghost">
              <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
              <DownOutlined />
              打印出库回单
            </a-button>
          </a-dropdown>
        </div>
<!--        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:confirm']">-->
<!--          <a-button size="small" :loading="confirmLoading" @click="handlerConfirm">-->
<!--            <template #icon>-->
<!--              <GlobalIcon type="check" style="color:green"/>-->
<!--            </template>-->
<!--            确认-->
<!--          </a-button>-->
<!--        </div>-->
<!--        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:copyVersion']">-->
<!--          <a-button size="small" :loading="copyVersionLoading" @click="handlerCopyVersion">-->
<!--            <template #icon>-->
<!--              <GlobalIcon type="snippets" style="color:deepskyblue"/>-->
<!--            </template>-->
<!--            版本复制-->
<!--          </a-button>-->
<!--        </div>-->
        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:sendAudit']">
          <a-button size="small" :loading="sendAuditLoading" @click="handlerSendAudit">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue"/>
            </template>
            发送审核
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:warehouse:valid']">
          <a-button size="small" :loading="invalidateLoading" @click="handlerInvalidate">
            <template #icon>
              <GlobalIcon type="close-square" style="color:red"/>
            </template>
            作废
          </a-button>
        </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>

      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          column-drag
          :custom-row="customRow"
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  <template #icon>
                    <GlobalIcon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>
              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <StoreHeadTab :editConfig="editConfig" @onEditBack="handlerOnBack" />
    </div>

    <!-- 新增弹框 -->
    <cs-modal :visible="showAddModal" :title="'新增'" :width="1200" :footer="true" @cancel="handleCloseModal">
      <template #customContent>
        <BizStoreExtractHead ref="extractRef"></BizStoreExtractHead>
      </template>
      <template #footer>
        <div style="display: flex;justify-content: right;align-items: center">
          <a-button @click="handleCloseModal" size="small">返回</a-button>
          <a-button
            style="margin-left: 8px"
            size="small"
            type="primary"
            @click="extractData"
            :loading="confirmLoading"
          >保存</a-button>
        </div>
      </template>
    </cs-modal>

  </section>
</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import { GlobalIcon } from '@/components/icon'
import {createVNode, nextTick, onMounted, reactive, ref, watch} from "vue";
import {getColumns} from "@/view/warehouse/storeIHead/BizStoreIHeadColumns";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
const { totalColumns } = getColumns()
import {localeContent} from "../../utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute, useRouter} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
import {deletePlan,insertPlan,confirmPlan,sendAudit, copyVersion, invalidatePlan,checkPlanIdNotCancel} from "@/api/importedCigarettes/plan/planApi";
import StoreHeadTab from "@/view/warehouse/storeIHead/StoreHeadTab";
import BizStoreISearch from "@/view/warehouse/storeIHead/BizStoreISearch";
import BizStoreExtractHead from "@/view/warehouse/storeIHead/BizStoreExtractHead";
import {cancelStoreIHeadList, deleteStoreIHeadList, generateIStore, sendAuditStoreIHead} from "@/api/cs_api_constant";
import CsModal from "@/components/modal/cs-modal.vue";

/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData,
  onSelectChange

} = useCommon()



defineOptions({
  name: 'BizStoreIHeadList',
});

const router = useRouter();
ajaxUrl.selectAllPage = ycCsApi.warehouseUrl.bizStoreIHead.list
ajaxUrl.exportUrl = ycCsApi.warehouseUrl.bizStoreIHead.export

const importShow = ref(false)

onMounted(fn => {

  tableHeight.value = getTableScroll(100,'');
  getList()

  nextTick(() => {

    initCustomColumn()
  })
})

const tableHeight = ref('')

/* 按钮loading */
const deleteLoading = ref(false)
const confirmLoading = ref(false)
const copyVersionLoading = ref(false)
const sendAuditLoading = ref(false)
const printReceiptLoading = ref(false)
const invalidateLoading = ref(false)
const showAddModal = ref(false)
const extractRef = ref(null)
const handlerOpenAddModal = () => {
  showAddModal.value = true
}
const handleCloseModal = () => {
  showAddModal.value = false
  confirmLoading.value = false
}


const extractData = async ()=> {
  console.log('extractContractData',extractRef.value.gridData.selectedRowKeys)
  confirmLoading.value = true
  // 提取数据
  const res =  await generateIStore({sids:extractRef.value.gridData.selectedRowKeys});
  try {
    if (res.code === 200){
      message.success(res.message)
      confirmLoading.value = false
      showAddModal.value = false
      getList()
      // console.log('dataSourceList.value',dataSourceList.value)
      editConfig.value.editStatus = editStatus.EDIT
      editConfig.value.editData =  res.data
      show.value =!show.value
    }else{
      message.error(res.message)
      confirmLoading.value = false
    }
  }catch (err) {
    confirmLoading.value = false
  }
}



/* 返回事件 */
const handlerOnBack = async (flag) => {
  // 检查是否是自动创建的表头数据且未保存过，且是新增状态
  if (editConfig.value.autoCreated && !editConfig.value.hasSaved && editConfig.value.editStatus === editStatus.ADD) {
    // 弹出确认框
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确定',
      cancelText: '取消',
      content: '您尚未保存数据，是否确定返回？返回后当前已维护的表头数据无效。',
      onOk: async () => {
        try {
          // 删除自动创建的表头数据
          const res = await deleteStoreIHeadList(editConfig.value.editData.sid);
          if (res.code === 200) {
            // message.success('已删除未保存的数据');
          } else {
            // message.error(res.message || '删除失败');
          }
        } catch (error) {
          message.error('返回出错，请联系管理员');
        }
        // 重置标记
        editConfig.value.autoCreated = false;
        editConfig.value.hasSaved = false;
        // 返回列表页面
        show.value = !show.value;
        // 如果需要刷新列表
        if (flag) {
          getList();
        }
      },
      onCancel() {
        // 用户取消返回，不做任何操作
      }
    });
  } else {
    // 正常返回
    show.value = !show.value;
    // 返回清空选择数据
    if (flag) {
      getList();
    }
    // 重置标记，确保下次新增时是全新状态
    editConfig.value.autoCreated = false;
    editConfig.value.hasSaved = false;
  }
}

/* 新增数据 */
// const handlerAdd = async () => {
//   // 重置标记，确保每次新增都是全新的状态
//   editConfig.value.autoCreated = false;
//   editConfig.value.hasSaved = false;
//
//   // 设置 editConfig 为新增状态
//   editConfig.value.editStatus = editStatus.ADD;
//   editConfig.value.editData = {}; // 清空数据以便新增
//
//   // 创建一个临时对象用于自动保存
//   const tempData = {
//     businessType: '1',
//     planId: '',
//     planYear: '',
//     halfYear: '',
//     remark: '',
//     createrUser: '',
//     createrTime: '',
//     status: '0',
//     confirmTime: '',
//     apprStatus: '0',
//     versionNo: '1'
//   };
//
//   try {
//     // 自动调用保存接口创建表头数据
//     const res = await insertPlan(tempData);
//     if (res.code === 200) {
//       // 更新表单数据为后端返回的数据
//       editConfig.value.editData = res.data;
//       // 确保 sid 被正确设置
//       if (res.data && res.data.sid) {
//         editConfig.value.editData.sid = res.data.sid;
//       }
//       // 设置一个标记，表示已经创建了表头数据
//       editConfig.value.autoCreated = true;
//       // 显示编辑组件
//       show.value = !show.value;
//     } else {
//       message.error(res.message || '创建失败');
//     }
//   } catch (error) {
//     message.error('创建失败，请重试');
//   }
// }

/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }

  // 检查选中数据的状态
  // const selectedData = gridData.selectedData[0];
  // if (selectedData.status === '1') {
  //   message.warning('确认状态的数据不允许编辑');
  //   return;
  // }
  // if (selectedData.status === '2') {
  //   message.warning('作废状态的数据不允许编辑');
  //   return;
  // }

  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = gridData.selectedData[0]
  show.value = !show.value;
}

/**
 * 点击编辑按钮
 * @param row 表格行数据
 */
function handleEditByRow(row) {
  // // 检查选中数据的状态
  // if (row.status === '1') {
  //   message.warning('确认状态的数据不允许编辑');
  //   return;
  // }
  // if (row.status === '2') {
  //   message.warning('作废状态的数据不允许编辑');
  //   return;
  // }
  // 在这里添加处理编辑行的逻辑
  show.value = !show.value
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = row
}

// 自定义行属性
const customRow = (record) => {
  return {
    onDblclick: () => {
      handleRowDblclick(record);
    }, style: {cursor: 'pointer'}
  };
};
/* 双击行进入编辑页面 */
const handleRowDblclick = (record) => {
  // // 检查选中数据的状态
  // if (record.status === '1') {
  //   message.warning('确认状态的数据不允许编辑');
  //   return;
  // }
  // if (record.status === '2') {
  //   message.warning('作废状态的数据不允许编辑');
  //   return;
  // }
  editConfig.value.editStatus = editStatus.EDIT;
  editConfig.value.editData = record;
  show.value = !show.value;
};


/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('请只选择一条数据');
    return;
  }
  console.log(gridData.selectedData)
  if(gridData.selectedData[0].status !== '0'){
    message.warning('仅编制状态数据允许删除!');
    return;
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deleteStoreIHeadList(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        }else {
          message.error(res.message)
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });
}
// 处理打印菜单点击事件
const handlePrintReceiptContainer = (e) => {
  const printType = e.key; // 'pdf' or 'xlsx'
  handleSaveAndPrint(printType);
}
const handleSaveAndPrint = async (fileType) => {

  printReceiptLoading.value = true;
  // gridData.selectedRowKeys[0].fileType = fileType;
  // const params = {sid:'c1bb54fb-28c6-4e61-856d-cbda164fe6f9',type:'.pdf'}
  // const params =gridData.selectedRowKeys[0]
  const params = {sid:gridData.selectedRowKeys[0],fileType:fileType}
  const url = `${ycCsApi.warehouseUrl.bizStoreIHead.printReceiptContainer}`;

  window.majesty.httpUtil.downloadFile(
    url, null, params, 'post', null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
    message.error(`打印${fileType === 'xlsx' ? 'XLSX' : 'PDF'}失败`);
  }).finally(() => {
    printReceiptLoading.value = false;
  })
};



const handlerWarehouseExportPDF = () =>{
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择要打印的数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据')
    return
  }
  const params = {sid:gridData.selectedRowKeys[0],sType:'pdf'}
  window.majesty.httpUtil.downloadFile(
    `${ycCsApi.warehouseUrl.bizStoreIHead.printOutBoundReceipt}/${gridData.selectedRowKeys[0]}/pdf`, null,params,'post',null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
  }).finally(() => {
  })
}
const handlerWarehouseExportEXCEL = () =>{
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择要打印的数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据')
    return
  }
  const params = {sid:gridData.selectedRowKeys[0],sType:'excel'}
  window.majesty.httpUtil.downloadFile(
    `${ycCsApi.warehouseUrl.bizStoreIHead.printOutBoundReceipt}/${gridData.selectedRowKeys[0]}/excel`, null,params,'post',null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
  }).finally(() => {
  })
}
/* 导出事件 */
const handlerExport = () => {
  // 检查是否有选中的行
  // if (gridData.selectedRowKeys.length <= 0) {
  //   message.warning('请选择要导出的数据')
  //   return
  // }

  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`入库回单表头${timestamp}.xlsx`, totalColumns)

  // // 自定义导出方法，不使用公共的doExport方法
  // exportLoading.value = true
  // window.window.majesty.httpUtil.downloadFile(
  //   ajaxUrl.exportUrl,
  //   `入库回单${timestamp}.xlsx`,
  //   {
  //     // 传递选中的数据
  //     selectedData: gridData.selectedData,
  //     name: `入库回单${timestamp}.xlsx`,
  //     // 不过滤字段，传递空数组
  //     header: []
  //   },
  //   'post',
  //   () => {}
  // ).then((res) => {
  //   // 导出成功后的处理
  // }).finally(() => {
  //   exportLoading.value = false
  // })
}

/* 自定义设置 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')

tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()

/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}

/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
}

/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})




/* 发送审核处理 */
const handlerSendAudit = () => {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据');
    return;
  }


  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据发送审批');
    return;
  }

  // 检查选中数据的状态
  const selectedData = gridData.selectedData[0];
  if (selectedData.apprStatus !== '1') {
    message.warning('只有未审批的数据可以发送审批');
    return;
  }
  if (selectedData.status !== '1') {
    message.warning('请将进口计划操作确认再发送审批');
    return;
  }


  Modal.confirm({
    title: '确认操作',
    content: '确认将所选项发送内审吗？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      sendAuditLoading.value = true; // 开始 loading
      sendAuditStoreIHead(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("已发送审核！");
          getList();
        }else {
          message.error(res.message || "发送失败，请重试！");
        }
      }).finally(() => {
        sendAuditLoading.value = false; // 结束 loading
      });
    },
    onCancel() {
      // 取消操作
    },
  });
}

/* 作废处理 */
const handlerInvalidate = () => {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据');
    return;
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('请只选择一条数据');
    return;
  }
  Modal.confirm({
    title: '确认操作',
    content: '确认将所选项发送作废吗？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      invalidateLoading.value = true; // 开始 loading
      cancelStoreIHeadList(gridData.selectedRowKeys).then(res => {
        if (res.success === true) {
          message.success("已作废！");
          getList();
        } else {
          message.error(res.message);
        }
      }).finally(() => {
        invalidateLoading.value = false; // 结束 loading
      });
    },
    onCancel() {
      // 取消操作
    },
  });
}

</script>

<style lang="less" scoped>


</style>
