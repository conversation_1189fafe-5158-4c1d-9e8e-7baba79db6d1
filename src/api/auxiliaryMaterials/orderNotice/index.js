import ycCsApi from '@/api/ycCsApi'

export const getAllCustomers = (params) =>
  window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.orderNotice.head.getAllCustomers, params)

export const getPortOptions = () =>
  window.majesty.httpUtil.getAction(ycCsApi.auxiliaryMaterials.orderNotice.head.getPortOptions)

export const getSelectContractData = () =>
  window.majesty.httpUtil.getAction(ycCsApi.auxiliaryMaterials.orderNotice.head.getSelectData)

export const getAddData = contractIds =>
  window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.orderNotice.head.getAddData, {contractIds})

export const insertHead = addParams =>
  window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.orderNotice.head.insert, addParams)

export const updateHead = (id, updateParams) =>
  window.majesty.httpUtil.putAction(ycCsApi.auxiliaryMaterials.orderNotice.head.update + '/' + id, updateParams)

export const deleteHead = ids =>
  window.majesty.httpUtil.deleteAction(ycCsApi.auxiliaryMaterials.orderNotice.head.delete + '/' + ids)

export const confirmHead = id =>
  window.majesty.httpUtil.putAction(ycCsApi.auxiliaryMaterials.orderNotice.head.confirm + '/' + id)

export const invalidateHead = id =>
  window.majesty.httpUtil.putAction(ycCsApi.auxiliaryMaterials.orderNotice.head.invalidate + '/' + id)

export const checkVersionCopy = orderNo =>
  window.majesty.httpUtil.getAction(ycCsApi.auxiliaryMaterials.orderNotice.head.checkVersionCopy + '/' + orderNo)

export const versionCopy = params =>
  window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.orderNotice.head.versionCopy, params)

export const printOrderForm = (type, contractIds) =>
  window.majesty.httpUtil.downloadFile(ycCsApi.auxiliaryMaterials.orderNotice.head.printOrderForm
    , '', {type, contractIds}, 'post', null)

export const getSummaryList = params =>
  window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.orderNotice.body.getSummary, params)

export const updateList = (id, params) =>
  window.majesty.httpUtil.putAction(ycCsApi.auxiliaryMaterials.orderNotice.body.update + '/' + id, params)

export const deleteList = ids =>
  window.majesty.httpUtil.deleteAction(ycCsApi.auxiliaryMaterials.orderNotice.body.delete + '/' + ids)

export const getAddListDetailData = contractIds =>
  window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.orderNotice.body.getAddListDetailData, {contractIds})

export const addListDetail = params =>
  window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.orderNotice.body.addListDetail, params)
