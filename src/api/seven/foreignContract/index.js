import ycCsApi from '@/api/ycCsApi'

// =============================>>>>>>************ HEAD ************<<<<<<=============================
export const getOptionsData = () =>
  window.majesty.httpUtil.getAction(ycCsApi.seven.foreignContract.head.options)

export const insertContract = params => window.majesty.httpUtil.postAction(ycCsApi.seven.foreignContract.head.insert, params)

export const updateContract = (id, params) =>
  window.majesty.httpUtil.putAction(ycCsApi.seven.foreignContract.head.update + '/' + id, params)

export const deleteContract = ids =>
  window.majesty.httpUtil.deleteAction(ycCsApi.seven.foreignContract.head.delete + '/' + ids)

export const confirmContract = id =>
  window.majesty.httpUtil.putAction(ycCsApi.seven.foreignContract.head.confirm + '/' + id)

export const invalidateContract = id =>
  window.majesty.httpUtil.putAction(ycCsApi.seven.foreignContract.head.invalidate + '/' + id)

export const checkVersionCopy = contractNo =>
  window.majesty.httpUtil.getAction(ycCsApi.seven.foreignContract.head.checkVersionCopy + '/' + contractNo)

export const versionCopy = params =>
  window.majesty.httpUtil.postAction(ycCsApi.seven.foreignContract.head.versionCopy, params)

export const printSheet = (type, ids) =>
  window.majesty.httpUtil.downloadFile(ycCsApi.seven.foreignContract.head.printSheet
    , '', { type, ids }, 'post', null)

// =============================>>>>>>************ BODY ************<<<<<<=============================
export const getMaterial = () =>
  window.majesty.httpUtil.getAction(ycCsApi.seven.foreignContract.body.getMaterial)

export const getBodyList = (bodyType, headId, currentPage, pageSize) => {
  const listUrl = `${ycCsApi.seven.foreignContract.body.list}?page=${currentPage || 1}&limit=${pageSize || 10}`
  return window.majesty.httpUtil.postAction(listUrl, { headId, bodyType })
}

export const getBodySummaryInfo = params =>
  window.majesty.httpUtil.postAction(ycCsApi.seven.foreignContract.body.summary, params)

export const addBody = params =>
  window.majesty.httpUtil.postAction(ycCsApi.seven.foreignContract.body.insert, params)

export const updateBody = (id, params) =>
  window.majesty.httpUtil.putAction(ycCsApi.seven.foreignContract.body.update + '/' + id, params)

export const deleteBody = ids =>
  window.majesty.httpUtil.deleteAction(ycCsApi.seven.foreignContract.body.delete + '/' + ids)
