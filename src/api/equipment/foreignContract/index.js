import ycCsApi from '@/api/ycCsApi'

// =============================>>>>>>************ HEAD ************<<<<<<=============================
export const getDollarRate = curr =>
  window.majesty.httpUtil.getAction(ycCsApi.equipment.foreignContract.head.dollarRate + '/' + curr)

export const getOptionsData = () =>
  window.majesty.httpUtil.getAction(ycCsApi.equipment.foreignContract.head.options)

export const insertContract = params => window.majesty.httpUtil.postAction(ycCsApi.equipment.foreignContract.head.insert, params)

export const updateContract = (id, params) =>
  window.majesty.httpUtil.putAction(ycCsApi.equipment.foreignContract.head.update + '/' + id, params)

export const deleteContract = ids =>
  window.majesty.httpUtil.deleteAction(ycCsApi.equipment.foreignContract.head.delete + '/' + ids)

export const confirmContract = id =>
  window.majesty.httpUtil.putAction(ycCsApi.equipment.foreignContract.head.confirm + '/' + id)

export const invalidateContract = id =>
  window.majesty.httpUtil.putAction(ycCsApi.equipment.foreignContract.head.invalidate + '/' + id)

export const checkVersionCopy = contractNo =>
  window.majesty.httpUtil.getAction(ycCsApi.equipment.foreignContract.head.checkVersionCopy + '/' + contractNo)

export const versionCopy = params =>
  window.majesty.httpUtil.postAction(ycCsApi.equipment.foreignContract.head.versionCopy, params)

// =============================>>>>>>************ BODY ************<<<<<<=============================
export const getMaterial = () =>
  window.majesty.httpUtil.getAction(ycCsApi.equipment.foreignContract.body.getMaterial)

export const getBodyList = (headId, currentPage, pageSize) => {
  const listUrl = `${ycCsApi.equipment.foreignContract.body.list}?page=${currentPage || 1}&limit=${pageSize || 10}`
  return window.majesty.httpUtil.postAction(listUrl, { headId })
}

export const getBodySummaryInfo = params =>
  window.majesty.httpUtil.postAction(ycCsApi.equipment.foreignContract.body.summary, params)

export const addBody = params =>
  window.majesty.httpUtil.postAction(ycCsApi.equipment.foreignContract.body.insert, params)

export const updateBody = (id, params) =>
  window.majesty.httpUtil.putAction(ycCsApi.equipment.foreignContract.body.update + '/' + id, params)

export const deleteBody = ids =>
  window.majesty.httpUtil.deleteAction(ycCsApi.equipment.foreignContract.body.delete + '/' + ids)
