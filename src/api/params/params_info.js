import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";



// 仓库列表
export const insertStorehouse = (params) =>window.majesty.httpUtil.postAction(ycCsApi.params.storehouse.insert, params)
export const updateStorehouse = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.params.storehouse.update}/${sid}`, params)
export const deleteStorehouse = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.storehouse.delete}/${sids}`)
export const getSnoStorehouse = () => window.majesty.httpUtil.postAction(ycCsApi.params.storehouse.getSno)


// 商品类别列表
export const insertProductType = (params) =>window.majesty.httpUtil.postAction(ycCsApi.params.productType.insert, params)
export const updateProductType = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.params.productType.update}/${sid}`, params)
export const deleteProductType = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.productType.delete}/${sids}`)
export const getSnoProductType = () => window.majesty.httpUtil.postAction(ycCsApi.params.productType.getSno)


// 包装信息列表
export const insertPackageInfo = (params) =>window.majesty.httpUtil.postAction(ycCsApi.params.packageInfo.insert, params)
export const updatePackageInfo = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.params.packageInfo.update}/${sid}`, params)
export const deletePackageInfo = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.packageInfo.delete}/${sids}`)
export const getSnoPackageInfo = () => window.majesty.httpUtil.postAction(ycCsApi.params.packageInfo.getSno)


// 包装信息列表
export const insertCostType = (params) =>window.majesty.httpUtil.postAction(ycCsApi.params.costType.insert, params)
export const updateCostType = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.params.costType.update}/${sid}`, params)
export const deleteCostType = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.costType.delete}/${sids}`)
export const getSnoCostType = () => window.majesty.httpUtil.postAction(ycCsApi.params.costType.getSno)


// 汇率上浮表列表
export const insertRateTable= (params) =>window.majesty.httpUtil.postAction(ycCsApi.params.rateTable.insert, params)
export const updateRateTable= (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.params.rateTable.update}/${sid}`, params)
export const deleteRateTable= (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.rateTable.delete}/${sids}`)
export const getSnoRateTable= () => window.majesty.httpUtil.postAction(ycCsApi.params.rateTable.getSno)
export const getCurrList= (param) => window.majesty.httpUtil.postAction(ycCsApi.baseInfoCustomerParams.list,param)

//汇率
export const insertEnterpriseRateTable= (params) =>window.majesty.httpUtil.postAction(ycCsApi.params.enterpriseRate.insert, params)
export const updateEnterpriseRateTable= (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.params.enterpriseRate.update}/${sid}`, params)
export const deleteEnterpriseRateTable= (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.enterpriseRate.delete}/${sids}`)
export const getSnoEnterpriseRateTable= () => window.majesty.httpUtil.postAction(ycCsApi.params.enterpriseRate.getSno)

// 价格条款
export const getSnoPriceTerms = () => window.majesty.httpUtil.getAction(ycCsApi.params.priceTerms.getSno)
export const insertPriceTerms = (params) => window.majesty.httpUtil.postAction(ycCsApi.params.priceTerms.insert, params)
export const updatePriceTerms = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.params.priceTerms.update}/${sid}`, params)
export const deletePriceTerms = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.priceTerms.delete}/${sids}`)

// 城市
export const getSnoCity = () => window.majesty.httpUtil.getAction(ycCsApi.params.city.getSno)
export const insertCity = (params) => window.majesty.httpUtil.postAction(ycCsApi.params.city.insert, params)
export const updateCity = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.params.city.update}/${sid}`, params)
export const deleteCity = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.city.delete}/${sids}`)

// 划款参数
// export const getSnoPriceTerms = () => window.majesty.httpUtil.getAction(ycCsApi.params.priceTerms.getSno)
export const insertTransCode = (params) => window.majesty.httpUtil.postAction(ycCsApi.params.transCode.insert, params)
export const updateTransCode = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.params.transCode.update}/${sid}`, params)
export const deleteTransCode = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.transCode.delete}/${sids}`)
export const copyTransCode = (params) => window.majesty.httpUtil.postAction(ycCsApi.params.transCode.copy, params)



//箱型
export const insertBoxType= (params) =>window.majesty.httpUtil.postAction(ycCsApi.params.boxType.insert, params)
export const updateBoxType= (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.params.boxType.update}/${sid}`, params)
export const deleteBoxType= (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.boxType.delete}/${sids}`)
export const getSnoBoxType= () => window.majesty.httpUtil.postAction(ycCsApi.params.boxType.getSno)
export const getBoxTypeMap = () => window.majesty.httpUtil.postAction(ycCsApi.params.boxType.getBoxTypeMap)

//保险类别
export const insertInsuranceType= (params) =>window.majesty.httpUtil.postAction(ycCsApi.params.insuranceType.insert, params)
export const updateInsuranceType= (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.params.insuranceType.update}/${sid}`, params)
export const deleteInsuranceType= (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.params.insuranceType.delete}/${sids}`)
export const getSnoInsuranceType= () => window.majesty.httpUtil.postAction(ycCsApi.params.insuranceType.getSno)
export const getInsuranceTypeMap = () => window.majesty.httpUtil.postAction(ycCsApi.params.insuranceType.getBoxTypeMap)
