<template>
  <ant-select popupClassName="cs-select-dropdown"  id="cs-select" size="small" v-bind="$attrs" v-on="$attrs" @search="handleKeyDown" style="width: 100%" :default-active-first-option="activeFirstChecked">
    <!-- 自动渲染选项


      <cs-select
          :options="下拉选项"
          :combine-display="true"
          option-filter-prop="label"
          option-label-prop="key"
          allow-clear
          show-search
          v-model:value="searchParam.configId"
          id="configId"
        />
      PCode传入的是对象
      <cs-select
        :pCode="true"
        :options="pCode.CONTAINER_MODEL"
        :combine-display="true"
        allow-clear
        show-search
        v-model:value="formData.containerType"
        id="containerType"
      />
    -->
    <template v-if="pCode && pCode === true">
      <a-select-option
        class="cs-select-dropdown"
        v-for="(key,value) in options"
        :key="value +' '+ key"
        :value="value"
        :label="key + ' ' + value"
      >
        {{value }} {{key}}
      </a-select-option>
    </template>
    <template v-else-if="options && options.length > 0">
      <a-select-option
        class="cs-select-dropdown"
        v-for="item in options"
        :key="getOptionKey(item)"
        :value="getOptionValue(item)"
        :label="getOptionLabel(item)"
      >
        {{ getOptionDisplay(item) }}
      </a-select-option>
    </template>
    <!-- 使用 $slots.default 插槽，以保留原有 Select 的子组件 -->
    <slot v-else></slot>
  </ant-select>
</template>

<script>
import {ref, onMounted, nextTick } from 'vue';
import { Select, SelectOption } from 'ant-design-vue';

export default {
  name: 'CsSelect',
  // 在组件中注册 Ant Design Vue 的 Select 组件
  components: {
    'ant-select': Select,
    'a-select-option': SelectOption
  },
  props: {
    pCode:{
      type: Boolean,
      default: false
    },
    // 选项数据数组
    options: {
      type: Array || Object,
      default: () => []
    },
    // 值字段名，默认为 'value'
    valueField: {
      type: String,
      default: 'value'
    },
    // 标签字段名，默认为 'label'
    labelField: {
      type: String,
      default: 'label'
    },
    // 键字段名，用于key属性，默认为 'value'
    keyField: {
      type: String,
      default: 'value'
    },
    // 显示字段名，用于显示文本，默认为 'label'
    displayField: {
      type: String,
      default: 'label'
    },
    // 是否组合显示值和标签，默认false
    combineDisplay: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const activeFirstChecked = ref(false)
    function handleKeyDown(val) {
      if (val) {
        activeFirstChecked.value = true
      } else {
        activeFirstChecked.value = false
      }
    }

    /**设置禁用的下拉框样式，允许其内容可选择复制 */
    function setDropDownBoxStyleByDisabled() {
      nextTick(() => {
        document.querySelectorAll('input[class="ant-select-selection-search-input"][disabled]').forEach(function(el) {
          // 获取当前元素的父元素
          var parent = el.parentNode;
          // 获取父元素的父元素
          var grandparent = parent.parentNode;

          if (grandparent.className === 'ant-select-selector' && el.disabled) {
            grandparent.style.pointerEvents ='none'
          }
        });
      })
    }

    onMounted(() => {
      setDropDownBoxStyleByDisabled()
    })

    // 获取选项的key值
    function getOptionKey(item) {
      if (typeof item === 'string' || typeof item === 'number') {
        return item
      }
      if (props.combineDisplay) {
          return item[props.keyField] + ' ' + item[props.labelField]
      }
      return item[props.labelField]

    }

    // 获取选项的value值
    function getOptionValue(item) {
      if (typeof item === 'string' || typeof item === 'number') {
        return item
      }
      return item[props.valueField]
    }

    // 获取选项的label值
    function getOptionLabel(item) {
      if (typeof item === 'string' || typeof item === 'number') {
        return item
      }
      if (props.combineDisplay) {
        return item[props.valueField] + ' ' + item[props.labelField]
      }
      return item[props.labelField]
    }

    // 获取选项的显示文本
    function getOptionDisplay(item) {
      if (typeof item === 'string' || typeof item === 'number') {
        return item
      }


      // 如果传入的格式是 "1":"测试",这样的数据，就不能直接使用displayField，需要单独处理
      if (props.combineDisplay) {
        return item[props.valueField] + ' ' + item[props.labelField]
      }
      return item[props.displayField]
    }

    return {
      activeFirstChecked,
      handleKeyDown,
      getOptionKey,
      getOptionValue,
      getOptionLabel,
      getOptionDisplay
    }
  }
}
</script>

<style lang="less" scoped>

</style>


